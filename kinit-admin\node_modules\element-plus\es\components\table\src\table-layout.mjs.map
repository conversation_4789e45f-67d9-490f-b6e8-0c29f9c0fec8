{"version": 3, "file": "table-layout.mjs", "sources": ["../../../../../../packages/components/table/src/table-layout.ts"], "sourcesContent": ["// @ts-nocheck\nimport { isRef, nextTick, ref } from 'vue'\nimport { hasOwn, isClient } from '@element-plus/utils'\nimport { parseHeight } from './util'\nimport type { Ref } from 'vue'\n\nimport type { TableColumnCtx } from './table-column/defaults'\nimport type { TableHeader } from './table-header'\nimport type { Table } from './table/defaults'\nimport type { Store } from './store'\nclass TableLayout<T> {\n  observers: TableHeader[]\n  table: Table<T>\n  store: Store<T>\n  columns: TableColumnCtx<T>[]\n  fit: boolean\n  showHeader: boolean\n\n  height: Ref<null | number>\n  scrollX: Ref<boolean>\n  scrollY: Ref<boolean>\n  bodyWidth: Ref<null | number>\n  fixedWidth: Ref<null | number>\n  rightFixedWidth: Ref<null | number>\n  tableHeight: Ref<null | number>\n  headerHeight: Ref<null | number> // Table Header Height\n  appendHeight: Ref<null | number> // Append Slot Height\n  footerHeight: Ref<null | number> // Table Footer Height\n  gutterWidth: number\n  constructor(options: Record<string, any>) {\n    this.observers = []\n    this.table = null\n    this.store = null\n    this.columns = []\n    this.fit = true\n    this.showHeader = true\n    this.height = ref(null)\n    this.scrollX = ref(false)\n    this.scrollY = ref(false)\n    this.bodyWidth = ref(null)\n    this.fixedWidth = ref(null)\n    this.rightFixedWidth = ref(null)\n    this.gutterWidth = 0\n    for (const name in options) {\n      if (hasOwn(options, name)) {\n        if (isRef(this[name])) {\n          this[name as string].value = options[name]\n        } else {\n          this[name as string] = options[name]\n        }\n      }\n    }\n    if (!this.table) {\n      throw new Error('Table is required for Table Layout')\n    }\n    if (!this.store) {\n      throw new Error('Store is required for Table Layout')\n    }\n  }\n\n  updateScrollY() {\n    const height = this.height.value\n    /**\n     * When the height is not initialized, it is null.\n     * After the table is initialized, when the height is not configured, the height is 0.\n     */\n    if (height === null) return false\n    const scrollBarRef = this.table.refs.scrollBarRef\n    if (this.table.vnode.el && scrollBarRef?.wrapRef) {\n      let scrollY = true\n      const prevScrollY = this.scrollY.value\n      scrollY =\n        scrollBarRef.wrapRef.scrollHeight > scrollBarRef.wrapRef.clientHeight\n      this.scrollY.value = scrollY\n      return prevScrollY !== scrollY\n    }\n    return false\n  }\n\n  setHeight(value: string | number, prop = 'height') {\n    if (!isClient) return\n    const el = this.table.vnode.el\n    value = parseHeight(value)\n    this.height.value = Number(value)\n\n    if (!el && (value || value === 0))\n      return nextTick(() => this.setHeight(value, prop))\n\n    if (typeof value === 'number') {\n      el.style[prop] = `${value}px`\n      this.updateElsHeight()\n    } else if (typeof value === 'string') {\n      el.style[prop] = value\n      this.updateElsHeight()\n    }\n  }\n\n  setMaxHeight(value: string | number) {\n    this.setHeight(value, 'max-height')\n  }\n\n  getFlattenColumns(): TableColumnCtx<T>[] {\n    const flattenColumns = []\n    const columns = this.table.store.states.columns.value\n    columns.forEach((column) => {\n      if (column.isColumnGroup) {\n        // eslint-disable-next-line prefer-spread\n        flattenColumns.push.apply(flattenColumns, column.columns)\n      } else {\n        flattenColumns.push(column)\n      }\n    })\n\n    return flattenColumns\n  }\n\n  updateElsHeight() {\n    this.updateScrollY()\n    this.notifyObservers('scrollable')\n  }\n\n  headerDisplayNone(elm: HTMLElement) {\n    if (!elm) return true\n    let headerChild = elm\n    while (headerChild.tagName !== 'DIV') {\n      if (getComputedStyle(headerChild).display === 'none') {\n        return true\n      }\n      headerChild = headerChild.parentElement\n    }\n    return false\n  }\n\n  updateColumnsWidth() {\n    if (!isClient) return\n    const fit = this.fit\n    const bodyWidth = this.table.vnode.el.clientWidth\n    let bodyMinWidth = 0\n\n    const flattenColumns = this.getFlattenColumns()\n    const flexColumns = flattenColumns.filter(\n      (column) => typeof column.width !== 'number'\n    )\n    flattenColumns.forEach((column) => {\n      // Clean those columns whose width changed from flex to unflex\n      if (typeof column.width === 'number' && column.realWidth)\n        column.realWidth = null\n    })\n    if (flexColumns.length > 0 && fit) {\n      flattenColumns.forEach((column) => {\n        bodyMinWidth += Number(column.width || column.minWidth || 80)\n      })\n      if (bodyMinWidth <= bodyWidth) {\n        // DON'T HAVE SCROLL BAR\n        this.scrollX.value = false\n\n        const totalFlexWidth = bodyWidth - bodyMinWidth\n\n        if (flexColumns.length === 1) {\n          flexColumns[0].realWidth =\n            Number(flexColumns[0].minWidth || 80) + totalFlexWidth\n        } else {\n          const allColumnsWidth = flexColumns.reduce(\n            (prev, column) => prev + Number(column.minWidth || 80),\n            0\n          )\n          const flexWidthPerPixel = totalFlexWidth / allColumnsWidth\n          let noneFirstWidth = 0\n\n          flexColumns.forEach((column, index) => {\n            if (index === 0) return\n            const flexWidth = Math.floor(\n              Number(column.minWidth || 80) * flexWidthPerPixel\n            )\n            noneFirstWidth += flexWidth\n            column.realWidth = Number(column.minWidth || 80) + flexWidth\n          })\n\n          flexColumns[0].realWidth =\n            Number(flexColumns[0].minWidth || 80) +\n            totalFlexWidth -\n            noneFirstWidth\n        }\n      } else {\n        // HAVE HORIZONTAL SCROLL BAR\n        this.scrollX.value = true\n        flexColumns.forEach((column) => {\n          column.realWidth = Number(column.minWidth)\n        })\n      }\n\n      this.bodyWidth.value = Math.max(bodyMinWidth, bodyWidth)\n      this.table.state.resizeState.value.width = this.bodyWidth.value\n    } else {\n      flattenColumns.forEach((column) => {\n        if (!column.width && !column.minWidth) {\n          column.realWidth = 80\n        } else {\n          column.realWidth = Number(column.width || column.minWidth)\n        }\n        bodyMinWidth += column.realWidth\n      })\n      this.scrollX.value = bodyMinWidth > bodyWidth\n\n      this.bodyWidth.value = bodyMinWidth\n    }\n\n    const fixedColumns = this.store.states.fixedColumns.value\n\n    if (fixedColumns.length > 0) {\n      let fixedWidth = 0\n      fixedColumns.forEach((column) => {\n        fixedWidth += Number(column.realWidth || column.width)\n      })\n\n      this.fixedWidth.value = fixedWidth\n    }\n\n    const rightFixedColumns = this.store.states.rightFixedColumns.value\n    if (rightFixedColumns.length > 0) {\n      let rightFixedWidth = 0\n      rightFixedColumns.forEach((column) => {\n        rightFixedWidth += Number(column.realWidth || column.width)\n      })\n\n      this.rightFixedWidth.value = rightFixedWidth\n    }\n    this.notifyObservers('columns')\n  }\n\n  addObserver(observer: TableHeader) {\n    this.observers.push(observer)\n  }\n\n  removeObserver(observer: TableHeader) {\n    const index = this.observers.indexOf(observer)\n    if (index !== -1) {\n      this.observers.splice(index, 1)\n    }\n  }\n\n  notifyObservers(event: string) {\n    const observers = this.observers\n    observers.forEach((observer) => {\n      switch (event) {\n        case 'columns':\n          observer.state?.onColumnsChange(this)\n          break\n        case 'scrollable':\n          observer.state?.onScrollableChange(this)\n          break\n        default:\n          throw new Error(`Table Layout don't have event ${event}.`)\n      }\n    })\n  }\n}\n\nexport default TableLayout\n"], "names": [], "mappings": ";;;;;;AAGA,MAAM,WAAW,CAAC;AAClB,EAAE,WAAW,CAAC,OAAO,EAAE;AACvB,IAAI,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;AACxB,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACtB,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;AACtB,IAAI,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;AACtB,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;AACpB,IAAI,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;AAC3B,IAAI,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AAC5B,IAAI,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,OAAO,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AAC9B,IAAI,IAAI,CAAC,SAAS,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AAC/B,IAAI,IAAI,CAAC,UAAU,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AAChC,IAAI,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;AACrC,IAAI,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;AACzB,IAAI,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE;AAChC,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE;AACjC,QAAQ,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;AAC/B,UAAU,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AAC3C,SAAS,MAAM;AACf,UAAU,IAAI,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;AACrC,SAAS;AACT,OAAO;AACP,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACrB,MAAM,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;AAC5D,KAAK;AACL,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;AACrB,MAAM,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAC;AAC5D,KAAK;AACL,GAAG;AACH,EAAE,aAAa,GAAG;AAClB,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;AACrC,IAAI,IAAI,MAAM,KAAK,IAAI;AACvB,MAAM,OAAO,KAAK,CAAC;AACnB,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC;AACtD,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,KAAK,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,YAAY,CAAC,OAAO,CAAC,EAAE;AACvF,MAAM,IAAI,OAAO,GAAG,IAAI,CAAC;AACzB,MAAM,MAAM,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC;AAC7C,MAAM,OAAO,GAAG,YAAY,CAAC,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC,OAAO,CAAC,YAAY,CAAC;AACtF,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC;AACnC,MAAM,OAAO,WAAW,KAAK,OAAO,CAAC;AACrC,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,SAAS,CAAC,KAAK,EAAE,IAAI,GAAG,QAAQ,EAAE;AACpC,IAAI,IAAI,CAAC,QAAQ;AACjB,MAAM,OAAO;AACb,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC;AACnC,IAAI,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC;AAC/B,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;AACtC,IAAI,IAAI,CAAC,EAAE,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,CAAC;AACrC,MAAM,OAAO,QAAQ,CAAC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC,CAAC;AACzD,IAAI,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AACnC,MAAM,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;AACpC,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;AAC7B,KAAK,MAAM,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;AAC1C,MAAM,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;AAC7B,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;AAC7B,KAAK;AACL,GAAG;AACH,EAAE,YAAY,CAAC,KAAK,EAAE;AACtB,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AACxC,GAAG;AACH,EAAE,iBAAiB,GAAG;AACtB,IAAI,MAAM,cAAc,GAAG,EAAE,CAAC;AAC9B,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,KAAK,CAAC;AAC1D,IAAI,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AAChC,MAAM,IAAI,MAAM,CAAC,aAAa,EAAE;AAChC,QAAQ,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;AAClE,OAAO,MAAM;AACb,QAAQ,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACpC,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,OAAO,cAAc,CAAC;AAC1B,GAAG;AACH,EAAE,eAAe,GAAG;AACpB,IAAI,IAAI,CAAC,aAAa,EAAE,CAAC;AACzB,IAAI,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC;AACvC,GAAG;AACH,EAAE,iBAAiB,CAAC,GAAG,EAAE;AACzB,IAAI,IAAI,CAAC,GAAG;AACZ,MAAM,OAAO,IAAI,CAAC;AAClB,IAAI,IAAI,WAAW,GAAG,GAAG,CAAC;AAC1B,IAAI,OAAO,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;AAC1C,MAAM,IAAI,gBAAgB,CAAC,WAAW,CAAC,CAAC,OAAO,KAAK,MAAM,EAAE;AAC5D,QAAQ,OAAO,IAAI,CAAC;AACpB,OAAO;AACP,MAAM,WAAW,GAAG,WAAW,CAAC,aAAa,CAAC;AAC9C,KAAK;AACL,IAAI,OAAO,KAAK,CAAC;AACjB,GAAG;AACH,EAAE,kBAAkB,GAAG;AACvB,IAAI,IAAI,CAAC,QAAQ;AACjB,MAAM,OAAO;AACb,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACzB,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC;AACtD,IAAI,IAAI,YAAY,GAAG,CAAC,CAAC;AACzB,IAAI,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;AACpD,IAAI,MAAM,WAAW,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,MAAM,KAAK,OAAO,MAAM,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC;AAC5F,IAAI,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACvC,MAAM,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,QAAQ,IAAI,MAAM,CAAC,SAAS;AAC9D,QAAQ,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;AAChC,KAAK,CAAC,CAAC;AACP,IAAI,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE;AACvC,MAAM,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACzC,QAAQ,YAAY,IAAI,MAAM,CAAC,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC,CAAC;AACtE,OAAO,CAAC,CAAC;AACT,MAAM,IAAI,YAAY,IAAI,SAAS,EAAE;AACrC,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC;AACnC,QAAQ,MAAM,cAAc,GAAG,SAAS,GAAG,YAAY,CAAC;AACxD,QAAQ,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;AACtC,UAAU,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,EAAE,CAAC,GAAG,cAAc,CAAC;AAC5F,SAAS,MAAM;AACf,UAAU,MAAM,eAAe,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,MAAM,KAAK,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AAChH,UAAU,MAAM,iBAAiB,GAAG,cAAc,GAAG,eAAe,CAAC;AACrE,UAAU,IAAI,cAAc,GAAG,CAAC,CAAC;AACjC,UAAU,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,KAAK,KAAK;AACjD,YAAY,IAAI,KAAK,KAAK,CAAC;AAC3B,cAAc,OAAO;AACrB,YAAY,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC,GAAG,iBAAiB,CAAC,CAAC;AAC5F,YAAY,cAAc,IAAI,SAAS,CAAC;AACxC,YAAY,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,IAAI,EAAE,CAAC,GAAG,SAAS,CAAC;AACzE,WAAW,CAAC,CAAC;AACb,UAAU,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,EAAE,CAAC,GAAG,cAAc,GAAG,cAAc,CAAC;AAC7G,SAAS;AACT,OAAO,MAAM;AACb,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;AAClC,QAAQ,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACxC,UAAU,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;AACrD,SAAS,CAAC,CAAC;AACX,OAAO;AACP,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;AAC/D,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;AACtE,KAAK,MAAM;AACX,MAAM,cAAc,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACzC,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AAC/C,UAAU,MAAM,CAAC,SAAS,GAAG,EAAE,CAAC;AAChC,SAAS,MAAM;AACf,UAAU,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC;AACrE,SAAS;AACT,QAAQ,YAAY,IAAI,MAAM,CAAC,SAAS,CAAC;AACzC,OAAO,CAAC,CAAC;AACT,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,YAAY,GAAG,SAAS,CAAC;AACpD,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,YAAY,CAAC;AAC1C,KAAK;AACL,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC;AAC9D,IAAI,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE;AACjC,MAAM,IAAI,UAAU,GAAG,CAAC,CAAC;AACzB,MAAM,YAAY,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACvC,QAAQ,UAAU,IAAI,MAAM,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;AAC/D,OAAO,CAAC,CAAC;AACT,MAAM,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC;AACzC,KAAK;AACL,IAAI,MAAM,iBAAiB,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,iBAAiB,CAAC,KAAK,CAAC;AACxE,IAAI,IAAI,iBAAiB,CAAC,MAAM,GAAG,CAAC,EAAE;AACtC,MAAM,IAAI,eAAe,GAAG,CAAC,CAAC;AAC9B,MAAM,iBAAiB,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AAC5C,QAAQ,eAAe,IAAI,MAAM,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,KAAK,CAAC,CAAC;AACpE,OAAO,CAAC,CAAC;AACT,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG,eAAe,CAAC;AACnD,KAAK;AACL,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC;AACpC,GAAG;AACH,EAAE,WAAW,CAAC,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;AAClC,GAAG;AACH,EAAE,cAAc,CAAC,QAAQ,EAAE;AAC3B,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;AACnD,IAAI,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;AACtB,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACtC,KAAK;AACL,GAAG;AACH,EAAE,eAAe,CAAC,KAAK,EAAE;AACzB,IAAI,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;AACrC,IAAI,SAAS,CAAC,OAAO,CAAC,CAAC,QAAQ,KAAK;AACpC,MAAM,IAAI,EAAE,EAAE,EAAE,CAAC;AACjB,MAAM,QAAQ,KAAK;AACnB,QAAQ,KAAK,SAAS;AACtB,UAAU,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;AAC5E,UAAU,MAAM;AAChB,QAAQ,KAAK,YAAY;AACzB,UAAU,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAC/E,UAAU,MAAM;AAChB,QAAQ;AACR,UAAU,MAAM,IAAI,KAAK,CAAC,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACrE,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG;AACH;;;;"}