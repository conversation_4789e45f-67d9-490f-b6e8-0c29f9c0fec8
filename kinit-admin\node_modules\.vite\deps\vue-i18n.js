import {
  require_vue
} from "./chunk-MMZUZRJS.js";
import "./chunk-NS63M6FW.js";
import {
  __commonJS,
  __esm,
  __export,
  __toCommonJS
} from "./chunk-GFT2G5UO.js";

// node_modules/vue-i18n/node_modules/@intlify/shared/dist/shared.esm-browser.js
var shared_esm_browser_exports = {};
__export(shared_esm_browser_exports, {
  assign: () => assign,
  createEmitter: () => createEmitter,
  deepCopy: () => deepCopy,
  escapeHtml: () => escapeHtml,
  format: () => format,
  friendlyJSONstringify: () => friendlyJSONstringify,
  generateCodeFrame: () => generateCodeFrame,
  generateFormatCacheKey: () => generateFormatCacheKey,
  getGlobalThis: () => getGlobalThis,
  hasOwn: () => hasOwn,
  inBrowser: () => inBrowser,
  incrementer: () => incrementer,
  isArray: () => isArray,
  isBoolean: () => isBoolean,
  isDate: () => isDate,
  isEmptyObject: () => isEmptyObject,
  isFunction: () => isFunction,
  isNumber: () => isNumber,
  isObject: () => isObject,
  isPlainObject: () => isPlainObject,
  isPromise: () => isPromise,
  isRegExp: () => isRegExp,
  isString: () => isString,
  isSymbol: () => isSymbol,
  join: () => join,
  makeSymbol: () => makeSymbol,
  mark: () => mark,
  measure: () => measure,
  objectToString: () => objectToString,
  toDisplayString: () => toDisplayString,
  toTypeString: () => toTypeString,
  warn: () => warn,
  warnOnce: () => warnOnce
});
function format(message, ...args) {
  if (args.length === 1 && isObject(args[0])) {
    args = args[0];
  }
  if (!args || !args.hasOwnProperty) {
    args = {};
  }
  return message.replace(RE_ARGS, (match, identifier) => {
    return args.hasOwnProperty(identifier) ? args[identifier] : "";
  });
}
function escapeHtml(rawText) {
  return rawText.replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&apos;");
}
function hasOwn(obj, key) {
  return hasOwnProperty.call(obj, key);
}
function join(items, separator = "") {
  return items.reduce((str, item, index) => index === 0 ? str + item : str + separator + item, "");
}
function generateCodeFrame(source, start = 0, end = source.length) {
  const lines = source.split(/\r?\n/);
  let count = 0;
  const res = [];
  for (let i = 0; i < lines.length; i++) {
    count += lines[i].length + 1;
    if (count >= start) {
      for (let j = i - RANGE; j <= i + RANGE || end > count; j++) {
        if (j < 0 || j >= lines.length)
          continue;
        const line = j + 1;
        res.push(`${line}${" ".repeat(3 - String(line).length)}|  ${lines[j]}`);
        const lineLength = lines[j].length;
        if (j === i) {
          const pad = start - (count - lineLength) + 1;
          const length = Math.max(1, end > count ? lineLength - pad : end - start);
          res.push(`   |  ` + " ".repeat(pad) + "^".repeat(length));
        } else if (j > i) {
          if (end > count) {
            const length = Math.max(Math.min(end - count, lineLength), 1);
            res.push(`   |  ` + "^".repeat(length));
          }
          count += lineLength + 1;
        }
      }
      break;
    }
  }
  return res.join("\n");
}
function incrementer(code2) {
  let current = code2;
  return () => ++current;
}
function warn(msg, err) {
  if (typeof console !== "undefined") {
    console.warn(`[intlify] ` + msg);
    if (err) {
      console.warn(err.stack);
    }
  }
}
function warnOnce(msg) {
  if (!hasWarned[msg]) {
    hasWarned[msg] = true;
    warn(msg);
  }
}
function createEmitter() {
  const events = /* @__PURE__ */ new Map();
  const emitter = {
    events,
    on(event, handler) {
      const handlers = events.get(event);
      const added = handlers && handlers.push(handler);
      if (!added) {
        events.set(event, [handler]);
      }
    },
    off(event, handler) {
      const handlers = events.get(event);
      if (handlers) {
        handlers.splice(handlers.indexOf(handler) >>> 0, 1);
      }
    },
    emit(event, payload) {
      (events.get(event) || []).slice().map((handler) => handler(payload));
      (events.get("*") || []).slice().map((handler) => handler(event, payload));
    }
  };
  return emitter;
}
function deepCopy(src, des) {
  if (isNotObjectOrIsArray(src) || isNotObjectOrIsArray(des)) {
    throw new Error("Invalid value");
  }
  const stack = [{ src, des }];
  while (stack.length) {
    const { src: src2, des: des2 } = stack.pop();
    Object.keys(src2).forEach((key) => {
      if (isNotObjectOrIsArray(src2[key]) || isNotObjectOrIsArray(des2[key])) {
        des2[key] = src2[key];
      } else {
        stack.push({ src: src2[key], des: des2[key] });
      }
    });
  }
}
var inBrowser, mark, measure, RE_ARGS, makeSymbol, generateFormatCacheKey, friendlyJSONstringify, isNumber, isDate, isRegExp, isEmptyObject, assign, _globalThis, getGlobalThis, hasOwnProperty, isArray, isFunction, isString, isBoolean, isSymbol, isObject, isPromise, objectToString, toTypeString, isPlainObject, toDisplayString, RANGE, hasWarned, isNotObjectOrIsArray;
var init_shared_esm_browser = __esm({
  "node_modules/vue-i18n/node_modules/@intlify/shared/dist/shared.esm-browser.js"() {
    inBrowser = typeof window !== "undefined";
    {
      const perf = inBrowser && window.performance;
      if (perf && perf.mark && perf.measure && perf.clearMarks && // @ts-ignore browser compat
      perf.clearMeasures) {
        mark = (tag) => {
          perf.mark(tag);
        };
        measure = (name, startTag, endTag) => {
          perf.measure(name, startTag, endTag);
          perf.clearMarks(startTag);
          perf.clearMarks(endTag);
        };
      }
    }
    RE_ARGS = /\{([0-9a-zA-Z]+)\}/g;
    makeSymbol = (name, shareable = false) => !shareable ? Symbol(name) : Symbol.for(name);
    generateFormatCacheKey = (locale, key, source) => friendlyJSONstringify({ l: locale, k: key, s: source });
    friendlyJSONstringify = (json) => JSON.stringify(json).replace(/\u2028/g, "\\u2028").replace(/\u2029/g, "\\u2029").replace(/\u0027/g, "\\u0027");
    isNumber = (val) => typeof val === "number" && isFinite(val);
    isDate = (val) => toTypeString(val) === "[object Date]";
    isRegExp = (val) => toTypeString(val) === "[object RegExp]";
    isEmptyObject = (val) => isPlainObject(val) && Object.keys(val).length === 0;
    assign = Object.assign;
    getGlobalThis = () => {
      return _globalThis || (_globalThis = typeof globalThis !== "undefined" ? globalThis : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : typeof global !== "undefined" ? global : {});
    };
    hasOwnProperty = Object.prototype.hasOwnProperty;
    isArray = Array.isArray;
    isFunction = (val) => typeof val === "function";
    isString = (val) => typeof val === "string";
    isBoolean = (val) => typeof val === "boolean";
    isSymbol = (val) => typeof val === "symbol";
    isObject = (val) => val !== null && typeof val === "object";
    isPromise = (val) => {
      return isObject(val) && isFunction(val.then) && isFunction(val.catch);
    };
    objectToString = Object.prototype.toString;
    toTypeString = (value) => objectToString.call(value);
    isPlainObject = (val) => {
      if (!isObject(val))
        return false;
      const proto = Object.getPrototypeOf(val);
      return proto === null || proto.constructor === Object;
    };
    toDisplayString = (val) => {
      return val == null ? "" : isArray(val) || isPlainObject(val) && val.toString === objectToString ? JSON.stringify(val, null, 2) : String(val);
    };
    RANGE = 2;
    hasWarned = {};
    isNotObjectOrIsArray = (val) => !isObject(val) || isArray(val);
  }
});

// node_modules/@intlify/core-base/dist/core-base.esm-browser.js
var core_base_esm_browser_exports = {};
__export(core_base_esm_browser_exports, {
  CompileErrorCodes: () => CompileErrorCodes,
  CoreErrorCodes: () => CoreErrorCodes,
  CoreWarnCodes: () => CoreWarnCodes,
  DATETIME_FORMAT_OPTIONS_KEYS: () => DATETIME_FORMAT_OPTIONS_KEYS,
  DEFAULT_LOCALE: () => DEFAULT_LOCALE,
  DEFAULT_MESSAGE_DATA_TYPE: () => DEFAULT_MESSAGE_DATA_TYPE,
  MISSING_RESOLVE_VALUE: () => MISSING_RESOLVE_VALUE,
  NOT_REOSLVED: () => NOT_REOSLVED,
  NUMBER_FORMAT_OPTIONS_KEYS: () => NUMBER_FORMAT_OPTIONS_KEYS,
  VERSION: () => VERSION,
  clearCompileCache: () => clearCompileCache,
  clearDateTimeFormat: () => clearDateTimeFormat,
  clearNumberFormat: () => clearNumberFormat,
  compile: () => compile,
  compileToFunction: () => compileToFunction,
  createCompileError: () => createCompileError,
  createCoreContext: () => createCoreContext,
  createCoreError: () => createCoreError,
  createMessageContext: () => createMessageContext,
  datetime: () => datetime,
  fallbackWithLocaleChain: () => fallbackWithLocaleChain,
  fallbackWithSimple: () => fallbackWithSimple,
  getAdditionalMeta: () => getAdditionalMeta,
  getDevToolsHook: () => getDevToolsHook,
  getFallbackContext: () => getFallbackContext,
  getLocale: () => getLocale,
  getWarnMessage: () => getWarnMessage,
  handleMissing: () => handleMissing,
  initI18nDevTools: () => initI18nDevTools,
  isMessageAST: () => isMessageAST,
  isMessageFunction: () => isMessageFunction,
  isTranslateFallbackWarn: () => isTranslateFallbackWarn,
  isTranslateMissingWarn: () => isTranslateMissingWarn,
  number: () => number,
  parse: () => parse,
  parseDateTimeArgs: () => parseDateTimeArgs,
  parseNumberArgs: () => parseNumberArgs,
  parseTranslateArgs: () => parseTranslateArgs,
  registerLocaleFallbacker: () => registerLocaleFallbacker,
  registerMessageCompiler: () => registerMessageCompiler,
  registerMessageResolver: () => registerMessageResolver,
  resolveLocale: () => resolveLocale,
  resolveValue: () => resolveValue,
  resolveWithKeyValue: () => resolveWithKeyValue,
  setAdditionalMeta: () => setAdditionalMeta,
  setDevToolsHook: () => setDevToolsHook,
  setFallbackContext: () => setFallbackContext,
  translate: () => translate,
  translateDevTools: () => translateDevTools,
  updateFallbackLocale: () => updateFallbackLocale
});
function format$1(message, ...args) {
  if (args.length === 1 && isObject2(args[0])) {
    args = args[0];
  }
  if (!args || !args.hasOwnProperty) {
    args = {};
  }
  return message.replace(RE_ARGS2, (match, identifier) => {
    return args.hasOwnProperty(identifier) ? args[identifier] : "";
  });
}
function escapeHtml2(rawText) {
  return rawText.replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&apos;");
}
function join2(items, separator = "") {
  return items.reduce((str, item, index) => index === 0 ? str + item : str + separator + item, "");
}
function generateCodeFrame2(source, start = 0, end = source.length) {
  const lines = source.split(/\r?\n/);
  let count = 0;
  const res = [];
  for (let i = 0; i < lines.length; i++) {
    count += lines[i].length + 1;
    if (count >= start) {
      for (let j = i - RANGE2; j <= i + RANGE2 || end > count; j++) {
        if (j < 0 || j >= lines.length)
          continue;
        const line = j + 1;
        res.push(`${line}${" ".repeat(3 - String(line).length)}|  ${lines[j]}`);
        const lineLength = lines[j].length;
        if (j === i) {
          const pad = start - (count - lineLength) + 1;
          const length = Math.max(1, end > count ? lineLength - pad : end - start);
          res.push(`   |  ` + " ".repeat(pad) + "^".repeat(length));
        } else if (j > i) {
          if (end > count) {
            const length = Math.max(Math.min(end - count, lineLength), 1);
            res.push(`   |  ` + "^".repeat(length));
          }
          count += lineLength + 1;
        }
      }
      break;
    }
  }
  return res.join("\n");
}
function incrementer2(code2) {
  let current = code2;
  return () => ++current;
}
function warn2(msg, err) {
  if (typeof console !== "undefined") {
    console.warn(`[intlify] ` + msg);
    if (err) {
      console.warn(err.stack);
    }
  }
}
function warnOnce2(msg) {
  if (!hasWarned2[msg]) {
    hasWarned2[msg] = true;
    warn2(msg);
  }
}
function createPosition(line, column, offset) {
  return { line, column, offset };
}
function createLocation(start, end, source) {
  const loc = { start, end };
  if (source != null) {
    loc.source = source;
  }
  return loc;
}
function createCompileError(code2, loc, options = {}) {
  const { domain, messages, args } = options;
  const msg = format$1((messages || errorMessages$1)[code2] || "", ...args || []);
  const error = new SyntaxError(String(msg));
  error.code = code2;
  if (loc) {
    error.location = loc;
  }
  error.domain = domain;
  return error;
}
function defaultOnError(error) {
  throw error;
}
function createScanner(str) {
  const _buf = str;
  let _index = 0;
  let _line = 1;
  let _column = 1;
  let _peekOffset = 0;
  const isCRLF = (index2) => _buf[index2] === CHAR_CR && _buf[index2 + 1] === CHAR_LF;
  const isLF = (index2) => _buf[index2] === CHAR_LF;
  const isPS = (index2) => _buf[index2] === CHAR_PS;
  const isLS = (index2) => _buf[index2] === CHAR_LS;
  const isLineEnd = (index2) => isCRLF(index2) || isLF(index2) || isPS(index2) || isLS(index2);
  const index = () => _index;
  const line = () => _line;
  const column = () => _column;
  const peekOffset = () => _peekOffset;
  const charAt = (offset) => isCRLF(offset) || isPS(offset) || isLS(offset) ? CHAR_LF : _buf[offset];
  const currentChar = () => charAt(_index);
  const currentPeek = () => charAt(_index + _peekOffset);
  function next() {
    _peekOffset = 0;
    if (isLineEnd(_index)) {
      _line++;
      _column = 0;
    }
    if (isCRLF(_index)) {
      _index++;
    }
    _index++;
    _column++;
    return _buf[_index];
  }
  function peek() {
    if (isCRLF(_index + _peekOffset)) {
      _peekOffset++;
    }
    _peekOffset++;
    return _buf[_index + _peekOffset];
  }
  function reset() {
    _index = 0;
    _line = 1;
    _column = 1;
    _peekOffset = 0;
  }
  function resetPeek(offset = 0) {
    _peekOffset = offset;
  }
  function skipToPeek() {
    const target = _index + _peekOffset;
    while (target !== _index) {
      next();
    }
    _peekOffset = 0;
  }
  return {
    index,
    line,
    column,
    peekOffset,
    charAt,
    currentChar,
    currentPeek,
    next,
    peek,
    reset,
    resetPeek,
    skipToPeek
  };
}
function createTokenizer(source, options = {}) {
  const location = options.location !== false;
  const _scnr = createScanner(source);
  const currentOffset = () => _scnr.index();
  const currentPosition = () => createPosition(_scnr.line(), _scnr.column(), _scnr.index());
  const _initLoc = currentPosition();
  const _initOffset = currentOffset();
  const _context = {
    currentType: 14,
    offset: _initOffset,
    startLoc: _initLoc,
    endLoc: _initLoc,
    lastType: 14,
    lastOffset: _initOffset,
    lastStartLoc: _initLoc,
    lastEndLoc: _initLoc,
    braceNest: 0,
    inLinked: false,
    text: ""
  };
  const context = () => _context;
  const { onError } = options;
  function emitError(code2, pos, offset, ...args) {
    const ctx = context();
    pos.column += offset;
    pos.offset += offset;
    if (onError) {
      const loc = location ? createLocation(ctx.startLoc, pos) : null;
      const err = createCompileError(code2, loc, {
        domain: ERROR_DOMAIN$3,
        args
      });
      onError(err);
    }
  }
  function getToken(context2, type, value) {
    context2.endLoc = currentPosition();
    context2.currentType = type;
    const token = { type };
    if (location) {
      token.loc = createLocation(context2.startLoc, context2.endLoc);
    }
    if (value != null) {
      token.value = value;
    }
    return token;
  }
  const getEndToken = (context2) => getToken(
    context2,
    14
    /* TokenTypes.EOF */
  );
  function eat(scnr, ch) {
    if (scnr.currentChar() === ch) {
      scnr.next();
      return ch;
    } else {
      emitError(CompileErrorCodes.EXPECTED_TOKEN, currentPosition(), 0, ch);
      return "";
    }
  }
  function peekSpaces(scnr) {
    let buf = "";
    while (scnr.currentPeek() === CHAR_SP || scnr.currentPeek() === CHAR_LF) {
      buf += scnr.currentPeek();
      scnr.peek();
    }
    return buf;
  }
  function skipSpaces(scnr) {
    const buf = peekSpaces(scnr);
    scnr.skipToPeek();
    return buf;
  }
  function isIdentifierStart(ch) {
    if (ch === EOF) {
      return false;
    }
    const cc = ch.charCodeAt(0);
    return cc >= 97 && cc <= 122 || // a-z
    cc >= 65 && cc <= 90 || // A-Z
    cc === 95;
  }
  function isNumberStart(ch) {
    if (ch === EOF) {
      return false;
    }
    const cc = ch.charCodeAt(0);
    return cc >= 48 && cc <= 57;
  }
  function isNamedIdentifierStart(scnr, context2) {
    const { currentType } = context2;
    if (currentType !== 2) {
      return false;
    }
    peekSpaces(scnr);
    const ret = isIdentifierStart(scnr.currentPeek());
    scnr.resetPeek();
    return ret;
  }
  function isListIdentifierStart(scnr, context2) {
    const { currentType } = context2;
    if (currentType !== 2) {
      return false;
    }
    peekSpaces(scnr);
    const ch = scnr.currentPeek() === "-" ? scnr.peek() : scnr.currentPeek();
    const ret = isNumberStart(ch);
    scnr.resetPeek();
    return ret;
  }
  function isLiteralStart(scnr, context2) {
    const { currentType } = context2;
    if (currentType !== 2) {
      return false;
    }
    peekSpaces(scnr);
    const ret = scnr.currentPeek() === LITERAL_DELIMITER;
    scnr.resetPeek();
    return ret;
  }
  function isLinkedDotStart(scnr, context2) {
    const { currentType } = context2;
    if (currentType !== 8) {
      return false;
    }
    peekSpaces(scnr);
    const ret = scnr.currentPeek() === ".";
    scnr.resetPeek();
    return ret;
  }
  function isLinkedModifierStart(scnr, context2) {
    const { currentType } = context2;
    if (currentType !== 9) {
      return false;
    }
    peekSpaces(scnr);
    const ret = isIdentifierStart(scnr.currentPeek());
    scnr.resetPeek();
    return ret;
  }
  function isLinkedDelimiterStart(scnr, context2) {
    const { currentType } = context2;
    if (!(currentType === 8 || currentType === 12)) {
      return false;
    }
    peekSpaces(scnr);
    const ret = scnr.currentPeek() === ":";
    scnr.resetPeek();
    return ret;
  }
  function isLinkedReferStart(scnr, context2) {
    const { currentType } = context2;
    if (currentType !== 10) {
      return false;
    }
    const fn = () => {
      const ch = scnr.currentPeek();
      if (ch === "{") {
        return isIdentifierStart(scnr.peek());
      } else if (ch === "@" || ch === "%" || ch === "|" || ch === ":" || ch === "." || ch === CHAR_SP || !ch) {
        return false;
      } else if (ch === CHAR_LF) {
        scnr.peek();
        return fn();
      } else {
        return isIdentifierStart(ch);
      }
    };
    const ret = fn();
    scnr.resetPeek();
    return ret;
  }
  function isPluralStart(scnr) {
    peekSpaces(scnr);
    const ret = scnr.currentPeek() === "|";
    scnr.resetPeek();
    return ret;
  }
  function detectModuloStart(scnr) {
    const spaces = peekSpaces(scnr);
    const ret = scnr.currentPeek() === "%" && scnr.peek() === "{";
    scnr.resetPeek();
    return {
      isModulo: ret,
      hasSpace: spaces.length > 0
    };
  }
  function isTextStart(scnr, reset = true) {
    const fn = (hasSpace = false, prev = "", detectModulo = false) => {
      const ch = scnr.currentPeek();
      if (ch === "{") {
        return prev === "%" ? false : hasSpace;
      } else if (ch === "@" || !ch) {
        return prev === "%" ? true : hasSpace;
      } else if (ch === "%") {
        scnr.peek();
        return fn(hasSpace, "%", true);
      } else if (ch === "|") {
        return prev === "%" || detectModulo ? true : !(prev === CHAR_SP || prev === CHAR_LF);
      } else if (ch === CHAR_SP) {
        scnr.peek();
        return fn(true, CHAR_SP, detectModulo);
      } else if (ch === CHAR_LF) {
        scnr.peek();
        return fn(true, CHAR_LF, detectModulo);
      } else {
        return true;
      }
    };
    const ret = fn();
    reset && scnr.resetPeek();
    return ret;
  }
  function takeChar(scnr, fn) {
    const ch = scnr.currentChar();
    if (ch === EOF) {
      return EOF;
    }
    if (fn(ch)) {
      scnr.next();
      return ch;
    }
    return null;
  }
  function takeIdentifierChar(scnr) {
    const closure = (ch) => {
      const cc = ch.charCodeAt(0);
      return cc >= 97 && cc <= 122 || // a-z
      cc >= 65 && cc <= 90 || // A-Z
      cc >= 48 && cc <= 57 || // 0-9
      cc === 95 || // _
      cc === 36;
    };
    return takeChar(scnr, closure);
  }
  function takeDigit(scnr) {
    const closure = (ch) => {
      const cc = ch.charCodeAt(0);
      return cc >= 48 && cc <= 57;
    };
    return takeChar(scnr, closure);
  }
  function takeHexDigit(scnr) {
    const closure = (ch) => {
      const cc = ch.charCodeAt(0);
      return cc >= 48 && cc <= 57 || // 0-9
      cc >= 65 && cc <= 70 || // A-F
      cc >= 97 && cc <= 102;
    };
    return takeChar(scnr, closure);
  }
  function getDigits(scnr) {
    let ch = "";
    let num = "";
    while (ch = takeDigit(scnr)) {
      num += ch;
    }
    return num;
  }
  function readModulo(scnr) {
    skipSpaces(scnr);
    const ch = scnr.currentChar();
    if (ch !== "%") {
      emitError(CompileErrorCodes.EXPECTED_TOKEN, currentPosition(), 0, ch);
    }
    scnr.next();
    return "%";
  }
  function readText(scnr) {
    let buf = "";
    while (true) {
      const ch = scnr.currentChar();
      if (ch === "{" || ch === "}" || ch === "@" || ch === "|" || !ch) {
        break;
      } else if (ch === "%") {
        if (isTextStart(scnr)) {
          buf += ch;
          scnr.next();
        } else {
          break;
        }
      } else if (ch === CHAR_SP || ch === CHAR_LF) {
        if (isTextStart(scnr)) {
          buf += ch;
          scnr.next();
        } else if (isPluralStart(scnr)) {
          break;
        } else {
          buf += ch;
          scnr.next();
        }
      } else {
        buf += ch;
        scnr.next();
      }
    }
    return buf;
  }
  function readNamedIdentifier(scnr) {
    skipSpaces(scnr);
    let ch = "";
    let name = "";
    while (ch = takeIdentifierChar(scnr)) {
      name += ch;
    }
    if (scnr.currentChar() === EOF) {
      emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);
    }
    return name;
  }
  function readListIdentifier(scnr) {
    skipSpaces(scnr);
    let value = "";
    if (scnr.currentChar() === "-") {
      scnr.next();
      value += `-${getDigits(scnr)}`;
    } else {
      value += getDigits(scnr);
    }
    if (scnr.currentChar() === EOF) {
      emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);
    }
    return value;
  }
  function readLiteral(scnr) {
    skipSpaces(scnr);
    eat(scnr, `'`);
    let ch = "";
    let literal = "";
    const fn = (x) => x !== LITERAL_DELIMITER && x !== CHAR_LF;
    while (ch = takeChar(scnr, fn)) {
      if (ch === "\\") {
        literal += readEscapeSequence(scnr);
      } else {
        literal += ch;
      }
    }
    const current = scnr.currentChar();
    if (current === CHAR_LF || current === EOF) {
      emitError(CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER, currentPosition(), 0);
      if (current === CHAR_LF) {
        scnr.next();
        eat(scnr, `'`);
      }
      return literal;
    }
    eat(scnr, `'`);
    return literal;
  }
  function readEscapeSequence(scnr) {
    const ch = scnr.currentChar();
    switch (ch) {
      case "\\":
      case `'`:
        scnr.next();
        return `\\${ch}`;
      case "u":
        return readUnicodeEscapeSequence(scnr, ch, 4);
      case "U":
        return readUnicodeEscapeSequence(scnr, ch, 6);
      default:
        emitError(CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE, currentPosition(), 0, ch);
        return "";
    }
  }
  function readUnicodeEscapeSequence(scnr, unicode, digits) {
    eat(scnr, unicode);
    let sequence = "";
    for (let i = 0; i < digits; i++) {
      const ch = takeHexDigit(scnr);
      if (!ch) {
        emitError(CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE, currentPosition(), 0, `\\${unicode}${sequence}${scnr.currentChar()}`);
        break;
      }
      sequence += ch;
    }
    return `\\${unicode}${sequence}`;
  }
  function readInvalidIdentifier(scnr) {
    skipSpaces(scnr);
    let ch = "";
    let identifiers = "";
    const closure = (ch2) => ch2 !== "{" && ch2 !== "}" && ch2 !== CHAR_SP && ch2 !== CHAR_LF;
    while (ch = takeChar(scnr, closure)) {
      identifiers += ch;
    }
    return identifiers;
  }
  function readLinkedModifier(scnr) {
    let ch = "";
    let name = "";
    while (ch = takeIdentifierChar(scnr)) {
      name += ch;
    }
    return name;
  }
  function readLinkedRefer(scnr) {
    const fn = (detect = false, buf) => {
      const ch = scnr.currentChar();
      if (ch === "{" || ch === "%" || ch === "@" || ch === "|" || ch === "(" || ch === ")" || !ch) {
        return buf;
      } else if (ch === CHAR_SP) {
        return buf;
      } else if (ch === CHAR_LF || ch === DOT) {
        buf += ch;
        scnr.next();
        return fn(detect, buf);
      } else {
        buf += ch;
        scnr.next();
        return fn(true, buf);
      }
    };
    return fn(false, "");
  }
  function readPlural(scnr) {
    skipSpaces(scnr);
    const plural = eat(
      scnr,
      "|"
      /* TokenChars.Pipe */
    );
    skipSpaces(scnr);
    return plural;
  }
  function readTokenInPlaceholder(scnr, context2) {
    let token = null;
    const ch = scnr.currentChar();
    switch (ch) {
      case "{":
        if (context2.braceNest >= 1) {
          emitError(CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER, currentPosition(), 0);
        }
        scnr.next();
        token = getToken(
          context2,
          2,
          "{"
          /* TokenChars.BraceLeft */
        );
        skipSpaces(scnr);
        context2.braceNest++;
        return token;
      case "}":
        if (context2.braceNest > 0 && context2.currentType === 2) {
          emitError(CompileErrorCodes.EMPTY_PLACEHOLDER, currentPosition(), 0);
        }
        scnr.next();
        token = getToken(
          context2,
          3,
          "}"
          /* TokenChars.BraceRight */
        );
        context2.braceNest--;
        context2.braceNest > 0 && skipSpaces(scnr);
        if (context2.inLinked && context2.braceNest === 0) {
          context2.inLinked = false;
        }
        return token;
      case "@":
        if (context2.braceNest > 0) {
          emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);
        }
        token = readTokenInLinked(scnr, context2) || getEndToken(context2);
        context2.braceNest = 0;
        return token;
      default:
        let validNamedIdentifier = true;
        let validListIdentifier = true;
        let validLiteral = true;
        if (isPluralStart(scnr)) {
          if (context2.braceNest > 0) {
            emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);
          }
          token = getToken(context2, 1, readPlural(scnr));
          context2.braceNest = 0;
          context2.inLinked = false;
          return token;
        }
        if (context2.braceNest > 0 && (context2.currentType === 5 || context2.currentType === 6 || context2.currentType === 7)) {
          emitError(CompileErrorCodes.UNTERMINATED_CLOSING_BRACE, currentPosition(), 0);
          context2.braceNest = 0;
          return readToken(scnr, context2);
        }
        if (validNamedIdentifier = isNamedIdentifierStart(scnr, context2)) {
          token = getToken(context2, 5, readNamedIdentifier(scnr));
          skipSpaces(scnr);
          return token;
        }
        if (validListIdentifier = isListIdentifierStart(scnr, context2)) {
          token = getToken(context2, 6, readListIdentifier(scnr));
          skipSpaces(scnr);
          return token;
        }
        if (validLiteral = isLiteralStart(scnr, context2)) {
          token = getToken(context2, 7, readLiteral(scnr));
          skipSpaces(scnr);
          return token;
        }
        if (!validNamedIdentifier && !validListIdentifier && !validLiteral) {
          token = getToken(context2, 13, readInvalidIdentifier(scnr));
          emitError(CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER, currentPosition(), 0, token.value);
          skipSpaces(scnr);
          return token;
        }
        break;
    }
    return token;
  }
  function readTokenInLinked(scnr, context2) {
    const { currentType } = context2;
    let token = null;
    const ch = scnr.currentChar();
    if ((currentType === 8 || currentType === 9 || currentType === 12 || currentType === 10) && (ch === CHAR_LF || ch === CHAR_SP)) {
      emitError(CompileErrorCodes.INVALID_LINKED_FORMAT, currentPosition(), 0);
    }
    switch (ch) {
      case "@":
        scnr.next();
        token = getToken(
          context2,
          8,
          "@"
          /* TokenChars.LinkedAlias */
        );
        context2.inLinked = true;
        return token;
      case ".":
        skipSpaces(scnr);
        scnr.next();
        return getToken(
          context2,
          9,
          "."
          /* TokenChars.LinkedDot */
        );
      case ":":
        skipSpaces(scnr);
        scnr.next();
        return getToken(
          context2,
          10,
          ":"
          /* TokenChars.LinkedDelimiter */
        );
      default:
        if (isPluralStart(scnr)) {
          token = getToken(context2, 1, readPlural(scnr));
          context2.braceNest = 0;
          context2.inLinked = false;
          return token;
        }
        if (isLinkedDotStart(scnr, context2) || isLinkedDelimiterStart(scnr, context2)) {
          skipSpaces(scnr);
          return readTokenInLinked(scnr, context2);
        }
        if (isLinkedModifierStart(scnr, context2)) {
          skipSpaces(scnr);
          return getToken(context2, 12, readLinkedModifier(scnr));
        }
        if (isLinkedReferStart(scnr, context2)) {
          skipSpaces(scnr);
          if (ch === "{") {
            return readTokenInPlaceholder(scnr, context2) || token;
          } else {
            return getToken(context2, 11, readLinkedRefer(scnr));
          }
        }
        if (currentType === 8) {
          emitError(CompileErrorCodes.INVALID_LINKED_FORMAT, currentPosition(), 0);
        }
        context2.braceNest = 0;
        context2.inLinked = false;
        return readToken(scnr, context2);
    }
  }
  function readToken(scnr, context2) {
    let token = {
      type: 14
      /* TokenTypes.EOF */
    };
    if (context2.braceNest > 0) {
      return readTokenInPlaceholder(scnr, context2) || getEndToken(context2);
    }
    if (context2.inLinked) {
      return readTokenInLinked(scnr, context2) || getEndToken(context2);
    }
    const ch = scnr.currentChar();
    switch (ch) {
      case "{":
        return readTokenInPlaceholder(scnr, context2) || getEndToken(context2);
      case "}":
        emitError(CompileErrorCodes.UNBALANCED_CLOSING_BRACE, currentPosition(), 0);
        scnr.next();
        return getToken(
          context2,
          3,
          "}"
          /* TokenChars.BraceRight */
        );
      case "@":
        return readTokenInLinked(scnr, context2) || getEndToken(context2);
      default:
        if (isPluralStart(scnr)) {
          token = getToken(context2, 1, readPlural(scnr));
          context2.braceNest = 0;
          context2.inLinked = false;
          return token;
        }
        const { isModulo, hasSpace } = detectModuloStart(scnr);
        if (isModulo) {
          return hasSpace ? getToken(context2, 0, readText(scnr)) : getToken(context2, 4, readModulo(scnr));
        }
        if (isTextStart(scnr)) {
          return getToken(context2, 0, readText(scnr));
        }
        break;
    }
    return token;
  }
  function nextToken() {
    const { currentType, offset, startLoc, endLoc } = _context;
    _context.lastType = currentType;
    _context.lastOffset = offset;
    _context.lastStartLoc = startLoc;
    _context.lastEndLoc = endLoc;
    _context.offset = currentOffset();
    _context.startLoc = currentPosition();
    if (_scnr.currentChar() === EOF) {
      return getToken(
        _context,
        14
        /* TokenTypes.EOF */
      );
    }
    return readToken(_scnr, _context);
  }
  return {
    nextToken,
    currentOffset,
    currentPosition,
    context
  };
}
function fromEscapeSequence(match, codePoint4, codePoint6) {
  switch (match) {
    case `\\\\`:
      return `\\`;
    case `\\'`:
      return `'`;
    default: {
      const codePoint = parseInt(codePoint4 || codePoint6, 16);
      if (codePoint <= 55295 || codePoint >= 57344) {
        return String.fromCodePoint(codePoint);
      }
      return "�";
    }
  }
}
function createParser(options = {}) {
  const location = options.location !== false;
  const { onError } = options;
  function emitError(tokenzer, code2, start, offset, ...args) {
    const end = tokenzer.currentPosition();
    end.offset += offset;
    end.column += offset;
    if (onError) {
      const loc = location ? createLocation(start, end) : null;
      const err = createCompileError(code2, loc, {
        domain: ERROR_DOMAIN$2,
        args
      });
      onError(err);
    }
  }
  function startNode(type, offset, loc) {
    const node = { type };
    if (location) {
      node.start = offset;
      node.end = offset;
      node.loc = { start: loc, end: loc };
    }
    return node;
  }
  function endNode(node, offset, pos, type) {
    if (type) {
      node.type = type;
    }
    if (location) {
      node.end = offset;
      if (node.loc) {
        node.loc.end = pos;
      }
    }
  }
  function parseText(tokenizer, value) {
    const context = tokenizer.context();
    const node = startNode(3, context.offset, context.startLoc);
    node.value = value;
    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());
    return node;
  }
  function parseList(tokenizer, index) {
    const context = tokenizer.context();
    const { lastOffset: offset, lastStartLoc: loc } = context;
    const node = startNode(5, offset, loc);
    node.index = parseInt(index, 10);
    tokenizer.nextToken();
    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());
    return node;
  }
  function parseNamed(tokenizer, key) {
    const context = tokenizer.context();
    const { lastOffset: offset, lastStartLoc: loc } = context;
    const node = startNode(4, offset, loc);
    node.key = key;
    tokenizer.nextToken();
    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());
    return node;
  }
  function parseLiteral(tokenizer, value) {
    const context = tokenizer.context();
    const { lastOffset: offset, lastStartLoc: loc } = context;
    const node = startNode(9, offset, loc);
    node.value = value.replace(KNOWN_ESCAPES, fromEscapeSequence);
    tokenizer.nextToken();
    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());
    return node;
  }
  function parseLinkedModifier(tokenizer) {
    const token = tokenizer.nextToken();
    const context = tokenizer.context();
    const { lastOffset: offset, lastStartLoc: loc } = context;
    const node = startNode(8, offset, loc);
    if (token.type !== 12) {
      emitError(tokenizer, CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER, context.lastStartLoc, 0);
      node.value = "";
      endNode(node, offset, loc);
      return {
        nextConsumeToken: token,
        node
      };
    }
    if (token.value == null) {
      emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
    }
    node.value = token.value || "";
    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());
    return {
      node
    };
  }
  function parseLinkedKey(tokenizer, value) {
    const context = tokenizer.context();
    const node = startNode(7, context.offset, context.startLoc);
    node.value = value;
    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());
    return node;
  }
  function parseLinked(tokenizer) {
    const context = tokenizer.context();
    const linkedNode = startNode(6, context.offset, context.startLoc);
    let token = tokenizer.nextToken();
    if (token.type === 9) {
      const parsed = parseLinkedModifier(tokenizer);
      linkedNode.modifier = parsed.node;
      token = parsed.nextConsumeToken || tokenizer.nextToken();
    }
    if (token.type !== 10) {
      emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
    }
    token = tokenizer.nextToken();
    if (token.type === 2) {
      token = tokenizer.nextToken();
    }
    switch (token.type) {
      case 11:
        if (token.value == null) {
          emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
        }
        linkedNode.key = parseLinkedKey(tokenizer, token.value || "");
        break;
      case 5:
        if (token.value == null) {
          emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
        }
        linkedNode.key = parseNamed(tokenizer, token.value || "");
        break;
      case 6:
        if (token.value == null) {
          emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
        }
        linkedNode.key = parseList(tokenizer, token.value || "");
        break;
      case 7:
        if (token.value == null) {
          emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
        }
        linkedNode.key = parseLiteral(tokenizer, token.value || "");
        break;
      default:
        emitError(tokenizer, CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY, context.lastStartLoc, 0);
        const nextContext = tokenizer.context();
        const emptyLinkedKeyNode = startNode(7, nextContext.offset, nextContext.startLoc);
        emptyLinkedKeyNode.value = "";
        endNode(emptyLinkedKeyNode, nextContext.offset, nextContext.startLoc);
        linkedNode.key = emptyLinkedKeyNode;
        endNode(linkedNode, nextContext.offset, nextContext.startLoc);
        return {
          nextConsumeToken: token,
          node: linkedNode
        };
    }
    endNode(linkedNode, tokenizer.currentOffset(), tokenizer.currentPosition());
    return {
      node: linkedNode
    };
  }
  function parseMessage(tokenizer) {
    const context = tokenizer.context();
    const startOffset = context.currentType === 1 ? tokenizer.currentOffset() : context.offset;
    const startLoc = context.currentType === 1 ? context.endLoc : context.startLoc;
    const node = startNode(2, startOffset, startLoc);
    node.items = [];
    let nextToken = null;
    do {
      const token = nextToken || tokenizer.nextToken();
      nextToken = null;
      switch (token.type) {
        case 0:
          if (token.value == null) {
            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
          }
          node.items.push(parseText(tokenizer, token.value || ""));
          break;
        case 6:
          if (token.value == null) {
            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
          }
          node.items.push(parseList(tokenizer, token.value || ""));
          break;
        case 5:
          if (token.value == null) {
            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
          }
          node.items.push(parseNamed(tokenizer, token.value || ""));
          break;
        case 7:
          if (token.value == null) {
            emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, getTokenCaption(token));
          }
          node.items.push(parseLiteral(tokenizer, token.value || ""));
          break;
        case 8:
          const parsed = parseLinked(tokenizer);
          node.items.push(parsed.node);
          nextToken = parsed.nextConsumeToken || null;
          break;
      }
    } while (context.currentType !== 14 && context.currentType !== 1);
    const endOffset = context.currentType === 1 ? context.lastOffset : tokenizer.currentOffset();
    const endLoc = context.currentType === 1 ? context.lastEndLoc : tokenizer.currentPosition();
    endNode(node, endOffset, endLoc);
    return node;
  }
  function parsePlural(tokenizer, offset, loc, msgNode) {
    const context = tokenizer.context();
    let hasEmptyMessage = msgNode.items.length === 0;
    const node = startNode(1, offset, loc);
    node.cases = [];
    node.cases.push(msgNode);
    do {
      const msg = parseMessage(tokenizer);
      if (!hasEmptyMessage) {
        hasEmptyMessage = msg.items.length === 0;
      }
      node.cases.push(msg);
    } while (context.currentType !== 14);
    if (hasEmptyMessage) {
      emitError(tokenizer, CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL, loc, 0);
    }
    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());
    return node;
  }
  function parseResource(tokenizer) {
    const context = tokenizer.context();
    const { offset, startLoc } = context;
    const msgNode = parseMessage(tokenizer);
    if (context.currentType === 14) {
      return msgNode;
    } else {
      return parsePlural(tokenizer, offset, startLoc, msgNode);
    }
  }
  function parse2(source) {
    const tokenizer = createTokenizer(source, assign2({}, options));
    const context = tokenizer.context();
    const node = startNode(0, context.offset, context.startLoc);
    if (location && node.loc) {
      node.loc.source = source;
    }
    node.body = parseResource(tokenizer);
    if (options.onCacheKey) {
      node.cacheKey = options.onCacheKey(source);
    }
    if (context.currentType !== 14) {
      emitError(tokenizer, CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS, context.lastStartLoc, 0, source[context.offset] || "");
    }
    endNode(node, tokenizer.currentOffset(), tokenizer.currentPosition());
    return node;
  }
  return { parse: parse2 };
}
function getTokenCaption(token) {
  if (token.type === 14) {
    return "EOF";
  }
  const name = (token.value || "").replace(/\r?\n/gu, "\\n");
  return name.length > 10 ? name.slice(0, 9) + "…" : name;
}
function createTransformer(ast, options = {}) {
  const _context = {
    ast,
    helpers: /* @__PURE__ */ new Set()
  };
  const context = () => _context;
  const helper = (name) => {
    _context.helpers.add(name);
    return name;
  };
  return { context, helper };
}
function traverseNodes(nodes, transformer) {
  for (let i = 0; i < nodes.length; i++) {
    traverseNode(nodes[i], transformer);
  }
}
function traverseNode(node, transformer) {
  switch (node.type) {
    case 1:
      traverseNodes(node.cases, transformer);
      transformer.helper(
        "plural"
        /* HelperNameMap.PLURAL */
      );
      break;
    case 2:
      traverseNodes(node.items, transformer);
      break;
    case 6:
      const linked = node;
      traverseNode(linked.key, transformer);
      transformer.helper(
        "linked"
        /* HelperNameMap.LINKED */
      );
      transformer.helper(
        "type"
        /* HelperNameMap.TYPE */
      );
      break;
    case 5:
      transformer.helper(
        "interpolate"
        /* HelperNameMap.INTERPOLATE */
      );
      transformer.helper(
        "list"
        /* HelperNameMap.LIST */
      );
      break;
    case 4:
      transformer.helper(
        "interpolate"
        /* HelperNameMap.INTERPOLATE */
      );
      transformer.helper(
        "named"
        /* HelperNameMap.NAMED */
      );
      break;
  }
}
function transform(ast, options = {}) {
  const transformer = createTransformer(ast);
  transformer.helper(
    "normalize"
    /* HelperNameMap.NORMALIZE */
  );
  ast.body && traverseNode(ast.body, transformer);
  const context = transformer.context();
  ast.helpers = Array.from(context.helpers);
}
function optimize(ast) {
  const body = ast.body;
  if (body.type === 2) {
    optimizeMessageNode(body);
  } else {
    body.cases.forEach((c) => optimizeMessageNode(c));
  }
  return ast;
}
function optimizeMessageNode(message) {
  if (message.items.length === 1) {
    const item = message.items[0];
    if (item.type === 3 || item.type === 9) {
      message.static = item.value;
      delete item.value;
    }
  } else {
    const values = [];
    for (let i = 0; i < message.items.length; i++) {
      const item = message.items[i];
      if (!(item.type === 3 || item.type === 9)) {
        break;
      }
      if (item.value == null) {
        break;
      }
      values.push(item.value);
    }
    if (values.length === message.items.length) {
      message.static = join2(values);
      for (let i = 0; i < message.items.length; i++) {
        const item = message.items[i];
        if (item.type === 3 || item.type === 9) {
          delete item.value;
        }
      }
    }
  }
}
function minify(node) {
  node.t = node.type;
  switch (node.type) {
    case 0:
      const resource = node;
      minify(resource.body);
      resource.b = resource.body;
      delete resource.body;
      break;
    case 1:
      const plural = node;
      const cases = plural.cases;
      for (let i = 0; i < cases.length; i++) {
        minify(cases[i]);
      }
      plural.c = cases;
      delete plural.cases;
      break;
    case 2:
      const message = node;
      const items = message.items;
      for (let i = 0; i < items.length; i++) {
        minify(items[i]);
      }
      message.i = items;
      delete message.items;
      if (message.static) {
        message.s = message.static;
        delete message.static;
      }
      break;
    case 3:
    case 9:
    case 8:
    case 7:
      const valueNode = node;
      if (valueNode.value) {
        valueNode.v = valueNode.value;
        delete valueNode.value;
      }
      break;
    case 6:
      const linked = node;
      minify(linked.key);
      linked.k = linked.key;
      delete linked.key;
      if (linked.modifier) {
        minify(linked.modifier);
        linked.m = linked.modifier;
        delete linked.modifier;
      }
      break;
    case 5:
      const list = node;
      list.i = list.index;
      delete list.index;
      break;
    case 4:
      const named = node;
      named.k = named.key;
      delete named.key;
      break;
    default: {
      throw createCompileError(CompileErrorCodes.UNHANDLED_MINIFIER_NODE_TYPE, null, {
        domain: ERROR_DOMAIN$1,
        args: [node.type]
      });
    }
  }
  delete node.type;
}
function createCodeGenerator(ast, options) {
  const { sourceMap, filename, breakLineCode, needIndent: _needIndent } = options;
  const location = options.location !== false;
  const _context = {
    filename,
    code: "",
    column: 1,
    line: 1,
    offset: 0,
    map: void 0,
    breakLineCode,
    needIndent: _needIndent,
    indentLevel: 0
  };
  if (location && ast.loc) {
    _context.source = ast.loc.source;
  }
  const context = () => _context;
  function push(code2, node) {
    _context.code += code2;
  }
  function _newline(n, withBreakLine = true) {
    const _breakLineCode = withBreakLine ? breakLineCode : "";
    push(_needIndent ? _breakLineCode + `  `.repeat(n) : _breakLineCode);
  }
  function indent(withNewLine = true) {
    const level = ++_context.indentLevel;
    withNewLine && _newline(level);
  }
  function deindent(withNewLine = true) {
    const level = --_context.indentLevel;
    withNewLine && _newline(level);
  }
  function newline() {
    _newline(_context.indentLevel);
  }
  const helper = (key) => `_${key}`;
  const needIndent = () => _context.needIndent;
  return {
    context,
    push,
    indent,
    deindent,
    newline,
    helper,
    needIndent
  };
}
function generateLinkedNode(generator, node) {
  const { helper } = generator;
  generator.push(`${helper(
    "linked"
    /* HelperNameMap.LINKED */
  )}(`);
  generateNode(generator, node.key);
  if (node.modifier) {
    generator.push(`, `);
    generateNode(generator, node.modifier);
    generator.push(`, _type`);
  } else {
    generator.push(`, undefined, _type`);
  }
  generator.push(`)`);
}
function generateMessageNode(generator, node) {
  const { helper, needIndent } = generator;
  generator.push(`${helper(
    "normalize"
    /* HelperNameMap.NORMALIZE */
  )}([`);
  generator.indent(needIndent());
  const length = node.items.length;
  for (let i = 0; i < length; i++) {
    generateNode(generator, node.items[i]);
    if (i === length - 1) {
      break;
    }
    generator.push(", ");
  }
  generator.deindent(needIndent());
  generator.push("])");
}
function generatePluralNode(generator, node) {
  const { helper, needIndent } = generator;
  if (node.cases.length > 1) {
    generator.push(`${helper(
      "plural"
      /* HelperNameMap.PLURAL */
    )}([`);
    generator.indent(needIndent());
    const length = node.cases.length;
    for (let i = 0; i < length; i++) {
      generateNode(generator, node.cases[i]);
      if (i === length - 1) {
        break;
      }
      generator.push(", ");
    }
    generator.deindent(needIndent());
    generator.push(`])`);
  }
}
function generateResource(generator, node) {
  if (node.body) {
    generateNode(generator, node.body);
  } else {
    generator.push("null");
  }
}
function generateNode(generator, node) {
  const { helper } = generator;
  switch (node.type) {
    case 0:
      generateResource(generator, node);
      break;
    case 1:
      generatePluralNode(generator, node);
      break;
    case 2:
      generateMessageNode(generator, node);
      break;
    case 6:
      generateLinkedNode(generator, node);
      break;
    case 8:
      generator.push(JSON.stringify(node.value), node);
      break;
    case 7:
      generator.push(JSON.stringify(node.value), node);
      break;
    case 5:
      generator.push(`${helper(
        "interpolate"
        /* HelperNameMap.INTERPOLATE */
      )}(${helper(
        "list"
        /* HelperNameMap.LIST */
      )}(${node.index}))`, node);
      break;
    case 4:
      generator.push(`${helper(
        "interpolate"
        /* HelperNameMap.INTERPOLATE */
      )}(${helper(
        "named"
        /* HelperNameMap.NAMED */
      )}(${JSON.stringify(node.key)}))`, node);
      break;
    case 9:
      generator.push(JSON.stringify(node.value), node);
      break;
    case 3:
      generator.push(JSON.stringify(node.value), node);
      break;
    default: {
      throw createCompileError(CompileErrorCodes.UNHANDLED_CODEGEN_NODE_TYPE, null, {
        domain: ERROR_DOMAIN,
        args: [node.type]
      });
    }
  }
}
function baseCompile$1(source, options = {}) {
  const assignedOptions = assign2({}, options);
  const jit = !!assignedOptions.jit;
  const enalbeMinify = !!assignedOptions.minify;
  const enambeOptimize = assignedOptions.optimize == null ? true : assignedOptions.optimize;
  const parser = createParser(assignedOptions);
  const ast = parser.parse(source);
  if (!jit) {
    transform(ast, assignedOptions);
    return generate(ast, assignedOptions);
  } else {
    enambeOptimize && optimize(ast);
    enalbeMinify && minify(ast);
    return { ast, code: "" };
  }
}
function isLiteral(exp) {
  return literalValueRE.test(exp);
}
function stripQuotes(str) {
  const a = str.charCodeAt(0);
  const b = str.charCodeAt(str.length - 1);
  return a === b && (a === 34 || a === 39) ? str.slice(1, -1) : str;
}
function getPathCharType(ch) {
  if (ch === void 0 || ch === null) {
    return "o";
  }
  const code2 = ch.charCodeAt(0);
  switch (code2) {
    case 91:
    case 93:
    case 46:
    case 34:
    case 39:
      return ch;
    case 95:
    case 36:
    case 45:
      return "i";
    case 9:
    case 10:
    case 13:
    case 160:
    case 65279:
    case 8232:
    case 8233:
      return "w";
  }
  return "i";
}
function formatSubPath(path) {
  const trimmed = path.trim();
  if (path.charAt(0) === "0" && isNaN(parseInt(path))) {
    return false;
  }
  return isLiteral(trimmed) ? stripQuotes(trimmed) : "*" + trimmed;
}
function parse(path) {
  const keys = [];
  let index = -1;
  let mode = 0;
  let subPathDepth = 0;
  let c;
  let key;
  let newChar;
  let type;
  let transition;
  let action;
  let typeMap;
  const actions = [];
  actions[
    0
    /* Actions.APPEND */
  ] = () => {
    if (key === void 0) {
      key = newChar;
    } else {
      key += newChar;
    }
  };
  actions[
    1
    /* Actions.PUSH */
  ] = () => {
    if (key !== void 0) {
      keys.push(key);
      key = void 0;
    }
  };
  actions[
    2
    /* Actions.INC_SUB_PATH_DEPTH */
  ] = () => {
    actions[
      0
      /* Actions.APPEND */
    ]();
    subPathDepth++;
  };
  actions[
    3
    /* Actions.PUSH_SUB_PATH */
  ] = () => {
    if (subPathDepth > 0) {
      subPathDepth--;
      mode = 4;
      actions[
        0
        /* Actions.APPEND */
      ]();
    } else {
      subPathDepth = 0;
      if (key === void 0) {
        return false;
      }
      key = formatSubPath(key);
      if (key === false) {
        return false;
      } else {
        actions[
          1
          /* Actions.PUSH */
        ]();
      }
    }
  };
  function maybeUnescapeQuote() {
    const nextChar = path[index + 1];
    if (mode === 5 && nextChar === "'" || mode === 6 && nextChar === '"') {
      index++;
      newChar = "\\" + nextChar;
      actions[
        0
        /* Actions.APPEND */
      ]();
      return true;
    }
  }
  while (mode !== null) {
    index++;
    c = path[index];
    if (c === "\\" && maybeUnescapeQuote()) {
      continue;
    }
    type = getPathCharType(c);
    typeMap = pathStateMachine[mode];
    transition = typeMap[type] || typeMap[
      "l"
      /* PathCharTypes.ELSE */
    ] || 8;
    if (transition === 8) {
      return;
    }
    mode = transition[0];
    if (transition[1] !== void 0) {
      action = actions[transition[1]];
      if (action) {
        newChar = c;
        if (action() === false) {
          return;
        }
      }
    }
    if (mode === 7) {
      return keys;
    }
  }
}
function resolveWithKeyValue(obj, path) {
  return isObject2(obj) ? obj[path] : null;
}
function resolveValue(obj, path) {
  if (!isObject2(obj)) {
    return null;
  }
  let hit = cache.get(path);
  if (!hit) {
    hit = parse(path);
    if (hit) {
      cache.set(path, hit);
    }
  }
  if (!hit) {
    return null;
  }
  const len = hit.length;
  let last = obj;
  let i = 0;
  while (i < len) {
    const val = last[hit[i]];
    if (val === void 0) {
      return null;
    }
    if (isFunction2(last)) {
      return null;
    }
    last = val;
    i++;
  }
  return last;
}
function pluralDefault(choice, choicesLength) {
  choice = Math.abs(choice);
  if (choicesLength === 2) {
    return choice ? choice > 1 ? 1 : 0 : 1;
  }
  return choice ? Math.min(choice, 2) : 0;
}
function getPluralIndex(options) {
  const index = isNumber2(options.pluralIndex) ? options.pluralIndex : -1;
  return options.named && (isNumber2(options.named.count) || isNumber2(options.named.n)) ? isNumber2(options.named.count) ? options.named.count : isNumber2(options.named.n) ? options.named.n : index : index;
}
function normalizeNamed(pluralIndex, props) {
  if (!props.count) {
    props.count = pluralIndex;
  }
  if (!props.n) {
    props.n = pluralIndex;
  }
}
function createMessageContext(options = {}) {
  const locale = options.locale;
  const pluralIndex = getPluralIndex(options);
  const pluralRule = isObject2(options.pluralRules) && isString2(locale) && isFunction2(options.pluralRules[locale]) ? options.pluralRules[locale] : pluralDefault;
  const orgPluralRule = isObject2(options.pluralRules) && isString2(locale) && isFunction2(options.pluralRules[locale]) ? pluralDefault : void 0;
  const plural = (messages) => {
    return messages[pluralRule(pluralIndex, messages.length, orgPluralRule)];
  };
  const _list = options.list || [];
  const list = (index) => _list[index];
  const _named = options.named || {};
  isNumber2(options.pluralIndex) && normalizeNamed(pluralIndex, _named);
  const named = (key) => _named[key];
  function message(key) {
    const msg = isFunction2(options.messages) ? options.messages(key) : isObject2(options.messages) ? options.messages[key] : false;
    return !msg ? options.parent ? options.parent.message(key) : DEFAULT_MESSAGE : msg;
  }
  const _modifier = (name) => options.modifiers ? options.modifiers[name] : DEFAULT_MODIFIER;
  const normalize = isPlainObject2(options.processor) && isFunction2(options.processor.normalize) ? options.processor.normalize : DEFAULT_NORMALIZE;
  const interpolate = isPlainObject2(options.processor) && isFunction2(options.processor.interpolate) ? options.processor.interpolate : DEFAULT_INTERPOLATE;
  const type = isPlainObject2(options.processor) && isString2(options.processor.type) ? options.processor.type : DEFAULT_MESSAGE_DATA_TYPE;
  const linked = (key, ...args) => {
    const [arg1, arg2] = args;
    let type2 = "text";
    let modifier = "";
    if (args.length === 1) {
      if (isObject2(arg1)) {
        modifier = arg1.modifier || modifier;
        type2 = arg1.type || type2;
      } else if (isString2(arg1)) {
        modifier = arg1 || modifier;
      }
    } else if (args.length === 2) {
      if (isString2(arg1)) {
        modifier = arg1 || modifier;
      }
      if (isString2(arg2)) {
        type2 = arg2 || type2;
      }
    }
    const ret = message(key)(ctx);
    const msg = (
      // The message in vnode resolved with linked are returned as an array by processor.nomalize
      type2 === "vnode" && isArray2(ret) && modifier ? ret[0] : ret
    );
    return modifier ? _modifier(modifier)(msg, type2) : msg;
  };
  const ctx = {
    [
      "list"
      /* HelperNameMap.LIST */
    ]: list,
    [
      "named"
      /* HelperNameMap.NAMED */
    ]: named,
    [
      "plural"
      /* HelperNameMap.PLURAL */
    ]: plural,
    [
      "linked"
      /* HelperNameMap.LINKED */
    ]: linked,
    [
      "message"
      /* HelperNameMap.MESSAGE */
    ]: message,
    [
      "type"
      /* HelperNameMap.TYPE */
    ]: type,
    [
      "interpolate"
      /* HelperNameMap.INTERPOLATE */
    ]: interpolate,
    [
      "normalize"
      /* HelperNameMap.NORMALIZE */
    ]: normalize,
    [
      "values"
      /* HelperNameMap.VALUES */
    ]: assign2({}, _list, _named)
  };
  return ctx;
}
function setDevToolsHook(hook) {
  devtools = hook;
}
function getDevToolsHook() {
  return devtools;
}
function initI18nDevTools(i18n, version, meta) {
  devtools && devtools.emit("i18n:init", {
    timestamp: Date.now(),
    i18n,
    version,
    meta
  });
}
function createDevToolsHook(hook) {
  return (payloads) => devtools && devtools.emit(hook, payloads);
}
function getWarnMessage(code2, ...args) {
  return format$1(warnMessages[code2], ...args);
}
function createCoreError(code2) {
  return createCompileError(code2, null, { messages: errorMessages });
}
function getLocale(context, options) {
  return options.locale != null ? resolveLocale(options.locale) : resolveLocale(context.locale);
}
function resolveLocale(locale) {
  if (isString2(locale)) {
    return locale;
  } else {
    if (isFunction2(locale)) {
      if (locale.resolvedOnce && _resolveLocale != null) {
        return _resolveLocale;
      } else if (locale.constructor.name === "Function") {
        const resolve = locale();
        if (isPromise2(resolve)) {
          throw createCoreError(CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE);
        }
        return _resolveLocale = resolve;
      } else {
        throw createCoreError(CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION);
      }
    } else {
      throw createCoreError(CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE);
    }
  }
}
function fallbackWithSimple(ctx, fallback, start) {
  return [.../* @__PURE__ */ new Set([
    start,
    ...isArray2(fallback) ? fallback : isObject2(fallback) ? Object.keys(fallback) : isString2(fallback) ? [fallback] : [start]
  ])];
}
function fallbackWithLocaleChain(ctx, fallback, start) {
  const startLocale = isString2(start) ? start : DEFAULT_LOCALE;
  const context = ctx;
  if (!context.__localeChainCache) {
    context.__localeChainCache = /* @__PURE__ */ new Map();
  }
  let chain = context.__localeChainCache.get(startLocale);
  if (!chain) {
    chain = [];
    let block = [start];
    while (isArray2(block)) {
      block = appendBlockToChain(chain, block, fallback);
    }
    const defaults = isArray2(fallback) || !isPlainObject2(fallback) ? fallback : fallback["default"] ? fallback["default"] : null;
    block = isString2(defaults) ? [defaults] : defaults;
    if (isArray2(block)) {
      appendBlockToChain(chain, block, false);
    }
    context.__localeChainCache.set(startLocale, chain);
  }
  return chain;
}
function appendBlockToChain(chain, block, blocks) {
  let follow = true;
  for (let i = 0; i < block.length && isBoolean2(follow); i++) {
    const locale = block[i];
    if (isString2(locale)) {
      follow = appendLocaleToChain(chain, block[i], blocks);
    }
  }
  return follow;
}
function appendLocaleToChain(chain, locale, blocks) {
  let follow;
  const tokens = locale.split("-");
  do {
    const target = tokens.join("-");
    follow = appendItemToChain(chain, target, blocks);
    tokens.splice(-1, 1);
  } while (tokens.length && follow === true);
  return follow;
}
function appendItemToChain(chain, target, blocks) {
  let follow = false;
  if (!chain.includes(target)) {
    follow = true;
    if (target) {
      follow = target[target.length - 1] !== "!";
      const locale = target.replace(/!/g, "");
      chain.push(locale);
      if ((isArray2(blocks) || isPlainObject2(blocks)) && blocks[locale]) {
        follow = blocks[locale];
      }
    }
  }
  return follow;
}
function getDefaultLinkedModifiers() {
  return {
    upper: (val, type) => {
      return type === "text" && isString2(val) ? val.toUpperCase() : type === "vnode" && isObject2(val) && "__v_isVNode" in val ? val.children.toUpperCase() : val;
    },
    lower: (val, type) => {
      return type === "text" && isString2(val) ? val.toLowerCase() : type === "vnode" && isObject2(val) && "__v_isVNode" in val ? val.children.toLowerCase() : val;
    },
    capitalize: (val, type) => {
      return type === "text" && isString2(val) ? capitalize(val) : type === "vnode" && isObject2(val) && "__v_isVNode" in val ? capitalize(val.children) : val;
    }
  };
}
function registerMessageCompiler(compiler) {
  _compiler = compiler;
}
function registerMessageResolver(resolver) {
  _resolver = resolver;
}
function registerLocaleFallbacker(fallbacker) {
  _fallbacker = fallbacker;
}
function createCoreContext(options = {}) {
  const onWarn = isFunction2(options.onWarn) ? options.onWarn : warn2;
  const version = isString2(options.version) ? options.version : VERSION;
  const locale = isString2(options.locale) || isFunction2(options.locale) ? options.locale : DEFAULT_LOCALE;
  const _locale = isFunction2(locale) ? DEFAULT_LOCALE : locale;
  const fallbackLocale = isArray2(options.fallbackLocale) || isPlainObject2(options.fallbackLocale) || isString2(options.fallbackLocale) || options.fallbackLocale === false ? options.fallbackLocale : _locale;
  const messages = isPlainObject2(options.messages) ? options.messages : { [_locale]: {} };
  const datetimeFormats = isPlainObject2(options.datetimeFormats) ? options.datetimeFormats : { [_locale]: {} };
  const numberFormats = isPlainObject2(options.numberFormats) ? options.numberFormats : { [_locale]: {} };
  const modifiers = assign2({}, options.modifiers || {}, getDefaultLinkedModifiers());
  const pluralRules = options.pluralRules || {};
  const missing = isFunction2(options.missing) ? options.missing : null;
  const missingWarn = isBoolean2(options.missingWarn) || isRegExp2(options.missingWarn) ? options.missingWarn : true;
  const fallbackWarn = isBoolean2(options.fallbackWarn) || isRegExp2(options.fallbackWarn) ? options.fallbackWarn : true;
  const fallbackFormat = !!options.fallbackFormat;
  const unresolving = !!options.unresolving;
  const postTranslation = isFunction2(options.postTranslation) ? options.postTranslation : null;
  const processor = isPlainObject2(options.processor) ? options.processor : null;
  const warnHtmlMessage = isBoolean2(options.warnHtmlMessage) ? options.warnHtmlMessage : true;
  const escapeParameter = !!options.escapeParameter;
  const messageCompiler = isFunction2(options.messageCompiler) ? options.messageCompiler : _compiler;
  if (isFunction2(options.messageCompiler)) {
    warnOnce2(getWarnMessage(CoreWarnCodes.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER));
  }
  const messageResolver = isFunction2(options.messageResolver) ? options.messageResolver : _resolver || resolveWithKeyValue;
  const localeFallbacker = isFunction2(options.localeFallbacker) ? options.localeFallbacker : _fallbacker || fallbackWithSimple;
  const fallbackContext = isObject2(options.fallbackContext) ? options.fallbackContext : void 0;
  const internalOptions = options;
  const __datetimeFormatters = isObject2(internalOptions.__datetimeFormatters) ? internalOptions.__datetimeFormatters : /* @__PURE__ */ new Map();
  const __numberFormatters = isObject2(internalOptions.__numberFormatters) ? internalOptions.__numberFormatters : /* @__PURE__ */ new Map();
  const __meta = isObject2(internalOptions.__meta) ? internalOptions.__meta : {};
  _cid++;
  const context = {
    version,
    cid: _cid,
    locale,
    fallbackLocale,
    messages,
    modifiers,
    pluralRules,
    missing,
    missingWarn,
    fallbackWarn,
    fallbackFormat,
    unresolving,
    postTranslation,
    processor,
    warnHtmlMessage,
    escapeParameter,
    messageCompiler,
    messageResolver,
    localeFallbacker,
    fallbackContext,
    onWarn,
    __meta
  };
  {
    context.datetimeFormats = datetimeFormats;
    context.numberFormats = numberFormats;
    context.__datetimeFormatters = __datetimeFormatters;
    context.__numberFormatters = __numberFormatters;
  }
  {
    context.__v_emitter = internalOptions.__v_emitter != null ? internalOptions.__v_emitter : void 0;
  }
  {
    initI18nDevTools(context, version, __meta);
  }
  return context;
}
function isTranslateFallbackWarn(fallback, key) {
  return fallback instanceof RegExp ? fallback.test(key) : fallback;
}
function isTranslateMissingWarn(missing, key) {
  return missing instanceof RegExp ? missing.test(key) : missing;
}
function handleMissing(context, key, locale, missingWarn, type) {
  const { missing, onWarn } = context;
  {
    const emitter = context.__v_emitter;
    if (emitter) {
      emitter.emit("missing", {
        locale,
        key,
        type,
        groupId: `${type}:${key}`
      });
    }
  }
  if (missing !== null) {
    const ret = missing(context, locale, key, type);
    return isString2(ret) ? ret : key;
  } else {
    if (isTranslateMissingWarn(missingWarn, key)) {
      onWarn(getWarnMessage(CoreWarnCodes.NOT_FOUND_KEY, { key, locale }));
    }
    return key;
  }
}
function updateFallbackLocale(ctx, locale, fallback) {
  const context = ctx;
  context.__localeChainCache = /* @__PURE__ */ new Map();
  ctx.localeFallbacker(ctx, fallback, locale);
}
function format2(ast) {
  const msg = (ctx) => formatParts(ctx, ast);
  return msg;
}
function formatParts(ctx, ast) {
  const body = ast.b || ast.body;
  if ((body.t || body.type) === 1) {
    const plural = body;
    const cases = plural.c || plural.cases;
    return ctx.plural(cases.reduce((messages, c) => [
      ...messages,
      formatMessageParts(ctx, c)
    ], []));
  } else {
    return formatMessageParts(ctx, body);
  }
}
function formatMessageParts(ctx, node) {
  const _static = node.s || node.static;
  if (_static) {
    return ctx.type === "text" ? _static : ctx.normalize([_static]);
  } else {
    const messages = (node.i || node.items).reduce((acm, c) => [...acm, formatMessagePart(ctx, c)], []);
    return ctx.normalize(messages);
  }
}
function formatMessagePart(ctx, node) {
  const type = node.t || node.type;
  switch (type) {
    case 3:
      const text = node;
      return text.v || text.value;
    case 9:
      const literal = node;
      return literal.v || literal.value;
    case 4:
      const named = node;
      return ctx.interpolate(ctx.named(named.k || named.key));
    case 5:
      const list = node;
      return ctx.interpolate(ctx.list(list.i != null ? list.i : list.index));
    case 6:
      const linked = node;
      const modifier = linked.m || linked.modifier;
      return ctx.linked(formatMessagePart(ctx, linked.k || linked.key), modifier ? formatMessagePart(ctx, modifier) : void 0, ctx.type);
    case 7:
      const linkedKey = node;
      return linkedKey.v || linkedKey.value;
    case 8:
      const linkedModifier = node;
      return linkedModifier.v || linkedModifier.value;
    default:
      throw new Error(`unhandled node type on format message part: ${type}`);
  }
}
function checkHtmlMessage(source, warnHtmlMessage) {
  if (warnHtmlMessage && detectHtmlTag(source)) {
    warn2(format$1(WARN_MESSAGE, { source }));
  }
}
function clearCompileCache() {
  compileCache = /* @__PURE__ */ Object.create(null);
}
function baseCompile(message, options = {}) {
  let detectError = false;
  const onError = options.onError || defaultOnError;
  options.onError = (err) => {
    detectError = true;
    onError(err);
  };
  return { ...baseCompile$1(message, options), detectError };
}
function compile(message, context) {
  if (isString2(message)) {
    const warnHtmlMessage = isBoolean2(context.warnHtmlMessage) ? context.warnHtmlMessage : true;
    checkHtmlMessage(message, warnHtmlMessage);
    const onCacheKey = context.onCacheKey || defaultOnCacheKey;
    const cacheKey = onCacheKey(message);
    const cached = compileCache[cacheKey];
    if (cached) {
      return cached;
    }
    const { ast, detectError } = baseCompile(message, {
      ...context,
      location: true,
      jit: true
    });
    const msg = format2(ast);
    return !detectError ? compileCache[cacheKey] = msg : msg;
  } else {
    if (!isMessageAST(message)) {
      warn2(`the message that is resolve with key '${context.key}' is not supported for jit compilation`);
      return () => message;
    }
    const cacheKey = message.cacheKey;
    if (cacheKey) {
      const cached = compileCache[cacheKey];
      if (cached) {
        return cached;
      }
      return compileCache[cacheKey] = format2(message);
    } else {
      return format2(message);
    }
  }
}
function translate(context, ...args) {
  const { fallbackFormat, postTranslation, unresolving, messageCompiler, fallbackLocale, messages } = context;
  const [key, options] = parseTranslateArgs(...args);
  const missingWarn = isBoolean2(options.missingWarn) ? options.missingWarn : context.missingWarn;
  const fallbackWarn = isBoolean2(options.fallbackWarn) ? options.fallbackWarn : context.fallbackWarn;
  const escapeParameter = isBoolean2(options.escapeParameter) ? options.escapeParameter : context.escapeParameter;
  const resolvedMessage = !!options.resolvedMessage;
  const defaultMsgOrKey = isString2(options.default) || isBoolean2(options.default) ? !isBoolean2(options.default) ? options.default : !messageCompiler ? () => key : key : fallbackFormat ? !messageCompiler ? () => key : key : "";
  const enableDefaultMsg = fallbackFormat || defaultMsgOrKey !== "";
  const locale = getLocale(context, options);
  escapeParameter && escapeParams(options);
  let [formatScope, targetLocale, message] = !resolvedMessage ? resolveMessageFormat(context, key, locale, fallbackLocale, fallbackWarn, missingWarn) : [
    key,
    locale,
    messages[locale] || {}
  ];
  let format3 = formatScope;
  let cacheBaseKey = key;
  if (!resolvedMessage && !(isString2(format3) || isMessageAST(format3) || isMessageFunction(format3))) {
    if (enableDefaultMsg) {
      format3 = defaultMsgOrKey;
      cacheBaseKey = format3;
    }
  }
  if (!resolvedMessage && (!(isString2(format3) || isMessageAST(format3) || isMessageFunction(format3)) || !isString2(targetLocale))) {
    return unresolving ? NOT_REOSLVED : key;
  }
  if (isString2(format3) && context.messageCompiler == null) {
    warn2(`The message format compilation is not supported in this build. Because message compiler isn't included. You need to pre-compilation all message format. So translate function return '${key}'.`);
    return key;
  }
  let occurred = false;
  const onError = () => {
    occurred = true;
  };
  const msg = !isMessageFunction(format3) ? compileMessageFormat(context, key, targetLocale, format3, cacheBaseKey, onError) : format3;
  if (occurred) {
    return format3;
  }
  const ctxOptions = getMessageContextOptions(context, targetLocale, message, options);
  const msgContext = createMessageContext(ctxOptions);
  const messaged = evaluateMessage(context, msg, msgContext);
  const ret = postTranslation ? postTranslation(messaged, key) : messaged;
  {
    const payloads = {
      timestamp: Date.now(),
      key: isString2(key) ? key : isMessageFunction(format3) ? format3.key : "",
      locale: targetLocale || (isMessageFunction(format3) ? format3.locale : ""),
      format: isString2(format3) ? format3 : isMessageFunction(format3) ? format3.source : "",
      message: ret
    };
    payloads.meta = assign2({}, context.__meta, getAdditionalMeta() || {});
    translateDevTools(payloads);
  }
  return ret;
}
function escapeParams(options) {
  if (isArray2(options.list)) {
    options.list = options.list.map((item) => isString2(item) ? escapeHtml2(item) : item);
  } else if (isObject2(options.named)) {
    Object.keys(options.named).forEach((key) => {
      if (isString2(options.named[key])) {
        options.named[key] = escapeHtml2(options.named[key]);
      }
    });
  }
}
function resolveMessageFormat(context, key, locale, fallbackLocale, fallbackWarn, missingWarn) {
  const { messages, onWarn, messageResolver: resolveValue2, localeFallbacker } = context;
  const locales = localeFallbacker(context, fallbackLocale, locale);
  let message = {};
  let targetLocale;
  let format3 = null;
  let from = locale;
  let to = null;
  const type = "translate";
  for (let i = 0; i < locales.length; i++) {
    targetLocale = to = locales[i];
    if (locale !== targetLocale && isTranslateFallbackWarn(fallbackWarn, key)) {
      onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_TRANSLATE, {
        key,
        target: targetLocale
      }));
    }
    if (locale !== targetLocale) {
      const emitter = context.__v_emitter;
      if (emitter) {
        emitter.emit("fallback", {
          type,
          key,
          from,
          to,
          groupId: `${type}:${key}`
        });
      }
    }
    message = messages[targetLocale] || {};
    let start = null;
    let startTag;
    let endTag;
    if (inBrowser2) {
      start = window.performance.now();
      startTag = "intlify-message-resolve-start";
      endTag = "intlify-message-resolve-end";
      mark2 && mark2(startTag);
    }
    if ((format3 = resolveValue2(message, key)) === null) {
      format3 = message[key];
    }
    if (inBrowser2) {
      const end = window.performance.now();
      const emitter = context.__v_emitter;
      if (emitter && start && format3) {
        emitter.emit("message-resolve", {
          type: "message-resolve",
          key,
          message: format3,
          time: end - start,
          groupId: `${type}:${key}`
        });
      }
      if (startTag && endTag && mark2 && measure2) {
        mark2(endTag);
        measure2("intlify message resolve", startTag, endTag);
      }
    }
    if (isString2(format3) || isMessageAST(format3) || isMessageFunction(format3)) {
      break;
    }
    const missingRet = handleMissing(
      context,
      // eslint-disable-line @typescript-eslint/no-explicit-any
      key,
      targetLocale,
      missingWarn,
      type
    );
    if (missingRet !== key) {
      format3 = missingRet;
    }
    from = to;
  }
  return [format3, targetLocale, message];
}
function compileMessageFormat(context, key, targetLocale, format3, cacheBaseKey, onError) {
  const { messageCompiler, warnHtmlMessage } = context;
  if (isMessageFunction(format3)) {
    const msg2 = format3;
    msg2.locale = msg2.locale || targetLocale;
    msg2.key = msg2.key || key;
    return msg2;
  }
  if (messageCompiler == null) {
    const msg2 = () => format3;
    msg2.locale = targetLocale;
    msg2.key = key;
    return msg2;
  }
  let start = null;
  let startTag;
  let endTag;
  if (inBrowser2) {
    start = window.performance.now();
    startTag = "intlify-message-compilation-start";
    endTag = "intlify-message-compilation-end";
    mark2 && mark2(startTag);
  }
  const msg = messageCompiler(format3, getCompileContext(context, targetLocale, cacheBaseKey, format3, warnHtmlMessage, onError));
  if (inBrowser2) {
    const end = window.performance.now();
    const emitter = context.__v_emitter;
    if (emitter && start) {
      emitter.emit("message-compilation", {
        type: "message-compilation",
        message: format3,
        time: end - start,
        groupId: `${"translate"}:${key}`
      });
    }
    if (startTag && endTag && mark2 && measure2) {
      mark2(endTag);
      measure2("intlify message compilation", startTag, endTag);
    }
  }
  msg.locale = targetLocale;
  msg.key = key;
  msg.source = format3;
  return msg;
}
function evaluateMessage(context, msg, msgCtx) {
  let start = null;
  let startTag;
  let endTag;
  if (inBrowser2) {
    start = window.performance.now();
    startTag = "intlify-message-evaluation-start";
    endTag = "intlify-message-evaluation-end";
    mark2 && mark2(startTag);
  }
  const messaged = msg(msgCtx);
  if (inBrowser2) {
    const end = window.performance.now();
    const emitter = context.__v_emitter;
    if (emitter && start) {
      emitter.emit("message-evaluation", {
        type: "message-evaluation",
        value: messaged,
        time: end - start,
        groupId: `${"translate"}:${msg.key}`
      });
    }
    if (startTag && endTag && mark2 && measure2) {
      mark2(endTag);
      measure2("intlify message evaluation", startTag, endTag);
    }
  }
  return messaged;
}
function parseTranslateArgs(...args) {
  const [arg1, arg2, arg3] = args;
  const options = {};
  if (!isString2(arg1) && !isNumber2(arg1) && !isMessageFunction(arg1) && !isMessageAST(arg1)) {
    throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);
  }
  const key = isNumber2(arg1) ? String(arg1) : isMessageFunction(arg1) ? arg1 : arg1;
  if (isNumber2(arg2)) {
    options.plural = arg2;
  } else if (isString2(arg2)) {
    options.default = arg2;
  } else if (isPlainObject2(arg2) && !isEmptyObject2(arg2)) {
    options.named = arg2;
  } else if (isArray2(arg2)) {
    options.list = arg2;
  }
  if (isNumber2(arg3)) {
    options.plural = arg3;
  } else if (isString2(arg3)) {
    options.default = arg3;
  } else if (isPlainObject2(arg3)) {
    assign2(options, arg3);
  }
  return [key, options];
}
function getCompileContext(context, locale, key, source, warnHtmlMessage, onError) {
  return {
    locale,
    key,
    warnHtmlMessage,
    onError: (err) => {
      onError && onError(err);
      {
        const _source = getSourceForCodeFrame(source);
        const message = `Message compilation error: ${err.message}`;
        const codeFrame = err.location && _source && generateCodeFrame2(_source, err.location.start.offset, err.location.end.offset);
        const emitter = context.__v_emitter;
        if (emitter && _source) {
          emitter.emit("compile-error", {
            message: _source,
            error: err.message,
            start: err.location && err.location.start.offset,
            end: err.location && err.location.end.offset,
            groupId: `${"translate"}:${key}`
          });
        }
        console.error(codeFrame ? `${message}
${codeFrame}` : message);
      }
    },
    onCacheKey: (source2) => generateFormatCacheKey2(locale, key, source2)
  };
}
function getSourceForCodeFrame(source) {
  if (isString2(source)) {
    return source;
  } else {
    if (source.loc && source.loc.source) {
      return source.loc.source;
    }
  }
}
function getMessageContextOptions(context, locale, message, options) {
  const { modifiers, pluralRules, messageResolver: resolveValue2, fallbackLocale, fallbackWarn, missingWarn, fallbackContext } = context;
  const resolveMessage = (key) => {
    let val = resolveValue2(message, key);
    if (val == null && fallbackContext) {
      const [, , message2] = resolveMessageFormat(fallbackContext, key, locale, fallbackLocale, fallbackWarn, missingWarn);
      val = resolveValue2(message2, key);
    }
    if (isString2(val) || isMessageAST(val)) {
      let occurred = false;
      const onError = () => {
        occurred = true;
      };
      const msg = compileMessageFormat(context, key, locale, val, key, onError);
      return !occurred ? msg : NOOP_MESSAGE_FUNCTION;
    } else if (isMessageFunction(val)) {
      return val;
    } else {
      return NOOP_MESSAGE_FUNCTION;
    }
  };
  const ctxOptions = {
    locale,
    modifiers,
    pluralRules,
    messages: resolveMessage
  };
  if (context.processor) {
    ctxOptions.processor = context.processor;
  }
  if (options.list) {
    ctxOptions.list = options.list;
  }
  if (options.named) {
    ctxOptions.named = options.named;
  }
  if (isNumber2(options.plural)) {
    ctxOptions.pluralIndex = options.plural;
  }
  return ctxOptions;
}
function datetime(context, ...args) {
  const { datetimeFormats, unresolving, fallbackLocale, onWarn, localeFallbacker } = context;
  const { __datetimeFormatters } = context;
  if (!Availabilities.dateTimeFormat) {
    onWarn(getWarnMessage(CoreWarnCodes.CANNOT_FORMAT_DATE));
    return MISSING_RESOLVE_VALUE;
  }
  const [key, value, options, overrides] = parseDateTimeArgs(...args);
  const missingWarn = isBoolean2(options.missingWarn) ? options.missingWarn : context.missingWarn;
  const fallbackWarn = isBoolean2(options.fallbackWarn) ? options.fallbackWarn : context.fallbackWarn;
  const part = !!options.part;
  const locale = getLocale(context, options);
  const locales = localeFallbacker(
    context,
    // eslint-disable-line @typescript-eslint/no-explicit-any
    fallbackLocale,
    locale
  );
  if (!isString2(key) || key === "") {
    return new Intl.DateTimeFormat(locale, overrides).format(value);
  }
  let datetimeFormat = {};
  let targetLocale;
  let format3 = null;
  let from = locale;
  let to = null;
  const type = "datetime format";
  for (let i = 0; i < locales.length; i++) {
    targetLocale = to = locales[i];
    if (locale !== targetLocale && isTranslateFallbackWarn(fallbackWarn, key)) {
      onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_DATE_FORMAT, {
        key,
        target: targetLocale
      }));
    }
    if (locale !== targetLocale) {
      const emitter = context.__v_emitter;
      if (emitter) {
        emitter.emit("fallback", {
          type,
          key,
          from,
          to,
          groupId: `${type}:${key}`
        });
      }
    }
    datetimeFormat = datetimeFormats[targetLocale] || {};
    format3 = datetimeFormat[key];
    if (isPlainObject2(format3))
      break;
    handleMissing(context, key, targetLocale, missingWarn, type);
    from = to;
  }
  if (!isPlainObject2(format3) || !isString2(targetLocale)) {
    return unresolving ? NOT_REOSLVED : key;
  }
  let id = `${targetLocale}__${key}`;
  if (!isEmptyObject2(overrides)) {
    id = `${id}__${JSON.stringify(overrides)}`;
  }
  let formatter = __datetimeFormatters.get(id);
  if (!formatter) {
    formatter = new Intl.DateTimeFormat(targetLocale, assign2({}, format3, overrides));
    __datetimeFormatters.set(id, formatter);
  }
  return !part ? formatter.format(value) : formatter.formatToParts(value);
}
function parseDateTimeArgs(...args) {
  const [arg1, arg2, arg3, arg4] = args;
  const options = {};
  let overrides = {};
  let value;
  if (isString2(arg1)) {
    const matches = arg1.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);
    if (!matches) {
      throw createCoreError(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);
    }
    const dateTime = matches[3] ? matches[3].trim().startsWith("T") ? `${matches[1].trim()}${matches[3].trim()}` : `${matches[1].trim()}T${matches[3].trim()}` : matches[1].trim();
    value = new Date(dateTime);
    try {
      value.toISOString();
    } catch (e) {
      throw createCoreError(CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT);
    }
  } else if (isDate2(arg1)) {
    if (isNaN(arg1.getTime())) {
      throw createCoreError(CoreErrorCodes.INVALID_DATE_ARGUMENT);
    }
    value = arg1;
  } else if (isNumber2(arg1)) {
    value = arg1;
  } else {
    throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);
  }
  if (isString2(arg2)) {
    options.key = arg2;
  } else if (isPlainObject2(arg2)) {
    Object.keys(arg2).forEach((key) => {
      if (DATETIME_FORMAT_OPTIONS_KEYS.includes(key)) {
        overrides[key] = arg2[key];
      } else {
        options[key] = arg2[key];
      }
    });
  }
  if (isString2(arg3)) {
    options.locale = arg3;
  } else if (isPlainObject2(arg3)) {
    overrides = arg3;
  }
  if (isPlainObject2(arg4)) {
    overrides = arg4;
  }
  return [options.key || "", value, options, overrides];
}
function clearDateTimeFormat(ctx, locale, format3) {
  const context = ctx;
  for (const key in format3) {
    const id = `${locale}__${key}`;
    if (!context.__datetimeFormatters.has(id)) {
      continue;
    }
    context.__datetimeFormatters.delete(id);
  }
}
function number(context, ...args) {
  const { numberFormats, unresolving, fallbackLocale, onWarn, localeFallbacker } = context;
  const { __numberFormatters } = context;
  if (!Availabilities.numberFormat) {
    onWarn(getWarnMessage(CoreWarnCodes.CANNOT_FORMAT_NUMBER));
    return MISSING_RESOLVE_VALUE;
  }
  const [key, value, options, overrides] = parseNumberArgs(...args);
  const missingWarn = isBoolean2(options.missingWarn) ? options.missingWarn : context.missingWarn;
  const fallbackWarn = isBoolean2(options.fallbackWarn) ? options.fallbackWarn : context.fallbackWarn;
  const part = !!options.part;
  const locale = getLocale(context, options);
  const locales = localeFallbacker(
    context,
    // eslint-disable-line @typescript-eslint/no-explicit-any
    fallbackLocale,
    locale
  );
  if (!isString2(key) || key === "") {
    return new Intl.NumberFormat(locale, overrides).format(value);
  }
  let numberFormat = {};
  let targetLocale;
  let format3 = null;
  let from = locale;
  let to = null;
  const type = "number format";
  for (let i = 0; i < locales.length; i++) {
    targetLocale = to = locales[i];
    if (locale !== targetLocale && isTranslateFallbackWarn(fallbackWarn, key)) {
      onWarn(getWarnMessage(CoreWarnCodes.FALLBACK_TO_NUMBER_FORMAT, {
        key,
        target: targetLocale
      }));
    }
    if (locale !== targetLocale) {
      const emitter = context.__v_emitter;
      if (emitter) {
        emitter.emit("fallback", {
          type,
          key,
          from,
          to,
          groupId: `${type}:${key}`
        });
      }
    }
    numberFormat = numberFormats[targetLocale] || {};
    format3 = numberFormat[key];
    if (isPlainObject2(format3))
      break;
    handleMissing(context, key, targetLocale, missingWarn, type);
    from = to;
  }
  if (!isPlainObject2(format3) || !isString2(targetLocale)) {
    return unresolving ? NOT_REOSLVED : key;
  }
  let id = `${targetLocale}__${key}`;
  if (!isEmptyObject2(overrides)) {
    id = `${id}__${JSON.stringify(overrides)}`;
  }
  let formatter = __numberFormatters.get(id);
  if (!formatter) {
    formatter = new Intl.NumberFormat(targetLocale, assign2({}, format3, overrides));
    __numberFormatters.set(id, formatter);
  }
  return !part ? formatter.format(value) : formatter.formatToParts(value);
}
function parseNumberArgs(...args) {
  const [arg1, arg2, arg3, arg4] = args;
  const options = {};
  let overrides = {};
  if (!isNumber2(arg1)) {
    throw createCoreError(CoreErrorCodes.INVALID_ARGUMENT);
  }
  const value = arg1;
  if (isString2(arg2)) {
    options.key = arg2;
  } else if (isPlainObject2(arg2)) {
    Object.keys(arg2).forEach((key) => {
      if (NUMBER_FORMAT_OPTIONS_KEYS.includes(key)) {
        overrides[key] = arg2[key];
      } else {
        options[key] = arg2[key];
      }
    });
  }
  if (isString2(arg3)) {
    options.locale = arg3;
  } else if (isPlainObject2(arg3)) {
    overrides = arg3;
  }
  if (isPlainObject2(arg4)) {
    overrides = arg4;
  }
  return [options.key || "", value, options, overrides];
}
function clearNumberFormat(ctx, locale, format3) {
  const context = ctx;
  for (const key in format3) {
    const id = `${locale}__${key}`;
    if (!context.__numberFormatters.has(id)) {
      continue;
    }
    context.__numberFormatters.delete(id);
  }
}
var inBrowser2, mark2, measure2, RE_ARGS2, generateFormatCacheKey2, friendlyJSONstringify2, isNumber2, isDate2, isRegExp2, isEmptyObject2, assign2, isArray2, isFunction2, isString2, isBoolean2, isObject2, isPromise2, objectToString2, toTypeString2, isPlainObject2, toDisplayString2, RANGE2, hasWarned2, CompileErrorCodes, errorMessages$1, RE_HTML_TAG, detectHtmlTag, CHAR_SP, CHAR_CR, CHAR_LF, CHAR_LS, CHAR_PS, EOF, DOT, LITERAL_DELIMITER, ERROR_DOMAIN$3, ERROR_DOMAIN$2, KNOWN_ESCAPES, ERROR_DOMAIN$1, ERROR_DOMAIN, generate, pathStateMachine, literalValueRE, cache, DEFAULT_MODIFIER, DEFAULT_MESSAGE, DEFAULT_MESSAGE_DATA_TYPE, DEFAULT_NORMALIZE, DEFAULT_INTERPOLATE, devtools, translateDevTools, CoreWarnCodes, warnMessages, code, inc, CoreErrorCodes, errorMessages, _resolveLocale, VERSION, NOT_REOSLVED, DEFAULT_LOCALE, MISSING_RESOLVE_VALUE, capitalize, _compiler, _resolver, _fallbacker, _additionalMeta, setAdditionalMeta, getAdditionalMeta, _fallbackContext, setFallbackContext, getFallbackContext, _cid, WARN_MESSAGE, defaultOnCacheKey, compileCache, isMessageAST, compileToFunction, NOOP_MESSAGE_FUNCTION, isMessageFunction, intlDefined, Availabilities, DATETIME_FORMAT_OPTIONS_KEYS, NUMBER_FORMAT_OPTIONS_KEYS;
var init_core_base_esm_browser = __esm({
  "node_modules/@intlify/core-base/dist/core-base.esm-browser.js"() {
    inBrowser2 = typeof window !== "undefined";
    {
      const perf = inBrowser2 && window.performance;
      if (perf && perf.mark && perf.measure && perf.clearMarks && // @ts-ignore browser compat
      perf.clearMeasures) {
        mark2 = (tag) => {
          perf.mark(tag);
        };
        measure2 = (name, startTag, endTag) => {
          perf.measure(name, startTag, endTag);
          perf.clearMarks(startTag);
          perf.clearMarks(endTag);
        };
      }
    }
    RE_ARGS2 = /\{([0-9a-zA-Z]+)\}/g;
    generateFormatCacheKey2 = (locale, key, source) => friendlyJSONstringify2({ l: locale, k: key, s: source });
    friendlyJSONstringify2 = (json) => JSON.stringify(json).replace(/\u2028/g, "\\u2028").replace(/\u2029/g, "\\u2029").replace(/\u0027/g, "\\u0027");
    isNumber2 = (val) => typeof val === "number" && isFinite(val);
    isDate2 = (val) => toTypeString2(val) === "[object Date]";
    isRegExp2 = (val) => toTypeString2(val) === "[object RegExp]";
    isEmptyObject2 = (val) => isPlainObject2(val) && Object.keys(val).length === 0;
    assign2 = Object.assign;
    isArray2 = Array.isArray;
    isFunction2 = (val) => typeof val === "function";
    isString2 = (val) => typeof val === "string";
    isBoolean2 = (val) => typeof val === "boolean";
    isObject2 = (val) => val !== null && typeof val === "object";
    isPromise2 = (val) => {
      return isObject2(val) && isFunction2(val.then) && isFunction2(val.catch);
    };
    objectToString2 = Object.prototype.toString;
    toTypeString2 = (value) => objectToString2.call(value);
    isPlainObject2 = (val) => {
      if (!isObject2(val))
        return false;
      const proto = Object.getPrototypeOf(val);
      return proto === null || proto.constructor === Object;
    };
    toDisplayString2 = (val) => {
      return val == null ? "" : isArray2(val) || isPlainObject2(val) && val.toString === objectToString2 ? JSON.stringify(val, null, 2) : String(val);
    };
    RANGE2 = 2;
    hasWarned2 = {};
    CompileErrorCodes = {
      // tokenizer error codes
      EXPECTED_TOKEN: 1,
      INVALID_TOKEN_IN_PLACEHOLDER: 2,
      UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER: 3,
      UNKNOWN_ESCAPE_SEQUENCE: 4,
      INVALID_UNICODE_ESCAPE_SEQUENCE: 5,
      UNBALANCED_CLOSING_BRACE: 6,
      UNTERMINATED_CLOSING_BRACE: 7,
      EMPTY_PLACEHOLDER: 8,
      NOT_ALLOW_NEST_PLACEHOLDER: 9,
      INVALID_LINKED_FORMAT: 10,
      // parser error codes
      MUST_HAVE_MESSAGES_IN_PLURAL: 11,
      UNEXPECTED_EMPTY_LINKED_MODIFIER: 12,
      UNEXPECTED_EMPTY_LINKED_KEY: 13,
      UNEXPECTED_LEXICAL_ANALYSIS: 14,
      // generator error codes
      UNHANDLED_CODEGEN_NODE_TYPE: 15,
      // minifier error codes
      UNHANDLED_MINIFIER_NODE_TYPE: 16,
      // Special value for higher-order compilers to pick up the last code
      // to avoid collision of error codes. This should always be kept as the last
      // item.
      __EXTEND_POINT__: 17
    };
    errorMessages$1 = {
      // tokenizer error messages
      [CompileErrorCodes.EXPECTED_TOKEN]: `Expected token: '{0}'`,
      [CompileErrorCodes.INVALID_TOKEN_IN_PLACEHOLDER]: `Invalid token in placeholder: '{0}'`,
      [CompileErrorCodes.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER]: `Unterminated single quote in placeholder`,
      [CompileErrorCodes.UNKNOWN_ESCAPE_SEQUENCE]: `Unknown escape sequence: \\{0}`,
      [CompileErrorCodes.INVALID_UNICODE_ESCAPE_SEQUENCE]: `Invalid unicode escape sequence: {0}`,
      [CompileErrorCodes.UNBALANCED_CLOSING_BRACE]: `Unbalanced closing brace`,
      [CompileErrorCodes.UNTERMINATED_CLOSING_BRACE]: `Unterminated closing brace`,
      [CompileErrorCodes.EMPTY_PLACEHOLDER]: `Empty placeholder`,
      [CompileErrorCodes.NOT_ALLOW_NEST_PLACEHOLDER]: `Not allowed nest placeholder`,
      [CompileErrorCodes.INVALID_LINKED_FORMAT]: `Invalid linked format`,
      // parser error messages
      [CompileErrorCodes.MUST_HAVE_MESSAGES_IN_PLURAL]: `Plural must have messages`,
      [CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_MODIFIER]: `Unexpected empty linked modifier`,
      [CompileErrorCodes.UNEXPECTED_EMPTY_LINKED_KEY]: `Unexpected empty linked key`,
      [CompileErrorCodes.UNEXPECTED_LEXICAL_ANALYSIS]: `Unexpected lexical analysis in token: '{0}'`,
      // generator error messages
      [CompileErrorCodes.UNHANDLED_CODEGEN_NODE_TYPE]: `unhandled codegen node type: '{0}'`,
      // minimizer error messages
      [CompileErrorCodes.UNHANDLED_MINIFIER_NODE_TYPE]: `unhandled mimifier node type: '{0}'`
    };
    RE_HTML_TAG = /<\/?[\w\s="/.':;#-\/]+>/;
    detectHtmlTag = (source) => RE_HTML_TAG.test(source);
    CHAR_SP = " ";
    CHAR_CR = "\r";
    CHAR_LF = "\n";
    CHAR_LS = String.fromCharCode(8232);
    CHAR_PS = String.fromCharCode(8233);
    EOF = void 0;
    DOT = ".";
    LITERAL_DELIMITER = "'";
    ERROR_DOMAIN$3 = "tokenizer";
    ERROR_DOMAIN$2 = "parser";
    KNOWN_ESCAPES = /(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;
    ERROR_DOMAIN$1 = "minifier";
    ERROR_DOMAIN = "parser";
    generate = (ast, options = {}) => {
      const mode = isString2(options.mode) ? options.mode : "normal";
      const filename = isString2(options.filename) ? options.filename : "message.intl";
      const sourceMap = !!options.sourceMap;
      const breakLineCode = options.breakLineCode != null ? options.breakLineCode : mode === "arrow" ? ";" : "\n";
      const needIndent = options.needIndent ? options.needIndent : mode !== "arrow";
      const helpers = ast.helpers || [];
      const generator = createCodeGenerator(ast, {
        mode,
        filename,
        sourceMap,
        breakLineCode,
        needIndent
      });
      generator.push(mode === "normal" ? `function __msg__ (ctx) {` : `(ctx) => {`);
      generator.indent(needIndent);
      if (helpers.length > 0) {
        generator.push(`const { ${join2(helpers.map((s) => `${s}: _${s}`), ", ")} } = ctx`);
        generator.newline();
      }
      generator.push(`return `);
      generateNode(generator, ast);
      generator.deindent(needIndent);
      generator.push(`}`);
      delete ast.helpers;
      const { code: code2, map } = generator.context();
      return {
        ast,
        code: code2,
        map: map ? map.toJSON() : void 0
        // eslint-disable-line @typescript-eslint/no-explicit-any
      };
    };
    pathStateMachine = [];
    pathStateMachine[
      0
      /* States.BEFORE_PATH */
    ] = {
      [
        "w"
        /* PathCharTypes.WORKSPACE */
      ]: [
        0
        /* States.BEFORE_PATH */
      ],
      [
        "i"
        /* PathCharTypes.IDENT */
      ]: [
        3,
        0
        /* Actions.APPEND */
      ],
      [
        "["
        /* PathCharTypes.LEFT_BRACKET */
      ]: [
        4
        /* States.IN_SUB_PATH */
      ],
      [
        "o"
        /* PathCharTypes.END_OF_FAIL */
      ]: [
        7
        /* States.AFTER_PATH */
      ]
    };
    pathStateMachine[
      1
      /* States.IN_PATH */
    ] = {
      [
        "w"
        /* PathCharTypes.WORKSPACE */
      ]: [
        1
        /* States.IN_PATH */
      ],
      [
        "."
        /* PathCharTypes.DOT */
      ]: [
        2
        /* States.BEFORE_IDENT */
      ],
      [
        "["
        /* PathCharTypes.LEFT_BRACKET */
      ]: [
        4
        /* States.IN_SUB_PATH */
      ],
      [
        "o"
        /* PathCharTypes.END_OF_FAIL */
      ]: [
        7
        /* States.AFTER_PATH */
      ]
    };
    pathStateMachine[
      2
      /* States.BEFORE_IDENT */
    ] = {
      [
        "w"
        /* PathCharTypes.WORKSPACE */
      ]: [
        2
        /* States.BEFORE_IDENT */
      ],
      [
        "i"
        /* PathCharTypes.IDENT */
      ]: [
        3,
        0
        /* Actions.APPEND */
      ],
      [
        "0"
        /* PathCharTypes.ZERO */
      ]: [
        3,
        0
        /* Actions.APPEND */
      ]
    };
    pathStateMachine[
      3
      /* States.IN_IDENT */
    ] = {
      [
        "i"
        /* PathCharTypes.IDENT */
      ]: [
        3,
        0
        /* Actions.APPEND */
      ],
      [
        "0"
        /* PathCharTypes.ZERO */
      ]: [
        3,
        0
        /* Actions.APPEND */
      ],
      [
        "w"
        /* PathCharTypes.WORKSPACE */
      ]: [
        1,
        1
        /* Actions.PUSH */
      ],
      [
        "."
        /* PathCharTypes.DOT */
      ]: [
        2,
        1
        /* Actions.PUSH */
      ],
      [
        "["
        /* PathCharTypes.LEFT_BRACKET */
      ]: [
        4,
        1
        /* Actions.PUSH */
      ],
      [
        "o"
        /* PathCharTypes.END_OF_FAIL */
      ]: [
        7,
        1
        /* Actions.PUSH */
      ]
    };
    pathStateMachine[
      4
      /* States.IN_SUB_PATH */
    ] = {
      [
        "'"
        /* PathCharTypes.SINGLE_QUOTE */
      ]: [
        5,
        0
        /* Actions.APPEND */
      ],
      [
        '"'
        /* PathCharTypes.DOUBLE_QUOTE */
      ]: [
        6,
        0
        /* Actions.APPEND */
      ],
      [
        "["
        /* PathCharTypes.LEFT_BRACKET */
      ]: [
        4,
        2
        /* Actions.INC_SUB_PATH_DEPTH */
      ],
      [
        "]"
        /* PathCharTypes.RIGHT_BRACKET */
      ]: [
        1,
        3
        /* Actions.PUSH_SUB_PATH */
      ],
      [
        "o"
        /* PathCharTypes.END_OF_FAIL */
      ]: 8,
      [
        "l"
        /* PathCharTypes.ELSE */
      ]: [
        4,
        0
        /* Actions.APPEND */
      ]
    };
    pathStateMachine[
      5
      /* States.IN_SINGLE_QUOTE */
    ] = {
      [
        "'"
        /* PathCharTypes.SINGLE_QUOTE */
      ]: [
        4,
        0
        /* Actions.APPEND */
      ],
      [
        "o"
        /* PathCharTypes.END_OF_FAIL */
      ]: 8,
      [
        "l"
        /* PathCharTypes.ELSE */
      ]: [
        5,
        0
        /* Actions.APPEND */
      ]
    };
    pathStateMachine[
      6
      /* States.IN_DOUBLE_QUOTE */
    ] = {
      [
        '"'
        /* PathCharTypes.DOUBLE_QUOTE */
      ]: [
        4,
        0
        /* Actions.APPEND */
      ],
      [
        "o"
        /* PathCharTypes.END_OF_FAIL */
      ]: 8,
      [
        "l"
        /* PathCharTypes.ELSE */
      ]: [
        6,
        0
        /* Actions.APPEND */
      ]
    };
    literalValueRE = /^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;
    cache = /* @__PURE__ */ new Map();
    DEFAULT_MODIFIER = (str) => str;
    DEFAULT_MESSAGE = (ctx) => "";
    DEFAULT_MESSAGE_DATA_TYPE = "text";
    DEFAULT_NORMALIZE = (values) => values.length === 0 ? "" : join2(values);
    DEFAULT_INTERPOLATE = toDisplayString2;
    devtools = null;
    translateDevTools = createDevToolsHook(
      "function:translate"
      /* IntlifyDevToolsHooks.FunctionTranslate */
    );
    CoreWarnCodes = {
      NOT_FOUND_KEY: 1,
      FALLBACK_TO_TRANSLATE: 2,
      CANNOT_FORMAT_NUMBER: 3,
      FALLBACK_TO_NUMBER_FORMAT: 4,
      CANNOT_FORMAT_DATE: 5,
      FALLBACK_TO_DATE_FORMAT: 6,
      EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER: 7,
      __EXTEND_POINT__: 8
    };
    warnMessages = {
      [CoreWarnCodes.NOT_FOUND_KEY]: `Not found '{key}' key in '{locale}' locale messages.`,
      [CoreWarnCodes.FALLBACK_TO_TRANSLATE]: `Fall back to translate '{key}' key with '{target}' locale.`,
      [CoreWarnCodes.CANNOT_FORMAT_NUMBER]: `Cannot format a number value due to not supported Intl.NumberFormat.`,
      [CoreWarnCodes.FALLBACK_TO_NUMBER_FORMAT]: `Fall back to number format '{key}' key with '{target}' locale.`,
      [CoreWarnCodes.CANNOT_FORMAT_DATE]: `Cannot format a date value due to not supported Intl.DateTimeFormat.`,
      [CoreWarnCodes.FALLBACK_TO_DATE_FORMAT]: `Fall back to datetime format '{key}' key with '{target}' locale.`,
      [CoreWarnCodes.EXPERIMENTAL_CUSTOM_MESSAGE_COMPILER]: `This project is using Custom Message Compiler, which is an experimental feature. It may receive breaking changes or be removed in the future.`
    };
    code = CompileErrorCodes.__EXTEND_POINT__;
    inc = incrementer2(code);
    CoreErrorCodes = {
      INVALID_ARGUMENT: code,
      INVALID_DATE_ARGUMENT: inc(),
      INVALID_ISO_DATE_ARGUMENT: inc(),
      NOT_SUPPORT_NON_STRING_MESSAGE: inc(),
      NOT_SUPPORT_LOCALE_PROMISE_VALUE: inc(),
      NOT_SUPPORT_LOCALE_ASYNC_FUNCTION: inc(),
      NOT_SUPPORT_LOCALE_TYPE: inc(),
      __EXTEND_POINT__: inc()
      // 25
    };
    errorMessages = {
      [CoreErrorCodes.INVALID_ARGUMENT]: "Invalid arguments",
      [CoreErrorCodes.INVALID_DATE_ARGUMENT]: "The date provided is an invalid Date object.Make sure your Date represents a valid date.",
      [CoreErrorCodes.INVALID_ISO_DATE_ARGUMENT]: "The argument provided is not a valid ISO date string",
      [CoreErrorCodes.NOT_SUPPORT_NON_STRING_MESSAGE]: "Not support non-string message",
      [CoreErrorCodes.NOT_SUPPORT_LOCALE_PROMISE_VALUE]: "cannot support promise value",
      [CoreErrorCodes.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION]: "cannot support async function",
      [CoreErrorCodes.NOT_SUPPORT_LOCALE_TYPE]: "cannot support locale type"
    };
    VERSION = "9.9.1";
    NOT_REOSLVED = -1;
    DEFAULT_LOCALE = "en-US";
    MISSING_RESOLVE_VALUE = "";
    capitalize = (str) => `${str.charAt(0).toLocaleUpperCase()}${str.substr(1)}`;
    _additionalMeta = null;
    setAdditionalMeta = (meta) => {
      _additionalMeta = meta;
    };
    getAdditionalMeta = () => _additionalMeta;
    _fallbackContext = null;
    setFallbackContext = (context) => {
      _fallbackContext = context;
    };
    getFallbackContext = () => _fallbackContext;
    _cid = 0;
    WARN_MESSAGE = `Detected HTML in '{source}' message. Recommend not using HTML messages to avoid XSS.`;
    defaultOnCacheKey = (message) => message;
    compileCache = /* @__PURE__ */ Object.create(null);
    isMessageAST = (val) => isObject2(val) && (val.t === 0 || val.type === 0) && ("b" in val || "body" in val);
    compileToFunction = (message, context) => {
      if (!isString2(message)) {
        throw createCoreError(CoreErrorCodes.NOT_SUPPORT_NON_STRING_MESSAGE);
      }
      {
        const warnHtmlMessage = isBoolean2(context.warnHtmlMessage) ? context.warnHtmlMessage : true;
        checkHtmlMessage(message, warnHtmlMessage);
        const onCacheKey = context.onCacheKey || defaultOnCacheKey;
        const cacheKey = onCacheKey(message);
        const cached = compileCache[cacheKey];
        if (cached) {
          return cached;
        }
        const { code: code2, detectError } = baseCompile(message, context);
        const msg = new Function(`return ${code2}`)();
        return !detectError ? compileCache[cacheKey] = msg : msg;
      }
    };
    NOOP_MESSAGE_FUNCTION = () => "";
    isMessageFunction = (val) => isFunction2(val);
    intlDefined = typeof Intl !== "undefined";
    Availabilities = {
      dateTimeFormat: intlDefined && typeof Intl.DateTimeFormat !== "undefined",
      numberFormat: intlDefined && typeof Intl.NumberFormat !== "undefined"
    };
    DATETIME_FORMAT_OPTIONS_KEYS = [
      "localeMatcher",
      "weekday",
      "era",
      "year",
      "month",
      "day",
      "hour",
      "minute",
      "second",
      "timeZoneName",
      "formatMatcher",
      "hour12",
      "timeZone",
      "dateStyle",
      "timeStyle",
      "calendar",
      "dayPeriod",
      "numberingSystem",
      "hourCycle",
      "fractionalSecondDigits"
    ];
    NUMBER_FORMAT_OPTIONS_KEYS = [
      "localeMatcher",
      "style",
      "currency",
      "currencyDisplay",
      "currencySign",
      "useGrouping",
      "minimumIntegerDigits",
      "minimumFractionDigits",
      "maximumFractionDigits",
      "minimumSignificantDigits",
      "maximumSignificantDigits",
      "compactDisplay",
      "notation",
      "signDisplay",
      "unit",
      "unitDisplay",
      "roundingMode",
      "roundingPriority",
      "roundingIncrement",
      "trailingZeroDisplay"
    ];
  }
});

// node_modules/vue-i18n/dist/vue-i18n.cjs
var require_vue_i18n = __commonJS({
  "node_modules/vue-i18n/dist/vue-i18n.cjs"(exports) {
    "use strict";
    var shared = (init_shared_esm_browser(), __toCommonJS(shared_esm_browser_exports));
    var coreBase = (init_core_base_esm_browser(), __toCommonJS(core_base_esm_browser_exports));
    var vue = require_vue();
    var VERSION2 = "9.9.1";
    var code$1 = coreBase.CoreWarnCodes.__EXTEND_POINT__;
    var inc$1 = shared.incrementer(code$1);
    var I18nWarnCodes = {
      FALLBACK_TO_ROOT: code$1,
      NOT_SUPPORTED_PRESERVE: inc$1(),
      NOT_SUPPORTED_FORMATTER: inc$1(),
      NOT_SUPPORTED_PRESERVE_DIRECTIVE: inc$1(),
      NOT_SUPPORTED_GET_CHOICE_INDEX: inc$1(),
      COMPONENT_NAME_LEGACY_COMPATIBLE: inc$1(),
      NOT_FOUND_PARENT_SCOPE: inc$1(),
      IGNORE_OBJ_FLATTEN: inc$1(),
      NOTICE_DROP_ALLOW_COMPOSITION: inc$1()
      // 17
    };
    var warnMessages2 = {
      [I18nWarnCodes.FALLBACK_TO_ROOT]: `Fall back to {type} '{key}' with root locale.`,
      [I18nWarnCodes.NOT_SUPPORTED_PRESERVE]: `Not supported 'preserve'.`,
      [I18nWarnCodes.NOT_SUPPORTED_FORMATTER]: `Not supported 'formatter'.`,
      [I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE]: `Not supported 'preserveDirectiveContent'.`,
      [I18nWarnCodes.NOT_SUPPORTED_GET_CHOICE_INDEX]: `Not supported 'getChoiceIndex'.`,
      [I18nWarnCodes.COMPONENT_NAME_LEGACY_COMPATIBLE]: `Component name legacy compatible: '{name}' -> 'i18n'`,
      [I18nWarnCodes.NOT_FOUND_PARENT_SCOPE]: `Not found parent scope. use the global scope.`,
      [I18nWarnCodes.IGNORE_OBJ_FLATTEN]: `Ignore object flatten: '{key}' key has an string value`,
      [I18nWarnCodes.NOTICE_DROP_ALLOW_COMPOSITION]: `'allowComposition' option will be dropped in the next major version. For more information, please see 👉 https://tinyurl.com/2p97mcze`
    };
    function getWarnMessage2(code3, ...args) {
      return shared.format(warnMessages2[code3], ...args);
    }
    var code2 = coreBase.CoreErrorCodes.__EXTEND_POINT__;
    var inc2 = shared.incrementer(code2);
    var I18nErrorCodes = {
      // composer module errors
      UNEXPECTED_RETURN_TYPE: code2,
      // legacy module errors
      INVALID_ARGUMENT: inc2(),
      // i18n module errors
      MUST_BE_CALL_SETUP_TOP: inc2(),
      NOT_INSTALLED: inc2(),
      NOT_AVAILABLE_IN_LEGACY_MODE: inc2(),
      // directive module errors
      REQUIRED_VALUE: inc2(),
      INVALID_VALUE: inc2(),
      // vue-devtools errors
      CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN: inc2(),
      NOT_INSTALLED_WITH_PROVIDE: inc2(),
      // unexpected error
      UNEXPECTED_ERROR: inc2(),
      // not compatible legacy vue-i18n constructor
      NOT_COMPATIBLE_LEGACY_VUE_I18N: inc2(),
      // bridge support vue 2.x only
      BRIDGE_SUPPORT_VUE_2_ONLY: inc2(),
      // need to define `i18n` option in `allowComposition: true` and `useScope: 'local' at `useI18n``
      MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION: inc2(),
      // Not available Compostion API in Legacy API mode. Please make sure that the legacy API mode is working properly
      NOT_AVAILABLE_COMPOSITION_IN_LEGACY: inc2(),
      // for enhancement
      __EXTEND_POINT__: inc2()
      // 40
    };
    function createI18nError(code3, ...args) {
      return coreBase.createCompileError(code3, null, { messages: errorMessages2, args });
    }
    var errorMessages2 = {
      [I18nErrorCodes.UNEXPECTED_RETURN_TYPE]: "Unexpected return type in composer",
      [I18nErrorCodes.INVALID_ARGUMENT]: "Invalid argument",
      [I18nErrorCodes.MUST_BE_CALL_SETUP_TOP]: "Must be called at the top of a `setup` function",
      [I18nErrorCodes.NOT_INSTALLED]: "Need to install with `app.use` function",
      [I18nErrorCodes.UNEXPECTED_ERROR]: "Unexpected error",
      [I18nErrorCodes.NOT_AVAILABLE_IN_LEGACY_MODE]: "Not available in legacy mode",
      [I18nErrorCodes.REQUIRED_VALUE]: `Required in value: {0}`,
      [I18nErrorCodes.INVALID_VALUE]: `Invalid value`,
      [I18nErrorCodes.CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN]: `Cannot setup vue-devtools plugin`,
      [I18nErrorCodes.NOT_INSTALLED_WITH_PROVIDE]: "Need to install with `provide` function",
      [I18nErrorCodes.NOT_COMPATIBLE_LEGACY_VUE_I18N]: "Not compatible legacy VueI18n.",
      [I18nErrorCodes.BRIDGE_SUPPORT_VUE_2_ONLY]: "vue-i18n-bridge support Vue 2.x only",
      [I18nErrorCodes.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION]: "Must define ‘i18n’ option or custom block in Composition API with using local scope in Legacy API mode",
      [I18nErrorCodes.NOT_AVAILABLE_COMPOSITION_IN_LEGACY]: "Not available Compostion API in Legacy API mode. Please make sure that the legacy API mode is working properly"
    };
    var TranslateVNodeSymbol = shared.makeSymbol("__translateVNode");
    var DatetimePartsSymbol = shared.makeSymbol("__datetimeParts");
    var NumberPartsSymbol = shared.makeSymbol("__numberParts");
    var EnableEmitter = shared.makeSymbol("__enableEmitter");
    var DisableEmitter = shared.makeSymbol("__disableEmitter");
    var SetPluralRulesSymbol = shared.makeSymbol("__setPluralRules");
    shared.makeSymbol("__intlifyMeta");
    var InejctWithOptionSymbol = shared.makeSymbol("__injectWithOption");
    var DisposeSymbol = shared.makeSymbol("__dispose");
    var __VUE_I18N_BRIDGE__ = "__VUE_I18N_BRIDGE__";
    function handleFlatJson(obj) {
      if (!shared.isObject(obj)) {
        return obj;
      }
      for (const key in obj) {
        if (!shared.hasOwn(obj, key)) {
          continue;
        }
        if (!key.includes(".")) {
          if (shared.isObject(obj[key])) {
            handleFlatJson(obj[key]);
          }
        } else {
          const subKeys = key.split(".");
          const lastIndex = subKeys.length - 1;
          let currentObj = obj;
          let hasStringValue = false;
          for (let i = 0; i < lastIndex; i++) {
            if (!(subKeys[i] in currentObj)) {
              currentObj[subKeys[i]] = {};
            }
            if (!shared.isObject(currentObj[subKeys[i]])) {
              shared.warn(getWarnMessage2(I18nWarnCodes.IGNORE_OBJ_FLATTEN, {
                key: subKeys[i]
              }));
              hasStringValue = true;
              break;
            }
            currentObj = currentObj[subKeys[i]];
          }
          if (!hasStringValue) {
            currentObj[subKeys[lastIndex]] = obj[key];
            delete obj[key];
          }
          if (shared.isObject(currentObj[subKeys[lastIndex]])) {
            handleFlatJson(currentObj[subKeys[lastIndex]]);
          }
        }
      }
      return obj;
    }
    function getLocaleMessages(locale, options) {
      const { messages, __i18n, messageResolver, flatJson } = options;
      const ret = shared.isPlainObject(messages) ? messages : shared.isArray(__i18n) ? {} : { [locale]: {} };
      if (shared.isArray(__i18n)) {
        __i18n.forEach((custom) => {
          if ("locale" in custom && "resource" in custom) {
            const { locale: locale2, resource } = custom;
            if (locale2) {
              ret[locale2] = ret[locale2] || {};
              shared.deepCopy(resource, ret[locale2]);
            } else {
              shared.deepCopy(resource, ret);
            }
          } else {
            shared.isString(custom) && shared.deepCopy(JSON.parse(custom), ret);
          }
        });
      }
      if (messageResolver == null && flatJson) {
        for (const key in ret) {
          if (shared.hasOwn(ret, key)) {
            handleFlatJson(ret[key]);
          }
        }
      }
      return ret;
    }
    function getComponentOptions(instance) {
      return instance.type;
    }
    function adjustI18nResources(gl, options, componentOptions) {
      let messages = shared.isObject(options.messages) ? options.messages : {};
      if ("__i18nGlobal" in componentOptions) {
        messages = getLocaleMessages(gl.locale.value, {
          messages,
          __i18n: componentOptions.__i18nGlobal
        });
      }
      const locales = Object.keys(messages);
      if (locales.length) {
        locales.forEach((locale) => {
          gl.mergeLocaleMessage(locale, messages[locale]);
        });
      }
      {
        if (shared.isObject(options.datetimeFormats)) {
          const locales2 = Object.keys(options.datetimeFormats);
          if (locales2.length) {
            locales2.forEach((locale) => {
              gl.mergeDateTimeFormat(locale, options.datetimeFormats[locale]);
            });
          }
        }
        if (shared.isObject(options.numberFormats)) {
          const locales2 = Object.keys(options.numberFormats);
          if (locales2.length) {
            locales2.forEach((locale) => {
              gl.mergeNumberFormat(locale, options.numberFormats[locale]);
            });
          }
        }
      }
    }
    function createTextNode(key) {
      return vue.createVNode(vue.Text, null, key, 0);
    }
    var DEVTOOLS_META = "__INTLIFY_META__";
    var NOOP_RETURN_ARRAY = () => [];
    var NOOP_RETURN_FALSE = () => false;
    var composerID = 0;
    function defineCoreMissingHandler(missing) {
      return (ctx, locale, key, type) => {
        return missing(locale, key, vue.getCurrentInstance() || void 0, type);
      };
    }
    var getMetaInfo = () => {
      const instance = vue.getCurrentInstance();
      let meta = null;
      return instance && (meta = getComponentOptions(instance)[DEVTOOLS_META]) ? { [DEVTOOLS_META]: meta } : null;
    };
    function createComposer(options = {}, VueI18nLegacy) {
      const { __root, __injectWithOption } = options;
      const _isGlobal = __root === void 0;
      const flatJson = options.flatJson;
      const _ref = shared.inBrowser ? vue.ref : vue.shallowRef;
      let _inheritLocale = shared.isBoolean(options.inheritLocale) ? options.inheritLocale : true;
      const _locale = _ref(
        // prettier-ignore
        __root && _inheritLocale ? __root.locale.value : shared.isString(options.locale) ? options.locale : coreBase.DEFAULT_LOCALE
      );
      const _fallbackLocale = _ref(
        // prettier-ignore
        __root && _inheritLocale ? __root.fallbackLocale.value : shared.isString(options.fallbackLocale) || shared.isArray(options.fallbackLocale) || shared.isPlainObject(options.fallbackLocale) || options.fallbackLocale === false ? options.fallbackLocale : _locale.value
      );
      const _messages = _ref(getLocaleMessages(_locale.value, options));
      const _datetimeFormats = _ref(shared.isPlainObject(options.datetimeFormats) ? options.datetimeFormats : { [_locale.value]: {} });
      const _numberFormats = _ref(shared.isPlainObject(options.numberFormats) ? options.numberFormats : { [_locale.value]: {} });
      let _missingWarn = __root ? __root.missingWarn : shared.isBoolean(options.missingWarn) || shared.isRegExp(options.missingWarn) ? options.missingWarn : true;
      let _fallbackWarn = __root ? __root.fallbackWarn : shared.isBoolean(options.fallbackWarn) || shared.isRegExp(options.fallbackWarn) ? options.fallbackWarn : true;
      let _fallbackRoot = __root ? __root.fallbackRoot : shared.isBoolean(options.fallbackRoot) ? options.fallbackRoot : true;
      let _fallbackFormat = !!options.fallbackFormat;
      let _missing = shared.isFunction(options.missing) ? options.missing : null;
      let _runtimeMissing = shared.isFunction(options.missing) ? defineCoreMissingHandler(options.missing) : null;
      let _postTranslation = shared.isFunction(options.postTranslation) ? options.postTranslation : null;
      let _warnHtmlMessage = __root ? __root.warnHtmlMessage : shared.isBoolean(options.warnHtmlMessage) ? options.warnHtmlMessage : true;
      let _escapeParameter = !!options.escapeParameter;
      const _modifiers = __root ? __root.modifiers : shared.isPlainObject(options.modifiers) ? options.modifiers : {};
      let _pluralRules = options.pluralRules || __root && __root.pluralRules;
      let _context;
      const getCoreContext = () => {
        _isGlobal && coreBase.setFallbackContext(null);
        const ctxOptions = {
          version: VERSION2,
          locale: _locale.value,
          fallbackLocale: _fallbackLocale.value,
          messages: _messages.value,
          modifiers: _modifiers,
          pluralRules: _pluralRules,
          missing: _runtimeMissing === null ? void 0 : _runtimeMissing,
          missingWarn: _missingWarn,
          fallbackWarn: _fallbackWarn,
          fallbackFormat: _fallbackFormat,
          unresolving: true,
          postTranslation: _postTranslation === null ? void 0 : _postTranslation,
          warnHtmlMessage: _warnHtmlMessage,
          escapeParameter: _escapeParameter,
          messageResolver: options.messageResolver,
          messageCompiler: options.messageCompiler,
          __meta: { framework: "vue" }
        };
        {
          ctxOptions.datetimeFormats = _datetimeFormats.value;
          ctxOptions.numberFormats = _numberFormats.value;
          ctxOptions.__datetimeFormatters = shared.isPlainObject(_context) ? _context.__datetimeFormatters : void 0;
          ctxOptions.__numberFormatters = shared.isPlainObject(_context) ? _context.__numberFormatters : void 0;
        }
        {
          ctxOptions.__v_emitter = shared.isPlainObject(_context) ? _context.__v_emitter : void 0;
        }
        const ctx = coreBase.createCoreContext(ctxOptions);
        _isGlobal && coreBase.setFallbackContext(ctx);
        return ctx;
      };
      _context = getCoreContext();
      coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);
      function trackReactivityValues() {
        return [
          _locale.value,
          _fallbackLocale.value,
          _messages.value,
          _datetimeFormats.value,
          _numberFormats.value
        ];
      }
      const locale = vue.computed({
        get: () => _locale.value,
        set: (val) => {
          _locale.value = val;
          _context.locale = _locale.value;
        }
      });
      const fallbackLocale = vue.computed({
        get: () => _fallbackLocale.value,
        set: (val) => {
          _fallbackLocale.value = val;
          _context.fallbackLocale = _fallbackLocale.value;
          coreBase.updateFallbackLocale(_context, _locale.value, val);
        }
      });
      const messages = vue.computed(() => _messages.value);
      const datetimeFormats = vue.computed(() => _datetimeFormats.value);
      const numberFormats = vue.computed(() => _numberFormats.value);
      function getPostTranslationHandler() {
        return shared.isFunction(_postTranslation) ? _postTranslation : null;
      }
      function setPostTranslationHandler(handler) {
        _postTranslation = handler;
        _context.postTranslation = handler;
      }
      function getMissingHandler() {
        return _missing;
      }
      function setMissingHandler(handler) {
        if (handler !== null) {
          _runtimeMissing = defineCoreMissingHandler(handler);
        }
        _missing = handler;
        _context.missing = _runtimeMissing;
      }
      function isResolvedTranslateMessage(type, arg) {
        return type !== "translate" || !arg.resolvedMessage;
      }
      const wrapWithDeps = (fn, argumentParser, warnType, fallbackSuccess, fallbackFail, successCondition) => {
        trackReactivityValues();
        let ret;
        try {
          if (true) {
            coreBase.setAdditionalMeta(getMetaInfo());
          }
          if (!_isGlobal) {
            _context.fallbackContext = __root ? coreBase.getFallbackContext() : void 0;
          }
          ret = fn(_context);
        } finally {
          {
            coreBase.setAdditionalMeta(null);
          }
          if (!_isGlobal) {
            _context.fallbackContext = void 0;
          }
        }
        if (warnType !== "translate exists" && // for not `te` (e.g `t`)
        shared.isNumber(ret) && ret === coreBase.NOT_REOSLVED || warnType === "translate exists" && !ret) {
          const [key, arg2] = argumentParser();
          if (__root && shared.isString(key) && isResolvedTranslateMessage(warnType, arg2)) {
            if (_fallbackRoot && (coreBase.isTranslateFallbackWarn(_fallbackWarn, key) || coreBase.isTranslateMissingWarn(_missingWarn, key))) {
              shared.warn(getWarnMessage2(I18nWarnCodes.FALLBACK_TO_ROOT, {
                key,
                type: warnType
              }));
            }
            {
              const { __v_emitter: emitter } = _context;
              if (emitter && _fallbackRoot) {
                emitter.emit("fallback", {
                  type: warnType,
                  key,
                  to: "global",
                  groupId: `${warnType}:${key}`
                });
              }
            }
          }
          return __root && _fallbackRoot ? fallbackSuccess(__root) : fallbackFail(key);
        } else if (successCondition(ret)) {
          return ret;
        } else {
          throw createI18nError(I18nErrorCodes.UNEXPECTED_RETURN_TYPE);
        }
      };
      function t(...args) {
        return wrapWithDeps((context) => Reflect.apply(coreBase.translate, null, [context, ...args]), () => coreBase.parseTranslateArgs(...args), "translate", (root) => Reflect.apply(root.t, root, [...args]), (key) => key, (val) => shared.isString(val));
      }
      function rt(...args) {
        const [arg1, arg2, arg3] = args;
        if (arg3 && !shared.isObject(arg3)) {
          throw createI18nError(I18nErrorCodes.INVALID_ARGUMENT);
        }
        return t(...[arg1, arg2, shared.assign({ resolvedMessage: true }, arg3 || {})]);
      }
      function d(...args) {
        return wrapWithDeps((context) => Reflect.apply(coreBase.datetime, null, [context, ...args]), () => coreBase.parseDateTimeArgs(...args), "datetime format", (root) => Reflect.apply(root.d, root, [...args]), () => coreBase.MISSING_RESOLVE_VALUE, (val) => shared.isString(val));
      }
      function n(...args) {
        return wrapWithDeps((context) => Reflect.apply(coreBase.number, null, [context, ...args]), () => coreBase.parseNumberArgs(...args), "number format", (root) => Reflect.apply(root.n, root, [...args]), () => coreBase.MISSING_RESOLVE_VALUE, (val) => shared.isString(val));
      }
      function normalize(values) {
        return values.map((val) => shared.isString(val) || shared.isNumber(val) || shared.isBoolean(val) ? createTextNode(String(val)) : val);
      }
      const interpolate = (val) => val;
      const processor = {
        normalize,
        interpolate,
        type: "vnode"
      };
      function translateVNode(...args) {
        return wrapWithDeps(
          (context) => {
            let ret;
            const _context2 = context;
            try {
              _context2.processor = processor;
              ret = Reflect.apply(coreBase.translate, null, [_context2, ...args]);
            } finally {
              _context2.processor = null;
            }
            return ret;
          },
          () => coreBase.parseTranslateArgs(...args),
          "translate",
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (root) => root[TranslateVNodeSymbol](...args),
          (key) => [createTextNode(key)],
          (val) => shared.isArray(val)
        );
      }
      function numberParts(...args) {
        return wrapWithDeps(
          (context) => Reflect.apply(coreBase.number, null, [context, ...args]),
          () => coreBase.parseNumberArgs(...args),
          "number format",
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (root) => root[NumberPartsSymbol](...args),
          NOOP_RETURN_ARRAY,
          (val) => shared.isString(val) || shared.isArray(val)
        );
      }
      function datetimeParts(...args) {
        return wrapWithDeps(
          (context) => Reflect.apply(coreBase.datetime, null, [context, ...args]),
          () => coreBase.parseDateTimeArgs(...args),
          "datetime format",
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          (root) => root[DatetimePartsSymbol](...args),
          NOOP_RETURN_ARRAY,
          (val) => shared.isString(val) || shared.isArray(val)
        );
      }
      function setPluralRules(rules) {
        _pluralRules = rules;
        _context.pluralRules = _pluralRules;
      }
      function te(key, locale2) {
        return wrapWithDeps(() => {
          if (!key) {
            return false;
          }
          const targetLocale = shared.isString(locale2) ? locale2 : _locale.value;
          const message = getLocaleMessage(targetLocale);
          const resolved = _context.messageResolver(message, key);
          return coreBase.isMessageAST(resolved) || coreBase.isMessageFunction(resolved) || shared.isString(resolved);
        }, () => [key], "translate exists", (root) => {
          return Reflect.apply(root.te, root, [key, locale2]);
        }, NOOP_RETURN_FALSE, (val) => shared.isBoolean(val));
      }
      function resolveMessages(key) {
        let messages2 = null;
        const locales = coreBase.fallbackWithLocaleChain(_context, _fallbackLocale.value, _locale.value);
        for (let i = 0; i < locales.length; i++) {
          const targetLocaleMessages = _messages.value[locales[i]] || {};
          const messageValue = _context.messageResolver(targetLocaleMessages, key);
          if (messageValue != null) {
            messages2 = messageValue;
            break;
          }
        }
        return messages2;
      }
      function tm(key) {
        const messages2 = resolveMessages(key);
        return messages2 != null ? messages2 : __root ? __root.tm(key) || {} : {};
      }
      function getLocaleMessage(locale2) {
        return _messages.value[locale2] || {};
      }
      function setLocaleMessage(locale2, message) {
        if (flatJson) {
          const _message = { [locale2]: message };
          for (const key in _message) {
            if (shared.hasOwn(_message, key)) {
              handleFlatJson(_message[key]);
            }
          }
          message = _message[locale2];
        }
        _messages.value[locale2] = message;
        _context.messages = _messages.value;
      }
      function mergeLocaleMessage(locale2, message) {
        _messages.value[locale2] = _messages.value[locale2] || {};
        const _message = { [locale2]: message };
        if (flatJson) {
          for (const key in _message) {
            if (shared.hasOwn(_message, key)) {
              handleFlatJson(_message[key]);
            }
          }
        }
        message = _message[locale2];
        shared.deepCopy(message, _messages.value[locale2]);
        _context.messages = _messages.value;
      }
      function getDateTimeFormat(locale2) {
        return _datetimeFormats.value[locale2] || {};
      }
      function setDateTimeFormat(locale2, format3) {
        _datetimeFormats.value[locale2] = format3;
        _context.datetimeFormats = _datetimeFormats.value;
        coreBase.clearDateTimeFormat(_context, locale2, format3);
      }
      function mergeDateTimeFormat(locale2, format3) {
        _datetimeFormats.value[locale2] = shared.assign(_datetimeFormats.value[locale2] || {}, format3);
        _context.datetimeFormats = _datetimeFormats.value;
        coreBase.clearDateTimeFormat(_context, locale2, format3);
      }
      function getNumberFormat(locale2) {
        return _numberFormats.value[locale2] || {};
      }
      function setNumberFormat(locale2, format3) {
        _numberFormats.value[locale2] = format3;
        _context.numberFormats = _numberFormats.value;
        coreBase.clearNumberFormat(_context, locale2, format3);
      }
      function mergeNumberFormat(locale2, format3) {
        _numberFormats.value[locale2] = shared.assign(_numberFormats.value[locale2] || {}, format3);
        _context.numberFormats = _numberFormats.value;
        coreBase.clearNumberFormat(_context, locale2, format3);
      }
      composerID++;
      if (__root && shared.inBrowser) {
        vue.watch(__root.locale, (val) => {
          if (_inheritLocale) {
            _locale.value = val;
            _context.locale = val;
            coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);
          }
        });
        vue.watch(__root.fallbackLocale, (val) => {
          if (_inheritLocale) {
            _fallbackLocale.value = val;
            _context.fallbackLocale = val;
            coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);
          }
        });
      }
      const composer = {
        id: composerID,
        locale,
        fallbackLocale,
        get inheritLocale() {
          return _inheritLocale;
        },
        set inheritLocale(val) {
          _inheritLocale = val;
          if (val && __root) {
            _locale.value = __root.locale.value;
            _fallbackLocale.value = __root.fallbackLocale.value;
            coreBase.updateFallbackLocale(_context, _locale.value, _fallbackLocale.value);
          }
        },
        get availableLocales() {
          return Object.keys(_messages.value).sort();
        },
        messages,
        get modifiers() {
          return _modifiers;
        },
        get pluralRules() {
          return _pluralRules || {};
        },
        get isGlobal() {
          return _isGlobal;
        },
        get missingWarn() {
          return _missingWarn;
        },
        set missingWarn(val) {
          _missingWarn = val;
          _context.missingWarn = _missingWarn;
        },
        get fallbackWarn() {
          return _fallbackWarn;
        },
        set fallbackWarn(val) {
          _fallbackWarn = val;
          _context.fallbackWarn = _fallbackWarn;
        },
        get fallbackRoot() {
          return _fallbackRoot;
        },
        set fallbackRoot(val) {
          _fallbackRoot = val;
        },
        get fallbackFormat() {
          return _fallbackFormat;
        },
        set fallbackFormat(val) {
          _fallbackFormat = val;
          _context.fallbackFormat = _fallbackFormat;
        },
        get warnHtmlMessage() {
          return _warnHtmlMessage;
        },
        set warnHtmlMessage(val) {
          _warnHtmlMessage = val;
          _context.warnHtmlMessage = val;
        },
        get escapeParameter() {
          return _escapeParameter;
        },
        set escapeParameter(val) {
          _escapeParameter = val;
          _context.escapeParameter = val;
        },
        t,
        getLocaleMessage,
        setLocaleMessage,
        mergeLocaleMessage,
        getPostTranslationHandler,
        setPostTranslationHandler,
        getMissingHandler,
        setMissingHandler,
        [SetPluralRulesSymbol]: setPluralRules
      };
      {
        composer.datetimeFormats = datetimeFormats;
        composer.numberFormats = numberFormats;
        composer.rt = rt;
        composer.te = te;
        composer.tm = tm;
        composer.d = d;
        composer.n = n;
        composer.getDateTimeFormat = getDateTimeFormat;
        composer.setDateTimeFormat = setDateTimeFormat;
        composer.mergeDateTimeFormat = mergeDateTimeFormat;
        composer.getNumberFormat = getNumberFormat;
        composer.setNumberFormat = setNumberFormat;
        composer.mergeNumberFormat = mergeNumberFormat;
        composer[InejctWithOptionSymbol] = __injectWithOption;
        composer[TranslateVNodeSymbol] = translateVNode;
        composer[DatetimePartsSymbol] = datetimeParts;
        composer[NumberPartsSymbol] = numberParts;
      }
      {
        composer[EnableEmitter] = (emitter) => {
          _context.__v_emitter = emitter;
        };
        composer[DisableEmitter] = () => {
          _context.__v_emitter = void 0;
        };
      }
      return composer;
    }
    function convertComposerOptions(options) {
      const locale = shared.isString(options.locale) ? options.locale : coreBase.DEFAULT_LOCALE;
      const fallbackLocale = shared.isString(options.fallbackLocale) || shared.isArray(options.fallbackLocale) || shared.isPlainObject(options.fallbackLocale) || options.fallbackLocale === false ? options.fallbackLocale : locale;
      const missing = shared.isFunction(options.missing) ? options.missing : void 0;
      const missingWarn = shared.isBoolean(options.silentTranslationWarn) || shared.isRegExp(options.silentTranslationWarn) ? !options.silentTranslationWarn : true;
      const fallbackWarn = shared.isBoolean(options.silentFallbackWarn) || shared.isRegExp(options.silentFallbackWarn) ? !options.silentFallbackWarn : true;
      const fallbackRoot = shared.isBoolean(options.fallbackRoot) ? options.fallbackRoot : true;
      const fallbackFormat = !!options.formatFallbackMessages;
      const modifiers = shared.isPlainObject(options.modifiers) ? options.modifiers : {};
      const pluralizationRules = options.pluralizationRules;
      const postTranslation = shared.isFunction(options.postTranslation) ? options.postTranslation : void 0;
      const warnHtmlMessage = shared.isString(options.warnHtmlInMessage) ? options.warnHtmlInMessage !== "off" : true;
      const escapeParameter = !!options.escapeParameterHtml;
      const inheritLocale = shared.isBoolean(options.sync) ? options.sync : true;
      if (options.formatter) {
        shared.warn(getWarnMessage2(I18nWarnCodes.NOT_SUPPORTED_FORMATTER));
      }
      if (options.preserveDirectiveContent) {
        shared.warn(getWarnMessage2(I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE));
      }
      let messages = options.messages;
      if (shared.isPlainObject(options.sharedMessages)) {
        const sharedMessages = options.sharedMessages;
        const locales = Object.keys(sharedMessages);
        messages = locales.reduce((messages2, locale2) => {
          const message = messages2[locale2] || (messages2[locale2] = {});
          shared.assign(message, sharedMessages[locale2]);
          return messages2;
        }, messages || {});
      }
      const { __i18n, __root, __injectWithOption } = options;
      const datetimeFormats = options.datetimeFormats;
      const numberFormats = options.numberFormats;
      const flatJson = options.flatJson;
      return {
        locale,
        fallbackLocale,
        messages,
        flatJson,
        datetimeFormats,
        numberFormats,
        missing,
        missingWarn,
        fallbackWarn,
        fallbackRoot,
        fallbackFormat,
        modifiers,
        pluralRules: pluralizationRules,
        postTranslation,
        warnHtmlMessage,
        escapeParameter,
        messageResolver: options.messageResolver,
        inheritLocale,
        __i18n,
        __root,
        __injectWithOption
      };
    }
    function createVueI18n(options = {}, VueI18nLegacy) {
      {
        const composer = createComposer(convertComposerOptions(options));
        const { __extender } = options;
        const vueI18n = {
          // id
          id: composer.id,
          // locale
          get locale() {
            return composer.locale.value;
          },
          set locale(val) {
            composer.locale.value = val;
          },
          // fallbackLocale
          get fallbackLocale() {
            return composer.fallbackLocale.value;
          },
          set fallbackLocale(val) {
            composer.fallbackLocale.value = val;
          },
          // messages
          get messages() {
            return composer.messages.value;
          },
          // datetimeFormats
          get datetimeFormats() {
            return composer.datetimeFormats.value;
          },
          // numberFormats
          get numberFormats() {
            return composer.numberFormats.value;
          },
          // availableLocales
          get availableLocales() {
            return composer.availableLocales;
          },
          // formatter
          get formatter() {
            shared.warn(getWarnMessage2(I18nWarnCodes.NOT_SUPPORTED_FORMATTER));
            return {
              interpolate() {
                return [];
              }
            };
          },
          set formatter(val) {
            shared.warn(getWarnMessage2(I18nWarnCodes.NOT_SUPPORTED_FORMATTER));
          },
          // missing
          get missing() {
            return composer.getMissingHandler();
          },
          set missing(handler) {
            composer.setMissingHandler(handler);
          },
          // silentTranslationWarn
          get silentTranslationWarn() {
            return shared.isBoolean(composer.missingWarn) ? !composer.missingWarn : composer.missingWarn;
          },
          set silentTranslationWarn(val) {
            composer.missingWarn = shared.isBoolean(val) ? !val : val;
          },
          // silentFallbackWarn
          get silentFallbackWarn() {
            return shared.isBoolean(composer.fallbackWarn) ? !composer.fallbackWarn : composer.fallbackWarn;
          },
          set silentFallbackWarn(val) {
            composer.fallbackWarn = shared.isBoolean(val) ? !val : val;
          },
          // modifiers
          get modifiers() {
            return composer.modifiers;
          },
          // formatFallbackMessages
          get formatFallbackMessages() {
            return composer.fallbackFormat;
          },
          set formatFallbackMessages(val) {
            composer.fallbackFormat = val;
          },
          // postTranslation
          get postTranslation() {
            return composer.getPostTranslationHandler();
          },
          set postTranslation(handler) {
            composer.setPostTranslationHandler(handler);
          },
          // sync
          get sync() {
            return composer.inheritLocale;
          },
          set sync(val) {
            composer.inheritLocale = val;
          },
          // warnInHtmlMessage
          get warnHtmlInMessage() {
            return composer.warnHtmlMessage ? "warn" : "off";
          },
          set warnHtmlInMessage(val) {
            composer.warnHtmlMessage = val !== "off";
          },
          // escapeParameterHtml
          get escapeParameterHtml() {
            return composer.escapeParameter;
          },
          set escapeParameterHtml(val) {
            composer.escapeParameter = val;
          },
          // preserveDirectiveContent
          get preserveDirectiveContent() {
            shared.warn(getWarnMessage2(I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE));
            return true;
          },
          set preserveDirectiveContent(val) {
            shared.warn(getWarnMessage2(I18nWarnCodes.NOT_SUPPORTED_PRESERVE_DIRECTIVE));
          },
          // pluralizationRules
          get pluralizationRules() {
            return composer.pluralRules || {};
          },
          // for internal
          __composer: composer,
          // t
          t(...args) {
            const [arg1, arg2, arg3] = args;
            const options2 = {};
            let list = null;
            let named = null;
            if (!shared.isString(arg1)) {
              throw createI18nError(I18nErrorCodes.INVALID_ARGUMENT);
            }
            const key = arg1;
            if (shared.isString(arg2)) {
              options2.locale = arg2;
            } else if (shared.isArray(arg2)) {
              list = arg2;
            } else if (shared.isPlainObject(arg2)) {
              named = arg2;
            }
            if (shared.isArray(arg3)) {
              list = arg3;
            } else if (shared.isPlainObject(arg3)) {
              named = arg3;
            }
            return Reflect.apply(composer.t, composer, [
              key,
              list || named || {},
              options2
            ]);
          },
          rt(...args) {
            return Reflect.apply(composer.rt, composer, [...args]);
          },
          // tc
          tc(...args) {
            const [arg1, arg2, arg3] = args;
            const options2 = { plural: 1 };
            let list = null;
            let named = null;
            if (!shared.isString(arg1)) {
              throw createI18nError(I18nErrorCodes.INVALID_ARGUMENT);
            }
            const key = arg1;
            if (shared.isString(arg2)) {
              options2.locale = arg2;
            } else if (shared.isNumber(arg2)) {
              options2.plural = arg2;
            } else if (shared.isArray(arg2)) {
              list = arg2;
            } else if (shared.isPlainObject(arg2)) {
              named = arg2;
            }
            if (shared.isString(arg3)) {
              options2.locale = arg3;
            } else if (shared.isArray(arg3)) {
              list = arg3;
            } else if (shared.isPlainObject(arg3)) {
              named = arg3;
            }
            return Reflect.apply(composer.t, composer, [
              key,
              list || named || {},
              options2
            ]);
          },
          // te
          te(key, locale) {
            return composer.te(key, locale);
          },
          // tm
          tm(key) {
            return composer.tm(key);
          },
          // getLocaleMessage
          getLocaleMessage(locale) {
            return composer.getLocaleMessage(locale);
          },
          // setLocaleMessage
          setLocaleMessage(locale, message) {
            composer.setLocaleMessage(locale, message);
          },
          // mergeLocaleMessage
          mergeLocaleMessage(locale, message) {
            composer.mergeLocaleMessage(locale, message);
          },
          // d
          d(...args) {
            return Reflect.apply(composer.d, composer, [...args]);
          },
          // getDateTimeFormat
          getDateTimeFormat(locale) {
            return composer.getDateTimeFormat(locale);
          },
          // setDateTimeFormat
          setDateTimeFormat(locale, format3) {
            composer.setDateTimeFormat(locale, format3);
          },
          // mergeDateTimeFormat
          mergeDateTimeFormat(locale, format3) {
            composer.mergeDateTimeFormat(locale, format3);
          },
          // n
          n(...args) {
            return Reflect.apply(composer.n, composer, [...args]);
          },
          // getNumberFormat
          getNumberFormat(locale) {
            return composer.getNumberFormat(locale);
          },
          // setNumberFormat
          setNumberFormat(locale, format3) {
            composer.setNumberFormat(locale, format3);
          },
          // mergeNumberFormat
          mergeNumberFormat(locale, format3) {
            composer.mergeNumberFormat(locale, format3);
          },
          // getChoiceIndex
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          getChoiceIndex(choice, choicesLength) {
            shared.warn(getWarnMessage2(I18nWarnCodes.NOT_SUPPORTED_GET_CHOICE_INDEX));
            return -1;
          }
        };
        vueI18n.__extender = __extender;
        {
          vueI18n.__enableEmitter = (emitter) => {
            const __composer = composer;
            __composer[EnableEmitter] && __composer[EnableEmitter](emitter);
          };
          vueI18n.__disableEmitter = () => {
            const __composer = composer;
            __composer[DisableEmitter] && __composer[DisableEmitter]();
          };
        }
        return vueI18n;
      }
    }
    var baseFormatProps = {
      tag: {
        type: [String, Object]
      },
      locale: {
        type: String
      },
      scope: {
        type: String,
        // NOTE: avoid https://github.com/microsoft/rushstack/issues/1050
        validator: (val) => val === "parent" || val === "global",
        default: "parent"
        /* ComponentI18nScope */
      },
      i18n: {
        type: Object
      }
    };
    function getInterpolateArg({ slots }, keys) {
      if (keys.length === 1 && keys[0] === "default") {
        const ret = slots.default ? slots.default() : [];
        return ret.reduce((slot, current) => {
          return [
            ...slot,
            // prettier-ignore
            ...current.type === vue.Fragment ? current.children : [current]
          ];
        }, []);
      } else {
        return keys.reduce((arg, key) => {
          const slot = slots[key];
          if (slot) {
            arg[key] = slot();
          }
          return arg;
        }, {});
      }
    }
    function getFragmentableTag(tag) {
      return vue.Fragment;
    }
    var TranslationImpl = vue.defineComponent({
      /* eslint-disable */
      name: "i18n-t",
      props: shared.assign({
        keypath: {
          type: String,
          required: true
        },
        plural: {
          type: [Number, String],
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          validator: (val) => shared.isNumber(val) || !isNaN(val)
        }
      }, baseFormatProps),
      /* eslint-enable */
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      setup(props, context) {
        const { slots, attrs } = context;
        const i18n = props.i18n || useI18n({
          useScope: props.scope,
          __useComponent: true
        });
        return () => {
          const keys = Object.keys(slots).filter((key) => key !== "_");
          const options = {};
          if (props.locale) {
            options.locale = props.locale;
          }
          if (props.plural !== void 0) {
            options.plural = shared.isString(props.plural) ? +props.plural : props.plural;
          }
          const arg = getInterpolateArg(context, keys);
          const children = i18n[TranslateVNodeSymbol](props.keypath, arg, options);
          const assignedAttrs = shared.assign({}, attrs);
          const tag = shared.isString(props.tag) || shared.isObject(props.tag) ? props.tag : getFragmentableTag();
          return vue.h(tag, assignedAttrs, children);
        };
      }
    });
    var Translation = TranslationImpl;
    var I18nT = Translation;
    function isVNode(target) {
      return shared.isArray(target) && !shared.isString(target[0]);
    }
    function renderFormatter(props, context, slotKeys, partFormatter) {
      const { slots, attrs } = context;
      return () => {
        const options = { part: true };
        let overrides = {};
        if (props.locale) {
          options.locale = props.locale;
        }
        if (shared.isString(props.format)) {
          options.key = props.format;
        } else if (shared.isObject(props.format)) {
          if (shared.isString(props.format.key)) {
            options.key = props.format.key;
          }
          overrides = Object.keys(props.format).reduce((options2, prop) => {
            return slotKeys.includes(prop) ? shared.assign({}, options2, { [prop]: props.format[prop] }) : options2;
          }, {});
        }
        const parts = partFormatter(...[props.value, options, overrides]);
        let children = [options.key];
        if (shared.isArray(parts)) {
          children = parts.map((part, index) => {
            const slot = slots[part.type];
            const node = slot ? slot({ [part.type]: part.value, index, parts }) : [part.value];
            if (isVNode(node)) {
              node[0].key = `${part.type}-${index}`;
            }
            return node;
          });
        } else if (shared.isString(parts)) {
          children = [parts];
        }
        const assignedAttrs = shared.assign({}, attrs);
        const tag = shared.isString(props.tag) || shared.isObject(props.tag) ? props.tag : getFragmentableTag();
        return vue.h(tag, assignedAttrs, children);
      };
    }
    var NumberFormatImpl = vue.defineComponent({
      /* eslint-disable */
      name: "i18n-n",
      props: shared.assign({
        value: {
          type: Number,
          required: true
        },
        format: {
          type: [String, Object]
        }
      }, baseFormatProps),
      /* eslint-enable */
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      setup(props, context) {
        const i18n = props.i18n || useI18n({
          useScope: "parent",
          __useComponent: true
        });
        return renderFormatter(props, context, coreBase.NUMBER_FORMAT_OPTIONS_KEYS, (...args) => (
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          i18n[NumberPartsSymbol](...args)
        ));
      }
    });
    var NumberFormat = NumberFormatImpl;
    var I18nN = NumberFormat;
    var DatetimeFormatImpl = vue.defineComponent({
      /* eslint-disable */
      name: "i18n-d",
      props: shared.assign({
        value: {
          type: [Number, Date],
          required: true
        },
        format: {
          type: [String, Object]
        }
      }, baseFormatProps),
      /* eslint-enable */
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      setup(props, context) {
        const i18n = props.i18n || useI18n({
          useScope: "parent",
          __useComponent: true
        });
        return renderFormatter(props, context, coreBase.DATETIME_FORMAT_OPTIONS_KEYS, (...args) => (
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          i18n[DatetimePartsSymbol](...args)
        ));
      }
    });
    var DatetimeFormat = DatetimeFormatImpl;
    var I18nD = DatetimeFormat;
    function getComposer$1(i18n, instance) {
      const i18nInternal = i18n;
      if (i18n.mode === "composition") {
        return i18nInternal.__getInstance(instance) || i18n.global;
      } else {
        const vueI18n = i18nInternal.__getInstance(instance);
        return vueI18n != null ? vueI18n.__composer : i18n.global.__composer;
      }
    }
    function vTDirective(i18n) {
      const _process = (binding) => {
        const { instance, modifiers, value } = binding;
        if (!instance || !instance.$) {
          throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);
        }
        const composer = getComposer$1(i18n, instance.$);
        if (modifiers.preserve) {
          shared.warn(getWarnMessage2(I18nWarnCodes.NOT_SUPPORTED_PRESERVE));
        }
        const parsedValue = parseValue(value);
        return [
          Reflect.apply(composer.t, composer, [...makeParams(parsedValue)]),
          composer
        ];
      };
      const register = (el, binding) => {
        const [textContent, composer] = _process(binding);
        if (shared.inBrowser && i18n.global === composer) {
          el.__i18nWatcher = vue.watch(composer.locale, () => {
            binding.instance && binding.instance.$forceUpdate();
          });
        }
        el.__composer = composer;
        el.textContent = textContent;
      };
      const unregister = (el) => {
        if (shared.inBrowser && el.__i18nWatcher) {
          el.__i18nWatcher();
          el.__i18nWatcher = void 0;
          delete el.__i18nWatcher;
        }
        if (el.__composer) {
          el.__composer = void 0;
          delete el.__composer;
        }
      };
      const update = (el, { value }) => {
        if (el.__composer) {
          const composer = el.__composer;
          const parsedValue = parseValue(value);
          el.textContent = Reflect.apply(composer.t, composer, [
            ...makeParams(parsedValue)
          ]);
        }
      };
      const getSSRProps = (binding) => {
        const [textContent] = _process(binding);
        return { textContent };
      };
      return {
        created: register,
        unmounted: unregister,
        beforeUpdate: update,
        getSSRProps
      };
    }
    function parseValue(value) {
      if (shared.isString(value)) {
        return { path: value };
      } else if (shared.isPlainObject(value)) {
        if (!("path" in value)) {
          throw createI18nError(I18nErrorCodes.REQUIRED_VALUE, "path");
        }
        return value;
      } else {
        throw createI18nError(I18nErrorCodes.INVALID_VALUE);
      }
    }
    function makeParams(value) {
      const { path, locale, args, choice, plural } = value;
      const options = {};
      const named = args || {};
      if (shared.isString(locale)) {
        options.locale = locale;
      }
      if (shared.isNumber(choice)) {
        options.plural = choice;
      }
      if (shared.isNumber(plural)) {
        options.plural = plural;
      }
      return [path, named, options];
    }
    function apply(app, i18n, ...options) {
      const pluginOptions = shared.isPlainObject(options[0]) ? options[0] : {};
      const useI18nComponentName = !!pluginOptions.useI18nComponentName;
      const globalInstall = shared.isBoolean(pluginOptions.globalInstall) ? pluginOptions.globalInstall : true;
      if (globalInstall && useI18nComponentName) {
        shared.warn(getWarnMessage2(I18nWarnCodes.COMPONENT_NAME_LEGACY_COMPATIBLE, {
          name: Translation.name
        }));
      }
      if (globalInstall) {
        [!useI18nComponentName ? Translation.name : "i18n", "I18nT"].forEach((name) => app.component(name, Translation));
        [NumberFormat.name, "I18nN"].forEach((name) => app.component(name, NumberFormat));
        [DatetimeFormat.name, "I18nD"].forEach((name) => app.component(name, DatetimeFormat));
      }
      {
        app.directive("t", vTDirective(i18n));
      }
    }
    function defineMixin(vuei18n, composer, i18n) {
      return {
        beforeCreate() {
          const instance = vue.getCurrentInstance();
          if (!instance) {
            throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);
          }
          const options = this.$options;
          if (options.i18n) {
            const optionsI18n = options.i18n;
            if (options.__i18n) {
              optionsI18n.__i18n = options.__i18n;
            }
            optionsI18n.__root = composer;
            if (this === this.$root) {
              this.$i18n = mergeToGlobal(vuei18n, optionsI18n);
            } else {
              optionsI18n.__injectWithOption = true;
              optionsI18n.__extender = i18n.__vueI18nExtend;
              this.$i18n = createVueI18n(optionsI18n);
              const _vueI18n = this.$i18n;
              if (_vueI18n.__extender) {
                _vueI18n.__disposer = _vueI18n.__extender(this.$i18n);
              }
            }
          } else if (options.__i18n) {
            if (this === this.$root) {
              this.$i18n = mergeToGlobal(vuei18n, options);
            } else {
              this.$i18n = createVueI18n({
                __i18n: options.__i18n,
                __injectWithOption: true,
                __extender: i18n.__vueI18nExtend,
                __root: composer
              });
              const _vueI18n = this.$i18n;
              if (_vueI18n.__extender) {
                _vueI18n.__disposer = _vueI18n.__extender(this.$i18n);
              }
            }
          } else {
            this.$i18n = vuei18n;
          }
          if (options.__i18nGlobal) {
            adjustI18nResources(composer, options, options);
          }
          this.$t = (...args) => this.$i18n.t(...args);
          this.$rt = (...args) => this.$i18n.rt(...args);
          this.$tc = (...args) => this.$i18n.tc(...args);
          this.$te = (key, locale) => this.$i18n.te(key, locale);
          this.$d = (...args) => this.$i18n.d(...args);
          this.$n = (...args) => this.$i18n.n(...args);
          this.$tm = (key) => this.$i18n.tm(key);
          i18n.__setInstance(instance, this.$i18n);
        },
        mounted() {
        },
        unmounted() {
          const instance = vue.getCurrentInstance();
          if (!instance) {
            throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);
          }
          const _vueI18n = this.$i18n;
          delete this.$t;
          delete this.$rt;
          delete this.$tc;
          delete this.$te;
          delete this.$d;
          delete this.$n;
          delete this.$tm;
          if (_vueI18n.__disposer) {
            _vueI18n.__disposer();
            delete _vueI18n.__disposer;
            delete _vueI18n.__extender;
          }
          i18n.__deleteInstance(instance);
          delete this.$i18n;
        }
      };
    }
    function mergeToGlobal(g, options) {
      g.locale = options.locale || g.locale;
      g.fallbackLocale = options.fallbackLocale || g.fallbackLocale;
      g.missing = options.missing || g.missing;
      g.silentTranslationWarn = options.silentTranslationWarn || g.silentFallbackWarn;
      g.silentFallbackWarn = options.silentFallbackWarn || g.silentFallbackWarn;
      g.formatFallbackMessages = options.formatFallbackMessages || g.formatFallbackMessages;
      g.postTranslation = options.postTranslation || g.postTranslation;
      g.warnHtmlInMessage = options.warnHtmlInMessage || g.warnHtmlInMessage;
      g.escapeParameterHtml = options.escapeParameterHtml || g.escapeParameterHtml;
      g.sync = options.sync || g.sync;
      g.__composer[SetPluralRulesSymbol](options.pluralizationRules || g.pluralizationRules);
      const messages = getLocaleMessages(g.locale, {
        messages: options.messages,
        __i18n: options.__i18n
      });
      Object.keys(messages).forEach((locale) => g.mergeLocaleMessage(locale, messages[locale]));
      if (options.datetimeFormats) {
        Object.keys(options.datetimeFormats).forEach((locale) => g.mergeDateTimeFormat(locale, options.datetimeFormats[locale]));
      }
      if (options.numberFormats) {
        Object.keys(options.numberFormats).forEach((locale) => g.mergeNumberFormat(locale, options.numberFormats[locale]));
      }
      return g;
    }
    var I18nInjectionKey = shared.makeSymbol("global-vue-i18n");
    function createI18n(options = {}, VueI18nLegacy) {
      const __legacyMode = shared.isBoolean(options.legacy) ? options.legacy : true;
      const __globalInjection = shared.isBoolean(options.globalInjection) ? options.globalInjection : true;
      const __allowComposition = __legacyMode ? !!options.allowComposition : true;
      const __instances = /* @__PURE__ */ new Map();
      const [globalScope, __global] = createGlobal(options, __legacyMode);
      const symbol = shared.makeSymbol("vue-i18n");
      {
        if (__legacyMode && __allowComposition && true) {
          shared.warn(getWarnMessage2(I18nWarnCodes.NOTICE_DROP_ALLOW_COMPOSITION));
        }
      }
      function __getInstance(component) {
        return __instances.get(component) || null;
      }
      function __setInstance(component, instance) {
        __instances.set(component, instance);
      }
      function __deleteInstance(component) {
        __instances.delete(component);
      }
      {
        const i18n = {
          // mode
          get mode() {
            return __legacyMode ? "legacy" : "composition";
          },
          // allowComposition
          get allowComposition() {
            return __allowComposition;
          },
          // install plugin
          async install(app, ...options2) {
            app.__VUE_I18N_SYMBOL__ = symbol;
            app.provide(app.__VUE_I18N_SYMBOL__, i18n);
            if (shared.isPlainObject(options2[0])) {
              const opts = options2[0];
              i18n.__composerExtend = opts.__composerExtend;
              i18n.__vueI18nExtend = opts.__vueI18nExtend;
            }
            let globalReleaseHandler = null;
            if (!__legacyMode && __globalInjection) {
              globalReleaseHandler = injectGlobalFields(app, i18n.global);
            }
            {
              apply(app, i18n, ...options2);
            }
            if (__legacyMode) {
              app.mixin(defineMixin(__global, __global.__composer, i18n));
            }
            const unmountApp = app.unmount;
            app.unmount = () => {
              globalReleaseHandler && globalReleaseHandler();
              i18n.dispose();
              unmountApp();
            };
          },
          // global accessor
          get global() {
            return __global;
          },
          dispose() {
            globalScope.stop();
          },
          // @internal
          __instances,
          // @internal
          __getInstance,
          // @internal
          __setInstance,
          // @internal
          __deleteInstance
        };
        return i18n;
      }
    }
    function useI18n(options = {}) {
      const instance = vue.getCurrentInstance();
      if (instance == null) {
        throw createI18nError(I18nErrorCodes.MUST_BE_CALL_SETUP_TOP);
      }
      if (!instance.isCE && instance.appContext.app != null && !instance.appContext.app.__VUE_I18N_SYMBOL__) {
        throw createI18nError(I18nErrorCodes.NOT_INSTALLED);
      }
      const i18n = getI18nInstance(instance);
      const gl = getGlobalComposer(i18n);
      const componentOptions = getComponentOptions(instance);
      const scope = getScope(options, componentOptions);
      {
        if (i18n.mode === "legacy" && !options.__useComponent) {
          if (!i18n.allowComposition) {
            throw createI18nError(I18nErrorCodes.NOT_AVAILABLE_IN_LEGACY_MODE);
          }
          return useI18nForLegacy(instance, scope, gl, options);
        }
      }
      if (scope === "global") {
        adjustI18nResources(gl, options, componentOptions);
        return gl;
      }
      if (scope === "parent") {
        let composer2 = getComposer(i18n, instance, options.__useComponent);
        if (composer2 == null) {
          {
            shared.warn(getWarnMessage2(I18nWarnCodes.NOT_FOUND_PARENT_SCOPE));
          }
          composer2 = gl;
        }
        return composer2;
      }
      const i18nInternal = i18n;
      let composer = i18nInternal.__getInstance(instance);
      if (composer == null) {
        const composerOptions = shared.assign({}, options);
        if ("__i18n" in componentOptions) {
          composerOptions.__i18n = componentOptions.__i18n;
        }
        if (gl) {
          composerOptions.__root = gl;
        }
        composer = createComposer(composerOptions);
        if (i18nInternal.__composerExtend) {
          composer[DisposeSymbol] = i18nInternal.__composerExtend(composer);
        }
        setupLifeCycle(i18nInternal, instance, composer);
        i18nInternal.__setInstance(instance, composer);
      }
      return composer;
    }
    var castToVueI18n = (i18n) => {
      if (!(__VUE_I18N_BRIDGE__ in i18n)) {
        throw createI18nError(I18nErrorCodes.NOT_COMPATIBLE_LEGACY_VUE_I18N);
      }
      return i18n;
    };
    function createGlobal(options, legacyMode, VueI18nLegacy) {
      const scope = vue.effectScope();
      {
        const obj = legacyMode ? scope.run(() => createVueI18n(options)) : scope.run(() => createComposer(options));
        if (obj == null) {
          throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);
        }
        return [scope, obj];
      }
    }
    function getI18nInstance(instance) {
      {
        const i18n = vue.inject(!instance.isCE ? instance.appContext.app.__VUE_I18N_SYMBOL__ : I18nInjectionKey);
        if (!i18n) {
          throw createI18nError(!instance.isCE ? I18nErrorCodes.UNEXPECTED_ERROR : I18nErrorCodes.NOT_INSTALLED_WITH_PROVIDE);
        }
        return i18n;
      }
    }
    function getScope(options, componentOptions) {
      return shared.isEmptyObject(options) ? "__i18n" in componentOptions ? "local" : "global" : !options.useScope ? "local" : options.useScope;
    }
    function getGlobalComposer(i18n) {
      return i18n.mode === "composition" ? i18n.global : i18n.global.__composer;
    }
    function getComposer(i18n, target, useComponent = false) {
      let composer = null;
      const root = target.root;
      let current = getParentComponentInstance(target, useComponent);
      while (current != null) {
        const i18nInternal = i18n;
        if (i18n.mode === "composition") {
          composer = i18nInternal.__getInstance(current);
        } else {
          {
            const vueI18n = i18nInternal.__getInstance(current);
            if (vueI18n != null) {
              composer = vueI18n.__composer;
              if (useComponent && composer && !composer[InejctWithOptionSymbol]) {
                composer = null;
              }
            }
          }
        }
        if (composer != null) {
          break;
        }
        if (root === current) {
          break;
        }
        current = current.parent;
      }
      return composer;
    }
    function getParentComponentInstance(target, useComponent = false) {
      if (target == null) {
        return null;
      }
      {
        return !useComponent ? target.parent : target.vnode.ctx || target.parent;
      }
    }
    function setupLifeCycle(i18n, target, composer) {
      {
        vue.onMounted(() => {
        }, target);
        vue.onUnmounted(() => {
          const _composer = composer;
          i18n.__deleteInstance(target);
          const dispose = _composer[DisposeSymbol];
          if (dispose) {
            dispose();
            delete _composer[DisposeSymbol];
          }
        }, target);
      }
    }
    function useI18nForLegacy(instance, scope, root, options = {}) {
      const isLocalScope = scope === "local";
      const _composer = vue.shallowRef(null);
      if (isLocalScope && instance.proxy && !(instance.proxy.$options.i18n || instance.proxy.$options.__i18n)) {
        throw createI18nError(I18nErrorCodes.MUST_DEFINE_I18N_OPTION_IN_ALLOW_COMPOSITION);
      }
      const _inheritLocale = shared.isBoolean(options.inheritLocale) ? options.inheritLocale : !shared.isString(options.locale);
      const _locale = vue.ref(
        // prettier-ignore
        !isLocalScope || _inheritLocale ? root.locale.value : shared.isString(options.locale) ? options.locale : coreBase.DEFAULT_LOCALE
      );
      const _fallbackLocale = vue.ref(
        // prettier-ignore
        !isLocalScope || _inheritLocale ? root.fallbackLocale.value : shared.isString(options.fallbackLocale) || shared.isArray(options.fallbackLocale) || shared.isPlainObject(options.fallbackLocale) || options.fallbackLocale === false ? options.fallbackLocale : _locale.value
      );
      const _messages = vue.ref(getLocaleMessages(_locale.value, options));
      const _datetimeFormats = vue.ref(shared.isPlainObject(options.datetimeFormats) ? options.datetimeFormats : { [_locale.value]: {} });
      const _numberFormats = vue.ref(shared.isPlainObject(options.numberFormats) ? options.numberFormats : { [_locale.value]: {} });
      const _missingWarn = isLocalScope ? root.missingWarn : shared.isBoolean(options.missingWarn) || shared.isRegExp(options.missingWarn) ? options.missingWarn : true;
      const _fallbackWarn = isLocalScope ? root.fallbackWarn : shared.isBoolean(options.fallbackWarn) || shared.isRegExp(options.fallbackWarn) ? options.fallbackWarn : true;
      const _fallbackRoot = isLocalScope ? root.fallbackRoot : shared.isBoolean(options.fallbackRoot) ? options.fallbackRoot : true;
      const _fallbackFormat = !!options.fallbackFormat;
      const _missing = shared.isFunction(options.missing) ? options.missing : null;
      const _postTranslation = shared.isFunction(options.postTranslation) ? options.postTranslation : null;
      const _warnHtmlMessage = isLocalScope ? root.warnHtmlMessage : shared.isBoolean(options.warnHtmlMessage) ? options.warnHtmlMessage : true;
      const _escapeParameter = !!options.escapeParameter;
      const _modifiers = isLocalScope ? root.modifiers : shared.isPlainObject(options.modifiers) ? options.modifiers : {};
      const _pluralRules = options.pluralRules || isLocalScope && root.pluralRules;
      function trackReactivityValues() {
        return [
          _locale.value,
          _fallbackLocale.value,
          _messages.value,
          _datetimeFormats.value,
          _numberFormats.value
        ];
      }
      const locale = vue.computed({
        get: () => {
          return _composer.value ? _composer.value.locale.value : _locale.value;
        },
        set: (val) => {
          if (_composer.value) {
            _composer.value.locale.value = val;
          }
          _locale.value = val;
        }
      });
      const fallbackLocale = vue.computed({
        get: () => {
          return _composer.value ? _composer.value.fallbackLocale.value : _fallbackLocale.value;
        },
        set: (val) => {
          if (_composer.value) {
            _composer.value.fallbackLocale.value = val;
          }
          _fallbackLocale.value = val;
        }
      });
      const messages = vue.computed(() => {
        if (_composer.value) {
          return _composer.value.messages.value;
        } else {
          return _messages.value;
        }
      });
      const datetimeFormats = vue.computed(() => _datetimeFormats.value);
      const numberFormats = vue.computed(() => _numberFormats.value);
      function getPostTranslationHandler() {
        return _composer.value ? _composer.value.getPostTranslationHandler() : _postTranslation;
      }
      function setPostTranslationHandler(handler) {
        if (_composer.value) {
          _composer.value.setPostTranslationHandler(handler);
        }
      }
      function getMissingHandler() {
        return _composer.value ? _composer.value.getMissingHandler() : _missing;
      }
      function setMissingHandler(handler) {
        if (_composer.value) {
          _composer.value.setMissingHandler(handler);
        }
      }
      function warpWithDeps(fn) {
        trackReactivityValues();
        return fn();
      }
      function t(...args) {
        return _composer.value ? warpWithDeps(() => Reflect.apply(_composer.value.t, null, [...args])) : warpWithDeps(() => "");
      }
      function rt(...args) {
        return _composer.value ? Reflect.apply(_composer.value.rt, null, [...args]) : "";
      }
      function d(...args) {
        return _composer.value ? warpWithDeps(() => Reflect.apply(_composer.value.d, null, [...args])) : warpWithDeps(() => "");
      }
      function n(...args) {
        return _composer.value ? warpWithDeps(() => Reflect.apply(_composer.value.n, null, [...args])) : warpWithDeps(() => "");
      }
      function tm(key) {
        return _composer.value ? _composer.value.tm(key) : {};
      }
      function te(key, locale2) {
        return _composer.value ? _composer.value.te(key, locale2) : false;
      }
      function getLocaleMessage(locale2) {
        return _composer.value ? _composer.value.getLocaleMessage(locale2) : {};
      }
      function setLocaleMessage(locale2, message) {
        if (_composer.value) {
          _composer.value.setLocaleMessage(locale2, message);
          _messages.value[locale2] = message;
        }
      }
      function mergeLocaleMessage(locale2, message) {
        if (_composer.value) {
          _composer.value.mergeLocaleMessage(locale2, message);
        }
      }
      function getDateTimeFormat(locale2) {
        return _composer.value ? _composer.value.getDateTimeFormat(locale2) : {};
      }
      function setDateTimeFormat(locale2, format3) {
        if (_composer.value) {
          _composer.value.setDateTimeFormat(locale2, format3);
          _datetimeFormats.value[locale2] = format3;
        }
      }
      function mergeDateTimeFormat(locale2, format3) {
        if (_composer.value) {
          _composer.value.mergeDateTimeFormat(locale2, format3);
        }
      }
      function getNumberFormat(locale2) {
        return _composer.value ? _composer.value.getNumberFormat(locale2) : {};
      }
      function setNumberFormat(locale2, format3) {
        if (_composer.value) {
          _composer.value.setNumberFormat(locale2, format3);
          _numberFormats.value[locale2] = format3;
        }
      }
      function mergeNumberFormat(locale2, format3) {
        if (_composer.value) {
          _composer.value.mergeNumberFormat(locale2, format3);
        }
      }
      const wrapper = {
        get id() {
          return _composer.value ? _composer.value.id : -1;
        },
        locale,
        fallbackLocale,
        messages,
        datetimeFormats,
        numberFormats,
        get inheritLocale() {
          return _composer.value ? _composer.value.inheritLocale : _inheritLocale;
        },
        set inheritLocale(val) {
          if (_composer.value) {
            _composer.value.inheritLocale = val;
          }
        },
        get availableLocales() {
          return _composer.value ? _composer.value.availableLocales : Object.keys(_messages.value);
        },
        get modifiers() {
          return _composer.value ? _composer.value.modifiers : _modifiers;
        },
        get pluralRules() {
          return _composer.value ? _composer.value.pluralRules : _pluralRules;
        },
        get isGlobal() {
          return _composer.value ? _composer.value.isGlobal : false;
        },
        get missingWarn() {
          return _composer.value ? _composer.value.missingWarn : _missingWarn;
        },
        set missingWarn(val) {
          if (_composer.value) {
            _composer.value.missingWarn = val;
          }
        },
        get fallbackWarn() {
          return _composer.value ? _composer.value.fallbackWarn : _fallbackWarn;
        },
        set fallbackWarn(val) {
          if (_composer.value) {
            _composer.value.missingWarn = val;
          }
        },
        get fallbackRoot() {
          return _composer.value ? _composer.value.fallbackRoot : _fallbackRoot;
        },
        set fallbackRoot(val) {
          if (_composer.value) {
            _composer.value.fallbackRoot = val;
          }
        },
        get fallbackFormat() {
          return _composer.value ? _composer.value.fallbackFormat : _fallbackFormat;
        },
        set fallbackFormat(val) {
          if (_composer.value) {
            _composer.value.fallbackFormat = val;
          }
        },
        get warnHtmlMessage() {
          return _composer.value ? _composer.value.warnHtmlMessage : _warnHtmlMessage;
        },
        set warnHtmlMessage(val) {
          if (_composer.value) {
            _composer.value.warnHtmlMessage = val;
          }
        },
        get escapeParameter() {
          return _composer.value ? _composer.value.escapeParameter : _escapeParameter;
        },
        set escapeParameter(val) {
          if (_composer.value) {
            _composer.value.escapeParameter = val;
          }
        },
        t,
        getPostTranslationHandler,
        setPostTranslationHandler,
        getMissingHandler,
        setMissingHandler,
        rt,
        d,
        n,
        tm,
        te,
        getLocaleMessage,
        setLocaleMessage,
        mergeLocaleMessage,
        getDateTimeFormat,
        setDateTimeFormat,
        mergeDateTimeFormat,
        getNumberFormat,
        setNumberFormat,
        mergeNumberFormat
      };
      function sync(composer) {
        composer.locale.value = _locale.value;
        composer.fallbackLocale.value = _fallbackLocale.value;
        Object.keys(_messages.value).forEach((locale2) => {
          composer.mergeLocaleMessage(locale2, _messages.value[locale2]);
        });
        Object.keys(_datetimeFormats.value).forEach((locale2) => {
          composer.mergeDateTimeFormat(locale2, _datetimeFormats.value[locale2]);
        });
        Object.keys(_numberFormats.value).forEach((locale2) => {
          composer.mergeNumberFormat(locale2, _numberFormats.value[locale2]);
        });
        composer.escapeParameter = _escapeParameter;
        composer.fallbackFormat = _fallbackFormat;
        composer.fallbackRoot = _fallbackRoot;
        composer.fallbackWarn = _fallbackWarn;
        composer.missingWarn = _missingWarn;
        composer.warnHtmlMessage = _warnHtmlMessage;
      }
      vue.onBeforeMount(() => {
        if (instance.proxy == null || instance.proxy.$i18n == null) {
          throw createI18nError(I18nErrorCodes.NOT_AVAILABLE_COMPOSITION_IN_LEGACY);
        }
        const composer = _composer.value = instance.proxy.$i18n.__composer;
        if (scope === "global") {
          _locale.value = composer.locale.value;
          _fallbackLocale.value = composer.fallbackLocale.value;
          _messages.value = composer.messages.value;
          _datetimeFormats.value = composer.datetimeFormats.value;
          _numberFormats.value = composer.numberFormats.value;
        } else if (isLocalScope) {
          sync(composer);
        }
      });
      return wrapper;
    }
    var globalExportProps = [
      "locale",
      "fallbackLocale",
      "availableLocales"
    ];
    var globalExportMethods = ["t", "rt", "d", "n", "tm", "te"];
    function injectGlobalFields(app, composer) {
      const i18n = /* @__PURE__ */ Object.create(null);
      globalExportProps.forEach((prop) => {
        const desc = Object.getOwnPropertyDescriptor(composer, prop);
        if (!desc) {
          throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);
        }
        const wrap = vue.isRef(desc.value) ? {
          get() {
            return desc.value.value;
          },
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          set(val) {
            desc.value.value = val;
          }
        } : {
          get() {
            return desc.get && desc.get();
          }
        };
        Object.defineProperty(i18n, prop, wrap);
      });
      app.config.globalProperties.$i18n = i18n;
      globalExportMethods.forEach((method) => {
        const desc = Object.getOwnPropertyDescriptor(composer, method);
        if (!desc || !desc.value) {
          throw createI18nError(I18nErrorCodes.UNEXPECTED_ERROR);
        }
        Object.defineProperty(app.config.globalProperties, `$${method}`, desc);
      });
      const dispose = () => {
        delete app.config.globalProperties.$i18n;
        globalExportMethods.forEach((method) => {
          delete app.config.globalProperties[`$${method}`];
        });
      };
      return dispose;
    }
    {
      coreBase.registerMessageCompiler(coreBase.compile);
    }
    coreBase.registerMessageResolver(coreBase.resolveValue);
    coreBase.registerLocaleFallbacker(coreBase.fallbackWithLocaleChain);
    {
      const target = shared.getGlobalThis();
      target.__INTLIFY__ = true;
      coreBase.setDevToolsHook(target.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__);
    }
    exports.DatetimeFormat = DatetimeFormat;
    exports.I18nD = I18nD;
    exports.I18nInjectionKey = I18nInjectionKey;
    exports.I18nN = I18nN;
    exports.I18nT = I18nT;
    exports.NumberFormat = NumberFormat;
    exports.Translation = Translation;
    exports.VERSION = VERSION2;
    exports.castToVueI18n = castToVueI18n;
    exports.createI18n = createI18n;
    exports.useI18n = useI18n;
    exports.vTDirective = vTDirective;
  }
});

// node_modules/vue-i18n/dist/vue-i18n.cjs.js
var require_vue_i18n_cjs = __commonJS({
  "node_modules/vue-i18n/dist/vue-i18n.cjs.js"(exports, module) {
    module.exports = require_vue_i18n();
  }
});
export default require_vue_i18n_cjs();
/*! Bundled license information:

@intlify/shared/dist/shared.esm-browser.js:
  (*!
    * shared v9.9.1
    * (c) 2024 kazuya kawaguchi
    * Released under the MIT License.
    *)

@intlify/core-base/dist/core-base.esm-browser.js:
  (*!
    * core-base v9.9.1
    * (c) 2024 kazuya kawaguchi
    * Released under the MIT License.
    *)

vue-i18n/dist/vue-i18n.cjs:
  (*!
    * vue-i18n v9.9.1
    * (c) 2024 kazuya kawaguchi
    * Released under the MIT License.
    *)
*/
//# sourceMappingURL=vue-i18n.js.map
