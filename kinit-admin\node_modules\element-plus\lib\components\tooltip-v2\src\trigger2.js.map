{"version": 3, "file": "trigger2.js", "sources": ["../../../../../../packages/components/tooltip-v2/src/trigger.vue"], "sourcesContent": ["<template>\n  <forward-ref v-if=\"nowrap\" :set-ref=\"setTriggerRef\" only-child>\n    <slot />\n  </forward-ref>\n  <button v-else ref=\"triggerRef\" v-bind=\"$attrs\">\n    <slot />\n  </button>\n</template>\n\n<script setup lang=\"ts\">\nimport { inject, onBeforeUnmount, watch } from 'vue'\nimport { composeEventHandlers } from '@element-plus/utils'\nimport { tooltipV2RootKey } from './constants'\nimport ForwardRef from './forward-ref'\nimport { tooltipV2TriggerProps } from './trigger'\nimport { tooltipV2CommonProps } from './common'\n\ndefineOptions({\n  name: 'ElTooltipV2Trigger',\n})\n\nconst props = defineProps({\n  ...tooltipV2CommonProps,\n  ...tooltipV2TriggerProps,\n})\n\n/**\n * onOpen opens the tooltip instantly, onTrigger acts a lil bit differently,\n * it will check if delayDuration is set to greater than 0 and based on that result,\n * if true, it opens the tooltip after delayDuration, otherwise it opens it instantly.\n */\nconst { onClose, onOpen, onDelayOpen, triggerRef, contentId } =\n  inject(tooltipV2RootKey)!\n\nlet isMousedown = false\n\nconst setTriggerRef = (el: HTMLElement | null) => {\n  triggerRef.value = el\n}\n\nconst onMouseup = () => {\n  isMousedown = false\n}\n\nconst onMouseenter = composeEventHandlers(props.onMouseEnter, onDelayOpen)\n\nconst onMouseleave = composeEventHandlers(props.onMouseLeave, onClose)\n\nconst onMousedown = composeEventHandlers(props.onMouseDown, () => {\n  onClose()\n  isMousedown = true\n  document.addEventListener('mouseup', onMouseup, { once: true })\n})\n\nconst onFocus = composeEventHandlers(props.onFocus, () => {\n  if (!isMousedown) onOpen()\n})\n\nconst onBlur = composeEventHandlers(props.onBlur, onClose)\n\nconst onClick = composeEventHandlers(props.onClick, (e) => {\n  if ((e as MouseEvent).detail === 0) onClose()\n})\n\nconst events = {\n  blur: onBlur,\n  click: onClick,\n  focus: onFocus,\n  mousedown: onMousedown,\n  mouseenter: onMouseenter,\n  mouseleave: onMouseleave,\n}\n\nconst setEvents = <T extends (e: Event) => void>(\n  el: HTMLElement | null | undefined,\n  events: Record<string, T>,\n  type: 'addEventListener' | 'removeEventListener'\n) => {\n  if (el) {\n    Object.entries(events).forEach(([name, handler]) => {\n      el[type](name, handler)\n    })\n  }\n}\n\nwatch(triggerRef, (triggerEl, previousTriggerEl) => {\n  setEvents(triggerEl, events, 'addEventListener')\n  setEvents(previousTriggerEl, events, 'removeEventListener')\n\n  if (triggerEl) {\n    triggerEl.setAttribute('aria-describedby', contentId.value)\n  }\n})\n\nonBeforeUnmount(() => {\n  setEvents(triggerRef.value, events, 'removeEventListener')\n  document.removeEventListener('mouseup', onMouseup)\n})\n</script>\n"], "names": ["inject", "tooltipV2RootKey", "composeEventHandlers", "watch", "onBeforeUnmount"], "mappings": ";;;;;;;;;;;;;uCAiBc,CAAA;AAAA,EACZ,IAAM,EAAA,oBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;;;AAYA,IAAA,MAAM,EAAE,OAAS,EAAA,MAAA,EAAQ,aAAa,UAAY,EAAA,SAAA,EAAA,GAChDA,WAAOC,0BAAgB,CAAA,CAAA;AAEzB,IAAA,IAAI,WAAc,GAAA,KAAA,CAAA;AAElB,IAAM,MAAA,aAAA,GAAgB,CAAC,EAA2B,KAAA;AAChD,MAAA,UAAA,CAAW,KAAQ,GAAA,EAAA,CAAA;AAAA,KACrB,CAAA;AAEA,IAAA,MAAM,YAAY,MAAM;AACtB,MAAc,WAAA,GAAA,KAAA,CAAA;AAAA,KAChB,CAAA;AAEA,IAAA,MAAM,YAAe,GAAAC,0BAAA,CAAqB,KAAM,CAAA,YAAA,EAAc,WAAW,CAAA,CAAA;AAEzE,IAAA,MAAM,YAAe,GAAAA,0BAAA,CAAqB,KAAM,CAAA,YAAA,EAAc,OAAO,CAAA,CAAA;AAErE,IAAA,MAAM,WAAc,GAAAA,0BAAA,CAAqB,KAAM,CAAA,WAAA,EAAa,MAAM;AAChE,MAAQ,OAAA,EAAA,CAAA;AACR,MAAc,WAAA,GAAA,IAAA,CAAA;AACd,MAAA,QAAA,CAAS,iBAAiB,SAAW,EAAA,SAAA,EAAW,EAAE,IAAA,EAAM,MAAM,CAAA,CAAA;AAAA,KAC/D,CAAA,CAAA;AAED,IAAA,MAAM,OAAU,GAAAA,0BAAA,CAAqB,KAAM,CAAA,OAAA,EAAS,MAAM;AACxD,MAAA,IAAI,CAAC,WAAA;AAAa,QAAO,MAAA,EAAA,CAAA;AAAA,KAC1B,CAAA,CAAA;AAED,IAAA,MAAM,MAAS,GAAAA,0BAAA,CAAqB,KAAM,CAAA,MAAA,EAAQ,OAAO,CAAA,CAAA;AAEzD,IAAA,MAAM,OAAU,GAAAA,0BAAA,CAAqB,KAAM,CAAA,OAAA,EAAS,CAAC,CAAM,KAAA;AACzD,MAAA,IAAK,EAAiB,MAAW,KAAA,CAAA;AAAG,QAAQ,OAAA,EAAA,CAAA;AAAA,KAC7C,CAAA,CAAA;AAED,IAAA,MAAM,MAAS,GAAA;AAAA,MACb,IAAM,EAAA,MAAA;AAAA,MACN,KAAO,EAAA,OAAA;AAAA,MACP,KAAO,EAAA,OAAA;AAAA,MACP,SAAW,EAAA,WAAA;AAAA,MACX,UAAY,EAAA,YAAA;AAAA,MACZ,UAAY,EAAA,YAAA;AAAA,KACd,CAAA;AAEA,IAAA,MAAM,SAAY,GAAA,CAChB,EACA,EAAA,OAAA,EACA,IACG,KAAA;AACH,MAAA,IAAI,EAAI,EAAA;AACN,QAAA,MAAA,CAAO,QAAQ,OAAM,CAAA,CAAE,QAAQ,CAAC,CAAC,MAAM,OAAa,CAAA,KAAA;AAClD,UAAG,EAAA,CAAA,IAAA,CAAA,CAAM,MAAM,OAAO,CAAA,CAAA;AAAA,SACvB,CAAA,CAAA;AAAA,OACH;AAAA,KACF,CAAA;AAEA,IAAMC,SAAA,CAAA,UAAA,EAAY,CAAC,SAAA,EAAW,iBAAsB,KAAA;AAClD,MAAU,SAAA,CAAA,SAAA,EAAW,QAAQ,kBAAkB,CAAA,CAAA;AAC/C,MAAU,SAAA,CAAA,iBAAA,EAAmB,QAAQ,qBAAqB,CAAA,CAAA;AAE1D,MAAA,IAAI,SAAW,EAAA;AACb,QAAU,SAAA,CAAA,YAAA,CAAa,kBAAoB,EAAA,SAAA,CAAU,KAAK,CAAA,CAAA;AAAA,OAC5D;AAAA,KACD,CAAA,CAAA;AAED,IAAAC,mBAAA,CAAgB,MAAM;AACpB,MAAU,SAAA,CAAA,UAAA,CAAW,KAAO,EAAA,MAAA,EAAQ,qBAAqB,CAAA,CAAA;AACzD,MAAS,QAAA,CAAA,mBAAA,CAAoB,WAAW,SAAS,CAAA,CAAA;AAAA,KAClD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;"}