{"version": 3, "file": "page-header2.js", "sources": ["../../../../../../packages/components/page-header/src/page-header.vue"], "sourcesContent": ["<template>\n  <div :class=\"kls\">\n    <div v-if=\"$slots.breadcrumb\" :class=\"ns.e('breadcrumb')\">\n      <slot name=\"breadcrumb\" />\n    </div>\n    <div :class=\"ns.e('header')\">\n      <div :class=\"ns.e('left')\">\n        <div\n          :class=\"ns.e('back')\"\n          role=\"button\"\n          tabindex=\"0\"\n          @click=\"handleClick\"\n        >\n          <div\n            v-if=\"icon || $slots.icon\"\n            :aria-label=\"title || t('el.pageHeader.title')\"\n            :class=\"ns.e('icon')\"\n          >\n            <slot name=\"icon\">\n              <el-icon v-if=\"icon\">\n                <component :is=\"icon\" />\n              </el-icon>\n            </slot>\n          </div>\n          <div :class=\"ns.e('title')\">\n            <slot name=\"title\">{{ title || t('el.pageHeader.title') }}</slot>\n          </div>\n        </div>\n        <el-divider direction=\"vertical\" />\n        <div :class=\"ns.e('content')\">\n          <slot name=\"content\">{{ content }}</slot>\n        </div>\n      </div>\n\n      <div v-if=\"$slots.extra\" :class=\"ns.e('extra')\">\n        <slot name=\"extra\" />\n      </div>\n    </div>\n\n    <div v-if=\"$slots.default\" :class=\"ns.e('main')\">\n      <slot />\n    </div>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { computed, useSlots } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { ElDivider } from '@element-plus/components/divider'\n\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { pageHeaderEmits, pageHeaderProps } from './page-header'\n\ndefineOptions({\n  name: 'ElPageHeader',\n})\n\ndefineProps(pageHeaderProps)\nconst emit = defineEmits(pageHeaderEmits)\nconst slots = useSlots()\n\nconst { t } = useLocale()\nconst ns = useNamespace('page-header')\nconst kls = computed(() => {\n  return [\n    ns.b(),\n    {\n      [ns.m('has-breadcrumb')]: !!slots.breadcrumb,\n      [ns.m('has-extra')]: !!slots.extra,\n      [ns.is('contentful')]: !!slots.default,\n    },\n  ]\n})\n\nfunction handleClick() {\n  emit('back')\n}\n</script>\n"], "names": ["useSlots", "useLocale", "useNamespace", "computed"], "mappings": ";;;;;;;;;;;;;;uCAoDc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAIA,IAAA,MAAM,QAAQA,YAAS,EAAA,CAAA;AAEvB,IAAM,MAAA,EAAE,MAAMC,eAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAKC,qBAAa,aAAa,CAAA,CAAA;AACrC,IAAM,MAAA,GAAA,GAAMC,aAAS,MAAM;AACzB,MAAO,OAAA;AAAA,QACL,GAAG,CAAE,EAAA;AAAA,QACL;AAAA,UACE,CAAC,EAAG,CAAA,CAAA,CAAE,gBAAgB,CAAI,GAAA,CAAC,CAAC,KAAM,CAAA,UAAA;AAAA,UAClC,CAAC,EAAG,CAAA,CAAA,CAAE,WAAW,CAAI,GAAA,CAAC,CAAC,KAAM,CAAA,KAAA;AAAA,UAC7B,CAAC,EAAG,CAAA,EAAA,CAAG,YAAY,CAAI,GAAA,CAAC,CAAC,KAAM,CAAA,OAAA;AAAA,SACjC;AAAA,OACF,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAuB,SAAA,WAAA,GAAA;AACrB,MAAA,IAAA,CAAK,MAAM,CAAA,CAAA;AAAA,KACb;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}