{"version": 3, "file": "rate2.mjs", "sources": ["../../../../../../packages/components/rate/src/rate.vue"], "sourcesContent": ["<template>\n  <div\n    :id=\"inputId\"\n    :class=\"[rateClasses, ns.is('disabled', rateDisabled)]\"\n    role=\"slider\"\n    :aria-label=\"!isLabeledByFormItem ? label || 'rating' : undefined\"\n    :aria-labelledby=\"\n      isLabeledByFormItem ? formItemContext?.labelId : undefined\n    \"\n    :aria-valuenow=\"currentValue\"\n    :aria-valuetext=\"text || undefined\"\n    aria-valuemin=\"0\"\n    :aria-valuemax=\"max\"\n    tabindex=\"0\"\n    :style=\"rateStyles\"\n    @keydown=\"handleKey\"\n  >\n    <span\n      v-for=\"(item, key) in max\"\n      :key=\"key\"\n      :class=\"ns.e('item')\"\n      @mousemove=\"setCurrentValue(item, $event)\"\n      @mouseleave=\"resetCurrentValue\"\n      @click=\"selectValue(item)\"\n    >\n      <el-icon\n        :class=\"[\n          ns.e('icon'),\n          { hover: hoverIndex === item },\n          ns.is('active', item <= currentValue),\n        ]\"\n      >\n        <template v-if=\"!showDecimalIcon(item)\">\n          <component :is=\"activeComponent\" v-show=\"item <= currentValue\" />\n          <component :is=\"voidComponent\" v-show=\"!(item <= currentValue)\" />\n        </template>\n        <template v-if=\"showDecimalIcon(item)\">\n          <component :is=\"voidComponent\" :class=\"[ns.em('decimal', 'box')]\" />\n          <el-icon\n            :style=\"decimalStyle\"\n            :class=\"[ns.e('icon'), ns.e('decimal')]\"\n          >\n            <component :is=\"decimalIconComponent\" />\n          </el-icon>\n        </template>\n      </el-icon>\n    </span>\n    <span\n      v-if=\"showText || showScore\"\n      :class=\"ns.e('text')\"\n      :style=\"{ color: textColor }\"\n    >\n      {{ text }}\n    </span>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { computed, inject, markRaw, ref, watch } from 'vue'\nimport { EVENT_CODE, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport { hasClass, isArray, isObject, isString } from '@element-plus/utils'\nimport {\n  formContextKey,\n  formItemContextKey,\n  useFormItemInputId,\n  useFormSize,\n} from '@element-plus/components/form'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { useNamespace } from '@element-plus/hooks'\nimport { rateEmits, rateProps } from './rate'\nimport type { CSSProperties, Component } from 'vue'\n\nfunction getValueFromMap<T>(\n  value: number,\n  map: Record<string, T | { excluded?: boolean; value: T }>\n) {\n  const isExcludedObject = (\n    val: unknown\n  ): val is { excluded?: boolean } & Record<any, unknown> => isObject(val)\n\n  const matchedKeys = Object.keys(map)\n    .map((key) => +key)\n    .filter((key) => {\n      const val = map[key]\n      const excluded = isExcludedObject(val) ? val.excluded : false\n      return excluded ? value < key : value <= key\n    })\n    .sort((a, b) => a - b)\n  const matchedValue = map[matchedKeys[0]]\n  return (isExcludedObject(matchedValue) && matchedValue.value) || matchedValue\n}\n\ndefineOptions({\n  name: 'ElRate',\n})\n\nconst props = defineProps(rateProps)\nconst emit = defineEmits(rateEmits)\n\nconst formContext = inject(formContextKey, undefined)\nconst formItemContext = inject(formItemContextKey, undefined)\nconst rateSize = useFormSize()\nconst ns = useNamespace('rate')\nconst { inputId, isLabeledByFormItem } = useFormItemInputId(props, {\n  formItemContext,\n})\n\nconst currentValue = ref(props.modelValue)\nconst hoverIndex = ref(-1)\nconst pointerAtLeftHalf = ref(true)\n\nconst rateClasses = computed(() => [ns.b(), ns.m(rateSize.value)])\nconst rateDisabled = computed(() => props.disabled || formContext?.disabled)\nconst rateStyles = computed(() => {\n  return ns.cssVarBlock({\n    'void-color': props.voidColor,\n    'disabled-void-color': props.disabledVoidColor,\n    'fill-color': activeColor.value,\n  }) as CSSProperties\n})\n\nconst text = computed(() => {\n  let result = ''\n  if (props.showScore) {\n    result = props.scoreTemplate.replace(\n      /\\{\\s*value\\s*\\}/,\n      rateDisabled.value ? `${props.modelValue}` : `${currentValue.value}`\n    )\n  } else if (props.showText) {\n    result = props.texts[Math.ceil(currentValue.value) - 1]\n  }\n  return result\n})\nconst valueDecimal = computed(\n  () => props.modelValue * 100 - Math.floor(props.modelValue) * 100\n)\nconst colorMap = computed(() =>\n  isArray(props.colors)\n    ? {\n        [props.lowThreshold]: props.colors[0],\n        [props.highThreshold]: { value: props.colors[1], excluded: true },\n        [props.max]: props.colors[2],\n      }\n    : props.colors\n)\nconst activeColor = computed(() => {\n  const color = getValueFromMap(currentValue.value, colorMap.value)\n  // {value: '', excluded: true} returned\n  return isObject(color) ? '' : color\n})\nconst decimalStyle = computed(() => {\n  let width = ''\n  if (rateDisabled.value) {\n    width = `${valueDecimal.value}%`\n  } else if (props.allowHalf) {\n    width = '50%'\n  }\n  return {\n    color: activeColor.value,\n    width,\n  }\n})\nconst componentMap = computed(() => {\n  let icons = isArray(props.icons) ? [...props.icons] : { ...props.icons }\n  icons = markRaw(icons) as\n    | Array<string | Component>\n    | Record<number, string | Component>\n  return isArray(icons)\n    ? {\n        [props.lowThreshold]: icons[0],\n        [props.highThreshold]: {\n          value: icons[1],\n          excluded: true,\n        },\n        [props.max]: icons[2],\n      }\n    : icons\n})\nconst decimalIconComponent = computed(() =>\n  getValueFromMap(props.modelValue, componentMap.value)\n)\nconst voidComponent = computed(() =>\n  rateDisabled.value\n    ? isString(props.disabledVoidIcon)\n      ? props.disabledVoidIcon\n      : (markRaw(props.disabledVoidIcon) as Component)\n    : isString(props.voidIcon)\n    ? props.voidIcon\n    : (markRaw(props.voidIcon) as Component)\n)\nconst activeComponent = computed(() =>\n  getValueFromMap(currentValue.value, componentMap.value)\n)\n\nfunction showDecimalIcon(item: number) {\n  const showWhenDisabled =\n    rateDisabled.value &&\n    valueDecimal.value > 0 &&\n    item - 1 < props.modelValue &&\n    item > props.modelValue\n  const showWhenAllowHalf =\n    props.allowHalf &&\n    pointerAtLeftHalf.value &&\n    item - 0.5 <= currentValue.value &&\n    item > currentValue.value\n  return showWhenDisabled || showWhenAllowHalf\n}\n\nfunction emitValue(value: number) {\n  // if allow clear, and selected value is same as modelValue, reset value to 0\n  if (props.clearable && value === props.modelValue) {\n    value = 0\n  }\n\n  emit(UPDATE_MODEL_EVENT, value)\n  if (props.modelValue !== value) {\n    emit('change', value)\n  }\n}\n\nfunction selectValue(value: number) {\n  if (rateDisabled.value) {\n    return\n  }\n  if (props.allowHalf && pointerAtLeftHalf.value) {\n    emitValue(currentValue.value)\n  } else {\n    emitValue(value)\n  }\n}\n\nfunction handleKey(e: KeyboardEvent) {\n  if (rateDisabled.value) {\n    return\n  }\n  let _currentValue = currentValue.value\n  const code = e.code\n  if (code === EVENT_CODE.up || code === EVENT_CODE.right) {\n    if (props.allowHalf) {\n      _currentValue += 0.5\n    } else {\n      _currentValue += 1\n    }\n    e.stopPropagation()\n    e.preventDefault()\n  } else if (code === EVENT_CODE.left || code === EVENT_CODE.down) {\n    if (props.allowHalf) {\n      _currentValue -= 0.5\n    } else {\n      _currentValue -= 1\n    }\n    e.stopPropagation()\n    e.preventDefault()\n  }\n  _currentValue = _currentValue < 0 ? 0 : _currentValue\n  _currentValue = _currentValue > props.max ? props.max : _currentValue\n  emit(UPDATE_MODEL_EVENT, _currentValue)\n  emit('change', _currentValue)\n  return _currentValue\n}\n\nfunction setCurrentValue(value: number, event?: MouseEvent) {\n  if (rateDisabled.value) {\n    return\n  }\n  if (props.allowHalf && event) {\n    // TODO: use cache via computed https://github.com/element-plus/element-plus/pull/5456#discussion_r786472092\n    let target = event.target as HTMLElement\n    if (hasClass(target, ns.e('item'))) {\n      target = target.querySelector(`.${ns.e('icon')}`)!\n    }\n    if (target.clientWidth === 0 || hasClass(target, ns.e('decimal'))) {\n      target = target.parentNode as HTMLElement\n    }\n    pointerAtLeftHalf.value = event.offsetX * 2 <= target.clientWidth\n    currentValue.value = pointerAtLeftHalf.value ? value - 0.5 : value\n  } else {\n    currentValue.value = value\n  }\n  hoverIndex.value = value\n}\n\nfunction resetCurrentValue() {\n  if (rateDisabled.value) {\n    return\n  }\n  if (props.allowHalf) {\n    pointerAtLeftHalf.value = props.modelValue !== Math.floor(props.modelValue)\n  }\n  currentValue.value = props.modelValue\n  hoverIndex.value = -1\n}\n\nwatch(\n  () => props.modelValue,\n  (val) => {\n    currentValue.value = val\n    pointerAtLeftHalf.value = props.modelValue !== Math.floor(props.modelValue)\n  }\n)\n\nif (!props.modelValue) {\n  emit(UPDATE_MODEL_EVENT, 0)\n}\n\ndefineExpose({\n  /** @description set current value */\n  setCurrentValue,\n  /** @description reset current value */\n  resetCurrentValue,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;mCA2Fc,CAAA;AAAA,EACZ,IAAM,EAAA,QAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAtBA,IAAA,SAAA,eAAA,CACE,OACA,GACA,EAAA;AACA,MAAA,MAAM,gBAAmB,GAAA,CACvB,GACyD,KAAA,QAAA,CAAS,GAAG,CAAA,CAAA;AAEvE,MAAA,MAAM,WAAc,GAAA,MAAA,CAAO,IAAK,CAAA,GAAG,CAChC,CAAA,GAAA,CAAI,CAAC,GAAA,KAAQ,CAAC,GAAG,CACjB,CAAA,MAAA,CAAO,CAAC,GAAQ,KAAA;AACf,QAAA,MAAM,MAAM,GAAI,CAAA,GAAA,CAAA,CAAA;AAChB,QAAA,MAAM,QAAW,GAAA,gBAAA,CAAiB,GAAG,CAAA,GAAI,IAAI,QAAW,GAAA,KAAA,CAAA;AACxD,QAAO,OAAA,QAAA,GAAW,KAAQ,GAAA,GAAA,GAAM,KAAS,IAAA,GAAA,CAAA;AAAA,OAC1C,CACA,CAAA,IAAA,CAAK,CAAC,CAAG,EAAA,CAAA,KAAM,IAAI,CAAC,CAAA,CAAA;AACvB,MAAM,MAAA,YAAA,GAAe,IAAI,WAAY,CAAA,CAAA,CAAA,CAAA,CAAA;AACrC,MAAA,OAAQ,gBAAiB,CAAA,YAAY,CAAK,IAAA,YAAA,CAAa,KAAU,IAAA,YAAA,CAAA;AAAA,KACnE;AASA,IAAM,MAAA,WAAA,GAAc,MAAO,CAAA,cAAA,EAAgB,KAAS,CAAA,CAAA,CAAA;AACpD,IAAM,MAAA,eAAA,GAAkB,MAAO,CAAA,kBAAA,EAAoB,KAAS,CAAA,CAAA,CAAA;AAC5D,IAAA,MAAM,WAAW,WAAY,EAAA,CAAA;AAC7B,IAAM,MAAA,EAAA,GAAK,aAAa,MAAM,CAAA,CAAA;AAC9B,IAAA,MAAM,EAAE,OAAA,EAAS,mBAAwB,EAAA,GAAA,kBAAA,CAAmB,KAAO,EAAA;AAAA,MACjE,eAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAe,GAAI,CAAA,KAAA,CAAM,UAAU,CAAA,CAAA;AACzC,IAAM,MAAA,UAAA,GAAa,IAAI,CAAE,CAAA,CAAA,CAAA;AACzB,IAAM,MAAA,iBAAA,GAAoB,IAAI,IAAI,CAAA,CAAA;AAElC,IAAA,MAAM,WAAc,GAAA,QAAA,CAAS,MAAM,CAAC,EAAG,CAAA,CAAA,EAAK,EAAA,EAAA,CAAG,CAAE,CAAA,QAAA,CAAS,KAAK,CAAC,CAAC,CAAA,CAAA;AACjE,IAAA,MAAM,eAAe,QAAS,CAAA,MAAM,KAAM,CAAA,QAAA,oBAAiC,IAAA,GAAA,KAAA,CAAA,GAAA,WAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAC3E,IAAM,MAAA,UAAA,GAAa,SAAS,MAAM;AAChC,MAAA,OAAO,GAAG,WAAY,CAAA;AAAA,QACpB,cAAc,KAAM,CAAA,SAAA;AAAA,QACpB,uBAAuB,KAAM,CAAA,iBAAA;AAAA,QAC7B,cAAc,WAAY,CAAA,KAAA;AAAA,OAC3B,CAAA,CAAA;AAAA,KACF,CAAA,CAAA;AAED,IAAM,MAAA,IAAA,GAAO,SAAS,MAAM;AAC1B,MAAA,IAAI,MAAS,GAAA,EAAA,CAAA;AACb,MAAA,IAAI,MAAM,SAAW,EAAA;AACnB,QAAS,MAAA,GAAA,KAAA,CAAM,aAAc,CAAA,OAAA,CAC3B,iBACA,EAAA,YAAA,CAAa,KAAQ,GAAA,CAAA,EAAG,KAAM,CAAA,UAAA,CAAA,CAAA,GAAe,CAAG,EAAA,YAAA,CAAa,KAC/D,CAAA,CAAA,CAAA,CAAA;AAAA,OACF,MAAA,IAAW,MAAM,QAAU,EAAA;AACzB,QAAA,MAAA,GAAS,MAAM,KAAM,CAAA,IAAA,CAAK,IAAK,CAAA,YAAA,CAAa,KAAK,CAAI,GAAA,CAAA,CAAA,CAAA;AAAA,OACvD;AACA,MAAO,OAAA,MAAA,CAAA;AAAA,KACR,CAAA,CAAA;AACD,IAAM,MAAA,YAAA,GAAe,QACnB,CAAA,MAAM,KAAM,CAAA,UAAA,GAAa,GAAM,GAAA,IAAA,CAAK,KAAM,CAAA,KAAA,CAAM,UAAU,CAAA,GAAI,GAChE,CAAA,CAAA;AACA,IAAA,MAAM,WAAW,QAAS,CAAA,MACxB,OAAQ,CAAA,KAAA,CAAM,MAAM,CAChB,GAAA;AAAA,MACE,CAAC,KAAA,CAAM,YAAe,GAAA,KAAA,CAAM,MAAO,CAAA,CAAA,CAAA;AAAA,MACnC,CAAC,MAAM,aAAgB,GAAA,EAAE,OAAO,KAAM,CAAA,MAAA,CAAO,CAAI,CAAA,EAAA,QAAA,EAAU,IAAK,EAAA;AAAA,MAChE,CAAC,KAAA,CAAM,GAAM,GAAA,KAAA,CAAM,MAAO,CAAA,CAAA,CAAA;AAAA,KAC5B,GACA,MAAM,MACZ,CAAA,CAAA;AACA,IAAM,MAAA,WAAA,GAAc,SAAS,MAAM;AACjC,MAAA,MAAM,KAAQ,GAAA,eAAA,CAAgB,YAAa,CAAA,KAAA,EAAO,SAAS,KAAK,CAAA,CAAA;AAEhE,MAAO,OAAA,QAAA,CAAS,KAAK,CAAA,GAAI,EAAK,GAAA,KAAA,CAAA;AAAA,KAC/B,CAAA,CAAA;AACD,IAAM,MAAA,YAAA,GAAe,SAAS,MAAM;AAClC,MAAA,IAAI,KAAQ,GAAA,EAAA,CAAA;AACZ,MAAA,IAAI,aAAa,KAAO,EAAA;AACtB,QAAA,KAAA,GAAQ,GAAG,YAAa,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AAAA,OAC1B,MAAA,IAAW,MAAM,SAAW,EAAA;AAC1B,QAAQ,KAAA,GAAA,KAAA,CAAA;AAAA,OACV;AACA,MAAO,OAAA;AAAA,QACL,OAAO,WAAY,CAAA,KAAA;AAAA,QACnB,KAAA;AAAA,OACF,CAAA;AAAA,KACD,CAAA,CAAA;AACD,IAAM,MAAA,YAAA,GAAe,SAAS,MAAM;AAClC,MAAA,IAAI,KAAQ,GAAA,OAAA,CAAQ,KAAM,CAAA,KAAK,CAAI,GAAA,CAAC,GAAG,KAAA,CAAM,KAAK,CAAA,GAAI,EAAE,GAAG,MAAM,KAAM,EAAA,CAAA;AACvE,MAAA,KAAA,GAAQ,QAAQ,KAAK,CAAA,CAAA;AAGrB,MAAO,OAAA,OAAA,CAAQ,KAAK,CAChB,GAAA;AAAA,QACE,CAAC,KAAM,CAAA,YAAA,GAAe,KAAM,CAAA,CAAA,CAAA;AAAA,QAC5B,CAAC,MAAM,aAAgB,GAAA;AAAA,UACrB,OAAO,KAAM,CAAA,CAAA,CAAA;AAAA,UACb,QAAU,EAAA,IAAA;AAAA,SACZ;AAAA,QACA,CAAC,KAAM,CAAA,GAAA,GAAM,KAAM,CAAA,CAAA,CAAA;AAAA,OAErB,GAAA,KAAA,CAAA;AAAA,KACL,CAAA,CAAA;AACD,IAAM,MAAA,oBAAA,GAAuB,SAAS,MACpC,eAAA,CAAgB,MAAM,UAAY,EAAA,YAAA,CAAa,KAAK,CACtD,CAAA,CAAA;AACA,IAAM,MAAA,aAAA,GAAgB,QAAS,CAAA,MAC7B,YAAa,CAAA,KAAA,GACT,SAAS,KAAM,CAAA,gBAAgB,CAC7B,GAAA,KAAA,CAAM,gBACL,GAAA,OAAA,CAAQ,MAAM,gBAAgB,CAAA,GACjC,QAAS,CAAA,KAAA,CAAM,QAAQ,CAAA,GACvB,MAAM,QACL,GAAA,OAAA,CAAQ,KAAM,CAAA,QAAQ,CAC7B,CAAA,CAAA;AACA,IAAM,MAAA,eAAA,GAAkB,SAAS,MAC/B,eAAA,CAAgB,aAAa,KAAO,EAAA,YAAA,CAAa,KAAK,CACxD,CAAA,CAAA;AAEA,IAAA,SAAA,eAAA,CAAyB,IAAc,EAAA;AACrC,MAAM,MAAA,gBAAA,GACJ,YAAa,CAAA,KAAA,IACb,YAAa,CAAA,KAAA,GAAQ,CACrB,IAAA,IAAA,GAAO,CAAI,GAAA,KAAA,CAAM,UACjB,IAAA,IAAA,GAAO,KAAM,CAAA,UAAA,CAAA;AACf,MAAM,MAAA,iBAAA,GACJ,KAAM,CAAA,SAAA,IACN,iBAAkB,CAAA,KAAA,IAClB,OAAO,GAAO,IAAA,YAAA,CAAa,KAC3B,IAAA,IAAA,GAAO,YAAa,CAAA,KAAA,CAAA;AACtB,MAAA,OAAO,gBAAoB,IAAA,iBAAA,CAAA;AAAA,KAC7B;AAEA,IAAA,SAAA,SAAA,CAAmB,KAAe,EAAA;AAEhC,MAAA,IAAI,KAAM,CAAA,SAAA,IAAa,KAAU,KAAA,KAAA,CAAM,UAAY,EAAA;AACjD,QAAQ,KAAA,GAAA,CAAA,CAAA;AAAA,OACV;AAEA,MAAA,IAAA,CAAK,oBAAoB,KAAK,CAAA,CAAA;AAC9B,MAAI,IAAA,KAAA,CAAM,eAAe,KAAO,EAAA;AAC9B,QAAA,IAAA,CAAK,UAAU,KAAK,CAAA,CAAA;AAAA,OACtB;AAAA,KACF;AAEA,IAAA,SAAA,WAAA,CAAqB,KAAe,EAAA;AAClC,MAAA,IAAI,aAAa,KAAO,EAAA;AACtB,QAAA,OAAA;AAAA,OACF;AACA,MAAI,IAAA,KAAA,CAAM,SAAa,IAAA,iBAAA,CAAkB,KAAO,EAAA;AAC9C,QAAA,SAAA,CAAU,aAAa,KAAK,CAAA,CAAA;AAAA,OACvB,MAAA;AACL,QAAA,SAAA,CAAU,KAAK,CAAA,CAAA;AAAA,OACjB;AAAA,KACF;AAEA,IAAA,SAAA,SAAA,CAAmB,CAAkB,EAAA;AACnC,MAAA,IAAI,aAAa,KAAO,EAAA;AACtB,QAAA,OAAA;AAAA,OACF;AACA,MAAA,IAAI,gBAAgB,YAAa,CAAA,KAAA,CAAA;AACjC,MAAA,MAAM,OAAO,CAAE,CAAA,IAAA,CAAA;AACf,MAAA,IAAI,IAAS,KAAA,UAAA,CAAW,EAAM,IAAA,IAAA,KAAS,WAAW,KAAO,EAAA;AACvD,QAAA,IAAI,MAAM,SAAW,EAAA;AACnB,UAAiB,aAAA,IAAA,GAAA,CAAA;AAAA,SACZ,MAAA;AACL,UAAiB,aAAA,IAAA,CAAA,CAAA;AAAA,SACnB;AACA,QAAA,CAAA,CAAE,eAAgB,EAAA,CAAA;AAClB,QAAA,CAAA,CAAE,cAAe,EAAA,CAAA;AAAA,iBACR,IAAS,KAAA,UAAA,CAAW,IAAQ,IAAA,IAAA,KAAS,WAAW,IAAM,EAAA;AAC/D,QAAA,IAAI,MAAM,SAAW,EAAA;AACnB,UAAiB,aAAA,IAAA,GAAA,CAAA;AAAA,SACZ,MAAA;AACL,UAAiB,aAAA,IAAA,CAAA,CAAA;AAAA,SACnB;AACA,QAAA,CAAA,CAAE,eAAgB,EAAA,CAAA;AAClB,QAAA,CAAA,CAAE,cAAe,EAAA,CAAA;AAAA,OACnB;AACA,MAAgB,aAAA,GAAA,aAAA,GAAgB,IAAI,CAAI,GAAA,aAAA,CAAA;AACxC,MAAA,aAAA,GAAgB,aAAgB,GAAA,KAAA,CAAM,GAAM,GAAA,KAAA,CAAM,GAAM,GAAA,aAAA,CAAA;AACxD,MAAA,IAAA,CAAK,oBAAoB,aAAa,CAAA,CAAA;AACtC,MAAA,IAAA,CAAK,UAAU,aAAa,CAAA,CAAA;AAC5B,MAAO,OAAA,aAAA,CAAA;AAAA,KACT;AAEA,IAAA,SAAA,eAAA,CAAyB,OAAe,KAAoB,EAAA;AAC1D,MAAA,IAAI,aAAa,KAAO,EAAA;AACtB,QAAA,OAAA;AAAA,OACF;AACA,MAAI,IAAA,KAAA,CAAM,aAAa,KAAO,EAAA;AAE5B,QAAA,IAAI,SAAS,KAAM,CAAA,MAAA,CAAA;AACnB,QAAA,IAAI,SAAS,MAAQ,EAAA,EAAA,CAAG,CAAE,CAAA,MAAM,CAAC,CAAG,EAAA;AAClC,UAAA,MAAA,GAAS,OAAO,aAAc,CAAA,CAAA,CAAA,EAAI,EAAG,CAAA,CAAA,CAAE,MAAM,CAAG,CAAA,CAAA,CAAA,CAAA;AAAA,SAClD;AACA,QAAI,IAAA,MAAA,CAAO,gBAAgB,CAAK,IAAA,QAAA,CAAS,QAAQ,EAAG,CAAA,CAAA,CAAE,SAAS,CAAC,CAAG,EAAA;AACjE,UAAA,MAAA,GAAS,MAAO,CAAA,UAAA,CAAA;AAAA,SAClB;AACA,QAAA,iBAAA,CAAkB,KAAQ,GAAA,KAAA,CAAM,OAAU,GAAA,CAAA,IAAK,MAAO,CAAA,WAAA,CAAA;AACtD,QAAA,YAAA,CAAa,KAAQ,GAAA,iBAAA,CAAkB,KAAQ,GAAA,KAAA,GAAQ,GAAM,GAAA,KAAA,CAAA;AAAA,OACxD,MAAA;AACL,QAAA,YAAA,CAAa,KAAQ,GAAA,KAAA,CAAA;AAAA,OACvB;AACA,MAAA,UAAA,CAAW,KAAQ,GAAA,KAAA,CAAA;AAAA,KACrB;AAEA,IAA6B,SAAA,iBAAA,GAAA;AAC3B,MAAA,IAAI,aAAa,KAAO,EAAA;AACtB,QAAA,OAAA;AAAA,OACF;AACA,MAAA,IAAI,MAAM,SAAW,EAAA;AACnB,QAAA,iBAAA,CAAkB,QAAQ,KAAM,CAAA,UAAA,KAAe,IAAK,CAAA,KAAA,CAAM,MAAM,UAAU,CAAA,CAAA;AAAA,OAC5E;AACA,MAAA,YAAA,CAAa,QAAQ,KAAM,CAAA,UAAA,CAAA;AAC3B,MAAA,UAAA,CAAW,KAAQ,GAAA,CAAA,CAAA,CAAA;AAAA,KACrB;AAEA,IAAA,KAAA,CACE,MAAM,KAAA,CAAM,UACZ,EAAA,CAAC,GAAQ,KAAA;AACP,MAAA,YAAA,CAAa,KAAQ,GAAA,GAAA,CAAA;AACrB,MAAA,iBAAA,CAAkB,QAAQ,KAAM,CAAA,UAAA,KAAe,IAAK,CAAA,KAAA,CAAM,MAAM,UAAU,CAAA,CAAA;AAAA,KAE9E,CAAA,CAAA;AAEA,IAAI,IAAA,CAAC,MAAM,UAAY,EAAA;AACrB,MAAA,IAAA,CAAK,oBAAoB,CAAC,CAAA,CAAA;AAAA,KAC5B;AAEA,IAAa,MAAA,CAAA;AAAA,MAEX,eAAA;AAAA,MAEA,iBAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}