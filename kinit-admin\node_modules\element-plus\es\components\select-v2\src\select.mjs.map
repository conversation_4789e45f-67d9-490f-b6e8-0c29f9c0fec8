{"version": 3, "file": "select.mjs", "sources": ["../../../../../../packages/components/select-v2/src/select.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"selectRef\"\n    v-click-outside:[popperRef]=\"handleClickOutside\"\n    :class=\"[nsSelect.b(), nsSelect.m(selectSize)]\"\n    @mouseenter=\"states.inputHovering = true\"\n    @mouseleave=\"states.inputHovering = false\"\n    @click.stop=\"toggleMenu\"\n  >\n    <el-tooltip\n      ref=\"tooltipRef\"\n      :visible=\"dropdownMenuVisible\"\n      :teleported=\"teleported\"\n      :popper-class=\"[nsSelect.e('popper'), popperClass]\"\n      :gpu-acceleration=\"false\"\n      :stop-popper-mouse-event=\"false\"\n      :popper-options=\"popperOptions\"\n      :fallback-placements=\"fallbackPlacements\"\n      :effect=\"effect\"\n      :placement=\"placement\"\n      pure\n      :transition=\"`${nsSelect.namespace.value}-zoom-in-top`\"\n      trigger=\"click\"\n      :persistent=\"persistent\"\n      @before-show=\"handleMenuEnter\"\n      @hide=\"states.isBeforeHide = false\"\n    >\n      <template #default>\n        <div\n          ref=\"wrapperRef\"\n          :class=\"[\n            nsSelect.e('wrapper'),\n            nsSelect.is('focused', isFocused),\n            nsSelect.is('hovering', states.inputHovering),\n            nsSelect.is('filterable', filterable),\n            nsSelect.is('disabled', selectDisabled),\n          ]\"\n        >\n          <div\n            v-if=\"$slots.prefix\"\n            ref=\"prefixRef\"\n            :class=\"nsSelect.e('prefix')\"\n          >\n            <slot name=\"prefix\" />\n          </div>\n          <div\n            ref=\"selectionRef\"\n            :class=\"[\n              nsSelect.e('selection'),\n              nsSelect.is(\n                'near',\n                multiple && !$slots.prefix && !!modelValue.length\n              ),\n            ]\"\n          >\n            <slot v-if=\"multiple\" name=\"tag\">\n              <div\n                v-for=\"item in showTagList\"\n                :key=\"getValueKey(getValue(item))\"\n                :class=\"nsSelect.e('selected-item')\"\n              >\n                <el-tag\n                  :closable=\"!selectDisabled && !getDisabled(item)\"\n                  :size=\"collapseTagSize\"\n                  :type=\"tagType\"\n                  disable-transitions\n                  :style=\"tagStyle\"\n                  @close=\"deleteTag($event, item)\"\n                >\n                  <span :class=\"nsSelect.e('tags-text')\">\n                    {{ getLabel(item) }}\n                  </span>\n                </el-tag>\n              </div>\n\n              <el-tooltip\n                v-if=\"collapseTags && modelValue.length > maxCollapseTags\"\n                ref=\"tagTooltipRef\"\n                :disabled=\"dropdownMenuVisible || !collapseTagsTooltip\"\n                :fallback-placements=\"['bottom', 'top', 'right', 'left']\"\n                :effect=\"effect\"\n                placement=\"bottom\"\n                :teleported=\"teleported\"\n              >\n                <template #default>\n                  <div\n                    ref=\"collapseItemRef\"\n                    :class=\"nsSelect.e('selected-item')\"\n                  >\n                    <el-tag\n                      :closable=\"false\"\n                      :size=\"collapseTagSize\"\n                      :type=\"tagType\"\n                      :style=\"collapseTagStyle\"\n                      disable-transitions\n                    >\n                      <span :class=\"nsSelect.e('tags-text')\">\n                        + {{ modelValue.length - maxCollapseTags }}\n                      </span>\n                    </el-tag>\n                  </div>\n                </template>\n                <template #content>\n                  <div ref=\"tagMenuRef\" :class=\"nsSelect.e('selection')\">\n                    <div\n                      v-for=\"selected in collapseTagList\"\n                      :key=\"getValueKey(getValue(selected))\"\n                      :class=\"nsSelect.e('selected-item')\"\n                    >\n                      <el-tag\n                        class=\"in-tooltip\"\n                        :closable=\"!selectDisabled && !getDisabled(selected)\"\n                        :size=\"collapseTagSize\"\n                        :type=\"tagType\"\n                        disable-transitions\n                        @close=\"deleteTag($event, selected)\"\n                      >\n                        <span :class=\"nsSelect.e('tags-text')\">\n                          {{ getLabel(selected) }}\n                        </span>\n                      </el-tag>\n                    </div>\n                  </div>\n                </template>\n              </el-tooltip>\n            </slot>\n            <div\n              v-if=\"!selectDisabled\"\n              :class=\"[\n                nsSelect.e('selected-item'),\n                nsSelect.e('input-wrapper'),\n                nsSelect.is('hidden', !filterable),\n              ]\"\n            >\n              <input\n                :id=\"inputId\"\n                ref=\"inputRef\"\n                v-model=\"states.inputValue\"\n                :style=\"inputStyle\"\n                :autocomplete=\"autocomplete\"\n                aria-autocomplete=\"list\"\n                aria-haspopup=\"listbox\"\n                autocapitalize=\"off\"\n                :aria-expanded=\"expanded\"\n                :aria-label=\"ariaLabel\"\n                :class=\"[nsSelect.e('input'), nsSelect.is(selectSize)]\"\n                :disabled=\"selectDisabled\"\n                role=\"combobox\"\n                :readonly=\"!filterable\"\n                spellcheck=\"false\"\n                type=\"text\"\n                :name=\"name\"\n                @focus=\"handleFocus\"\n                @blur=\"handleBlur\"\n                @input=\"onInput\"\n                @compositionstart=\"handleCompositionStart\"\n                @compositionupdate=\"handleCompositionUpdate\"\n                @compositionend=\"handleCompositionEnd\"\n                @keydown.up.stop.prevent=\"onKeyboardNavigate('backward')\"\n                @keydown.down.stop.prevent=\"onKeyboardNavigate('forward')\"\n                @keydown.enter.stop.prevent=\"onKeyboardSelect\"\n                @keydown.esc.stop.prevent=\"handleEsc\"\n                @keydown.delete.stop=\"handleDel\"\n                @click.stop=\"toggleMenu\"\n              />\n              <span\n                v-if=\"filterable\"\n                ref=\"calculatorRef\"\n                aria-hidden=\"true\"\n                :class=\"nsSelect.e('input-calculator')\"\n                v-text=\"states.inputValue\"\n              />\n            </div>\n            <div\n              v-if=\"shouldShowPlaceholder\"\n              :class=\"[\n                nsSelect.e('selected-item'),\n                nsSelect.e('placeholder'),\n                nsSelect.is(\n                  'transparent',\n                  !hasModelValue || (expanded && !states.inputValue)\n                ),\n              ]\"\n            >\n              <span>{{ currentPlaceholder }}</span>\n            </div>\n          </div>\n          <div ref=\"suffixRef\" :class=\"nsSelect.e('suffix')\">\n            <el-icon\n              v-if=\"iconComponent\"\n              v-show=\"!showClearBtn\"\n              :class=\"[nsSelect.e('caret'), nsInput.e('icon'), iconReverse]\"\n            >\n              <component :is=\"iconComponent\" />\n            </el-icon>\n            <el-icon\n              v-if=\"showClearBtn && clearIcon\"\n              :class=\"[nsSelect.e('caret'), nsInput.e('icon')]\"\n              @click.prevent.stop=\"handleClear\"\n            >\n              <component :is=\"clearIcon\" />\n            </el-icon>\n            <el-icon\n              v-if=\"validateState && validateIcon\"\n              :class=\"[nsInput.e('icon'), nsInput.e('validateIcon')]\"\n            >\n              <component :is=\"validateIcon\" />\n            </el-icon>\n          </div>\n        </div>\n      </template>\n      <template #content>\n        <el-select-menu\n          ref=\"menuRef\"\n          :data=\"filteredOptions\"\n          :width=\"popperSize\"\n          :hovering-index=\"states.hoveringIndex\"\n          :scrollbar-always-on=\"scrollbarAlwaysOn\"\n        >\n          <template v-if=\"$slots.header\" #header>\n            <div :class=\"nsSelect.be('dropdown', 'header')\">\n              <slot name=\"header\" />\n            </div>\n          </template>\n          <template #default=\"scope\">\n            <slot v-bind=\"scope\" />\n          </template>\n          <template v-if=\"$slots.loading && loading\" #loading>\n            <div :class=\"nsSelect.be('dropdown', 'loading')\">\n              <slot name=\"loading\" />\n            </div>\n          </template>\n          <template v-else-if=\"loading || filteredOptions.length === 0\" #empty>\n            <div :class=\"nsSelect.be('dropdown', 'empty')\">\n              <slot name=\"empty\">\n                <span>{{ emptyText }}</span>\n              </slot>\n            </div>\n          </template>\n          <template v-if=\"$slots.footer\" #footer>\n            <div :class=\"nsSelect.be('dropdown', 'footer')\">\n              <slot name=\"footer\" />\n            </div>\n          </template>\n        </el-select-menu>\n      </template>\n    </el-tooltip>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { computed, defineComponent, provide, reactive, toRefs } from 'vue'\nimport { isArray } from '@element-plus/utils'\nimport { ClickOutside } from '@element-plus/directives'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport ElTag from '@element-plus/components/tag'\nimport ElIcon from '@element-plus/components/icon'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport ElSelectMenu from './select-dropdown'\nimport useSelect from './useSelect'\nimport { SelectProps } from './defaults'\nimport { selectV2InjectionKey } from './token'\n\nexport default defineComponent({\n  name: 'ElSelectV2',\n  components: {\n    ElSelectMenu,\n    ElTag,\n    ElTooltip,\n    ElIcon,\n  },\n  directives: { ClickOutside },\n  props: SelectProps,\n  emits: [\n    UPDATE_MODEL_EVENT,\n    CHANGE_EVENT,\n    'remove-tag',\n    'clear',\n    'visible-change',\n    'focus',\n    'blur',\n  ],\n\n  setup(props, { emit }) {\n    const modelValue = computed(() => {\n      const { modelValue: rawModelValue, multiple } = props\n      const fallback = multiple ? [] : undefined\n      // When it is array, we check if this is multi-select.\n      // Based on the result we get\n      if (isArray(rawModelValue)) {\n        return multiple ? rawModelValue : fallback\n      }\n      return multiple ? fallback : rawModelValue\n    })\n\n    const API = useSelect(\n      reactive({\n        ...toRefs(props),\n        modelValue,\n      }),\n      emit\n    )\n    // TODO, remove the any cast to align the actual API.\n    provide(selectV2InjectionKey, {\n      props: reactive({\n        ...toRefs(props),\n        height: API.popupHeight,\n        modelValue,\n      }),\n      tooltipRef: API.tooltipRef,\n      onSelect: API.onSelect,\n      onHover: API.onHover,\n      onKeyboardNavigate: API.onKeyboardNavigate,\n      onKeyboardSelect: API.onKeyboardSelect,\n    } as any)\n\n    return {\n      ...API,\n      modelValue,\n    }\n  },\n})\n</script>\n"], "names": ["_createElementBlock", "_normalizeClass", "_createVNode", "_withCtx", "_createElementVNode", "_renderSlot", "_createCommentVNode", "_openBlock", "_Fragment", "_renderList", "_normalizeStyle", "_toDisplayString", "_createBlock", "_with<PERSON><PERSON><PERSON>", "_withModifiers", "_vModelText", "_withDirectives", "_resolveDynamicComponent", "_createSlots"], "mappings": ";;;;;;;;;;;;;;;;AAuQA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,YAAA;AAAA,EACN,UAAY,EAAA;AAAA,IACV,YAAA;AAAA,IACA,KAAA;AAAA,IACA,SAAA;AAAA,IACA,MAAA;AAAA,GACF;AAAA,EACA,UAAA,EAAY,EAAE,YAAa,EAAA;AAAA,EAC3B,KAAO,EAAA,WAAA;AAAA,EACP,KAAO,EAAA;AAAA,IACL,kBAAA;AAAA,IACA,YAAA;AAAA,IACA,YAAA;AAAA,IACA,OAAA;AAAA,IACA,gBAAA;AAAA,IACA,OAAA;AAAA,IACA,MAAA;AAAA,GACF;AAAA,EAEA,KAAA,CAAM,KAAO,EAAA,EAAE,IAAQ,EAAA,EAAA;AACrB,IAAM,MAAA,UAAA,GAAa,SAAS,MAAM;AAChC,MAAM,MAAA,EAAE,UAAY,EAAA,aAAA,EAAe,QAAa,EAAA,GAAA,KAAA,CAAA;AAChD,MAAM,MAAA,QAAA,GAAW,QAAW,GAAA,EAAK,GAAA,KAAA,CAAA,CAAA;AAGjC,MAAI,IAAA,OAAA,CAAQ,aAAa,CAAG,EAAA;AAC1B,QAAA,OAAO,WAAW,aAAgB,GAAA,QAAA,CAAA;AAAA,OACpC;AACA,MAAA,OAAO,WAAW,QAAW,GAAA,aAAA,CAAA;AAAA,KAC9B,CAAA,CAAA;AAED,IAAM,MAAA,GAAA,GAAM,UACV,QAAS,CAAA;AAAA,MACP,GAAG,OAAO,KAAK,CAAA;AAAA,MACf,UAAA;AAAA,KACD,GACD,IACF,CAAA,CAAA;AAEA,IAAA,OAAA,CAAQ,oBAAsB,EAAA;AAAA,MAC5B,OAAO,QAAS,CAAA;AAAA,QACd,GAAG,OAAO,KAAK,CAAA;AAAA,QACf,QAAQ,GAAI,CAAA,WAAA;AAAA,QACZ,UAAA;AAAA,OACD,CAAA;AAAA,MACD,YAAY,GAAI,CAAA,UAAA;AAAA,MAChB,UAAU,GAAI,CAAA,QAAA;AAAA,MACd,SAAS,GAAI,CAAA,OAAA;AAAA,MACb,oBAAoB,GAAI,CAAA,kBAAA;AAAA,MACxB,kBAAkB,GAAI,CAAA,gBAAA;AAAA,KAChB,CAAA,CAAA;AAER,IAAO,OAAA;AAAA,MACL,GAAG,GAAA;AAAA,MACH,UAAA;AAAA,KACF,CAAA;AAAA,GACF;AACF,CAAC,CAAA,CAAA;;;;;;;;;sCAhUCA,kBAsPM,CAAA,KAAA,EAAA;AAAA,IArPJ,GAAI,EAAA,WAAA;AAAA,IAEH,OAAKC,cAAG,CAAA,CAAA,IAAA,CAAA,QAAA,CAAS,GAAK,EAAA,IAAA,CAAA,QAAA,CAAS,EAAE,IAAU,CAAA,UAAA,CAAA,CAAA,CAAA;AAAA,IAC3C,YAAA,EAAU,MAAE,CAAA,EAAA,CAAA,KAAA,MAAA,CAAA,EAAA,CAAA,GAAA,CAAA,MAAA,KAAA,IAAA,CAAA,MAAA,CAAO,aAAa,GAAA,IAAA,CAAA;AAAA,IAChC,YAAA,EAAU,MAAE,CAAA,EAAA,CAAA,KAAA,MAAA,CAAA,EAAA,CAAA,GAAA,CAAA,MAAA,KAAA,IAAA,CAAA,MAAA,CAAO,aAAa,GAAA,KAAA,CAAA;AAAA,IAChC,OAAA,EAAK,uDAAO,IAAU,CAAA,UAAA,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,GAAA,EAAA;IAEvBC,WA6Oa,CAAA,qBAAA,EAAA;AAAA,MA5OX,GAAI,EAAA,YAAA;AAAA,MACH,OAAS,EAAA,IAAA,CAAA,mBAAA;AAAA,MACT,UAAY,EAAA,IAAA,CAAA,UAAA;AAAA,MACZ,cAAY,EAAA,CAAG,IAAS,CAAA,QAAA,CAAA,CAAA,CAAC,WAAY,IAAW,CAAA,WAAA,CAAA;AAAA,MAChD,kBAAkB,EAAA,KAAA;AAAA,MAClB,yBAAyB,EAAA,KAAA;AAAA,MACzB,gBAAgB,EAAA,IAAA,CAAA,aAAA;AAAA,MAChB,qBAAqB,EAAA,IAAA,CAAA,kBAAA;AAAA,MACrB,MAAQ,EAAA,IAAA,CAAA,MAAA;AAAA,MACR,SAAW,EAAA,IAAA,CAAA,SAAA;AAAA,MACZ,IAAA,EAAA,EAAA;AAAA,MACC,UAAA,EAAU,CAAK,EAAA,IAAA,CAAA,QAAA,CAAS,SAAU,CAAA,KAAA,CAAA,YAAA,CAAA;AAAA,MACnC,OAAQ,EAAA,OAAA;AAAA,MACP,UAAY,EAAA,IAAA,CAAA,UAAA;AAAA,MACZ,YAAa,EAAA,IAAA,CAAA,eAAA;AAAA,MACb,MAAA,EAAI,MAAE,CAAA,EAAA,CAAA,KAAA,MAAA,CAAA,EAAA,CAAA,GAAA,CAAA,MAAA,KAAA,IAAA,CAAA,MAAA,CAAO,YAAY,GAAA,KAAA,CAAA;AAAA,KAAA,EAAA;AAEf,MAAA,OAAA,EAAOC,QAChB,MAqLM;AAAA,QArLNC,kBAqLM,CAAA,KAAA,EAAA;AAAA,UApLJ,GAAI,EAAA,YAAA;AAAA,UACH,KAAK,EAAAH,cAAA,CAAA;AAAA,YAAgB,cAAS,CAAC,CAAA,SAAA,CAAA;AAAA,YAAyB,IAAA,CAAA,QAAA,CAAS,EAAE,CAAA,SAAA,EAAY,IAAS,CAAA,SAAA,CAAA;AAAA,YAAe,IAAS,CAAA,QAAA,CAAA,EAAA,CAAE,UAAa,EAAA,IAAA,CAAA,MAAA,CAAO,aAAa,CAAA;AAAA,YAAe,IAAA,CAAA,QAAA,CAAS,EAAE,CAAA,YAAA,EAAe,IAAU,CAAA,UAAA,CAAA;AAAA,YAAe,IAAA,CAAA,QAAA,CAAS,EAAE,CAAA,UAAA,EAAa,IAAc,CAAA,cAAA,CAAA;AAAA,WAAA,CAAA;;AASpP,UAAA,IAAA,CAAA,MAAA,CAAO,uBADfD,kBAMM,CAAA,KAAA,EAAA;AAAA,YAAA,GAAA,EAAA,CAAA;YAJJ,GAAI,EAAA,WAAA;AAAA,YACH,KAAA,EAAKC,cAAE,CAAA,IAAA,CAAA,QAAA,CAAS,CAAC,CAAA,QAAA,CAAA,CAAA;AAAA,WAAA,EAAA;YAElBI,UAAsB,CAAA,IAAA,CAAA,MAAA,EAAA,QAAA,CAAA;AAAA,WAAA,EAAA,CAAA,CAAA,IAAAC,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;UAExBF,kBA6IM,CAAA,KAAA,EAAA;AAAA,YA5IJ,GAAI,EAAA,cAAA;AAAA,YACH,KAAK,EAAAH,cAAA,CAAA;AAAA,cAAkB,cAAS,CAAC,CAAA,WAAA,CAAA;AAAA,cAA6B,cAAS,EAA4C,CAAA,MAAA,EAAA,IAAA,CAAA,QAAA,IAAQ,CAAK,IAAO,CAAA,MAAA,CAAA,MAAA,IAAM,EAAM,IAAW,CAAA,UAAA,CAAA,MAAA,CAAA;;;YAQnJ,IAAZ,CAAA,QAAA,GAAAI,UAAA,CAsEO,gCAtEP,MAsEO;AAAA,eAAAE,SAAA,CAAA,IAAA,CAAA,EArELP,kBAiBM,CAAAQ,QAAA,EAAA,IAAA,EAAAC,UAAA,CAhBW,IAAW,CAAA,WAAA,EAAA,CAAnB,IAAI,KAAA;oCADbT,kBAiBM,CAAA,KAAA,EAAA;AAAA,kBAfH,GAAA,EAAK,IAAY,CAAA,WAAA,CAAA,IAAA,CAAA,QAAA,CAAS,IAAI,CAAA,CAAA;AAAA,kBAC9B,KAAA,EAAKC,cAAE,CAAA,IAAA,CAAA,QAAA,CAAS,CAAC,CAAA,eAAA,CAAA,CAAA;AAAA,iBAAA,EAAA;kBAElBC,WAWS,CAAA,iBAAA,EAAA;AAAA,oBAVN,QAAQ,EAAA,CAAG,IAAc,CAAA,cAAA,IAAA,CAAK,iBAAY,IAAI,CAAA;AAAA,oBAC9C,IAAM,EAAA,IAAA,CAAA,eAAA;AAAA,oBACN,IAAM,EAAA,IAAA,CAAA,OAAA;AAAA,oBACP,qBAAA,EAAA,EAAA;AAAA,oBACC,KAAA,EAAKQ,eAAE,IAAQ,CAAA,QAAA,CAAA;AAAA,oBACf,OAAK,EAAA,CAAA,MAAA,KAAE,IAAU,CAAA,SAAA,CAAA,MAAA,EAAQ,IAAI,CAAA;AAAA,mBAAA,EAAA;qCAE9B,MAEO;AAAA,sBAFPN,kBAEO,CAAA,MAAA,EAAA;AAAA,wBAFA,KAAA,EAAKH,cAAE,CAAA,IAAA,CAAA,QAAA,CAAS,CAAC,CAAA,WAAA,CAAA,CAAA;AAAA,uBAAA,EAAAU,eAAA,CACnB,cAAS,IAAI,CAAA,CAAA,EAAA,CAAA,CAAA;AAAA,qBAAA,CAAA;;;;;cAMd,IAAgB,CAAA,YAAA,IAAA,IAAA,CAAA,UAAA,CAAW,MAAS,GAAA,IAAA,CAAA,eAAA,IAAAJ,SAAA,EAAA,EAD5CK,WAiDa,CAAA,qBAAA,EAAA;AAAA,gBAAA,GAAA,EAAA,CAAA;gBA/CX,GAAI,EAAA,eAAA;AAAA,gBACH,QAAA,EAAU,4BAAmB,CAAK,IAAA,CAAA,mBAAA;AAAA,gBAClC,qBAAqB,EAAA,CAAA,QAAA,EAAA,KAAA,EAAA,OAAA,EAAA,MAAA,CAAA;AAAA,gBACrB,MAAQ,EAAA,IAAA,CAAA,MAAA;AAAA,gBACT,SAAU,EAAA,QAAA;AAAA,gBACT,UAAY,EAAA,IAAA,CAAA,UAAA;AAAA,eAAA,EAAA;AAEF,gBAAA,OAAA,EAAOT,QAChB,MAeM;AAAA,kBAfNC,kBAeM,CAAA,KAAA,EAAA;AAAA,oBAdJ,GAAI,EAAA,iBAAA;AAAA,oBACH,KAAA,EAAKH,cAAE,CAAA,IAAA,CAAA,QAAA,CAAS,CAAC,CAAA,eAAA,CAAA,CAAA;AAAA,mBAAA,EAAA;oBAElBC,WAUS,CAAA,iBAAA,EAAA;AAAA,sBATN,QAAU,EAAA,KAAA;AAAA,sBACV,IAAM,EAAA,IAAA,CAAA,eAAA;AAAA,sBACN,IAAM,EAAA,IAAA,CAAA,OAAA;AAAA,sBACN,KAAA,EAAKQ,eAAE,IAAgB,CAAA,gBAAA,CAAA;AAAA,sBACxB,qBAAA,EAAA,EAAA;AAAA,qBAAA,EAAA;uCAEA,MAEO;AAAA,wBAFPN,kBAEO,CAAA,MAAA,EAAA;AAAA,0BAFA,KAAA,EAAKH,cAAE,CAAA,IAAA,CAAA,QAAA,CAAS,CAAC,CAAA,WAAA,CAAA,CAAA;AAAA,yBAAA,EAAe,KACnC,GAAAU,eAAA,CAAG,IAAW,CAAA,UAAA,CAAA,MAAA,GAAS,IAAe,CAAA,eAAA,CAAA,EAAA,CAAA,CAAA;AAAA,uBAAA,CAAA;;;;;AAKrC,gBAAA,OAAA,EAAOR,QAChB,MAmBM;AAAA,kBAnBNC,kBAmBM,CAAA,KAAA,EAAA;AAAA,oBAnBD,GAAI,EAAA,YAAA;AAAA,oBAAc,KAAA,EAAKH,cAAE,CAAA,IAAA,CAAA,QAAA,CAAS,CAAC,CAAA,WAAA,CAAA,CAAA;AAAA,mBAAA,EAAA;sCACtCD,kBAiBM,CAAAQ,QAAA,EAAA,IAAA,EAAAC,UAAA,CAhBe,IAAe,CAAA,eAAA,EAAA,CAA3B,QAAQ,KAAA;0CADjBT,kBAiBM,CAAA,KAAA,EAAA;AAAA,wBAfH,GAAA,EAAK,IAAY,CAAA,WAAA,CAAA,IAAA,CAAA,QAAA,CAAS,QAAQ,CAAA,CAAA;AAAA,wBAClC,KAAA,EAAKC,cAAE,CAAA,IAAA,CAAA,QAAA,CAAS,CAAC,CAAA,eAAA,CAAA,CAAA;AAAA,uBAAA,EAAA;wBAElBC,WAWS,CAAA,iBAAA,EAAA;AAAA,0BAVP,KAAM,EAAA,YAAA;AAAA,0BACL,QAAQ,EAAA,CAAG,IAAc,CAAA,cAAA,IAAA,CAAK,iBAAY,QAAQ,CAAA;AAAA,0BAClD,IAAM,EAAA,IAAA,CAAA,eAAA;AAAA,0BACN,IAAM,EAAA,IAAA,CAAA,OAAA;AAAA,0BACP,qBAAA,EAAA,EAAA;AAAA,0BACC,OAAK,EAAA,CAAA,MAAA,KAAE,IAAU,CAAA,SAAA,CAAA,MAAA,EAAQ,QAAQ,CAAA;AAAA,yBAAA,EAAA;2CAElC,MAEO;AAAA,4BAFPE,kBAEO,CAAA,MAAA,EAAA;AAAA,8BAFA,KAAA,EAAKH,cAAE,CAAA,IAAA,CAAA,QAAA,CAAS,CAAC,CAAA,WAAA,CAAA,CAAA;AAAA,6BAAA,EAAAU,eAAA,CACnB,cAAS,QAAQ,CAAA,CAAA,EAAA,CAAA,CAAA;AAAA,2BAAA,CAAA;;;;;;;;;;AASzB,YAAA,CAAA,IAAA,CAAA,cAAA,IAAAJ,SAAA,EAAA,EADTP,kBA8CM,CAAA,KAAA,EAAA;AAAA,cAAA,GAAA,EAAA,CAAA;cA5CH,KAAK,EAAAC,cAAA,CAAA;AAAA,gBAAoB,cAAS,CAAC,CAAA,eAAA,CAAA;AAAA,gBAAmC,cAAS,CAAC,CAAA,eAAA,CAAA;AAAA,gBAAmC,IAAA,CAAA,QAAA,CAAS,EAAE,CAAA,QAAA,EAAA,CAAY,IAAU,CAAA,UAAA,CAAA;AAAA,eAAA,CAAA;;6BAMrJG,kBA8BE,CAAA,OAAA,EAAA;AAAA,gBA7BC,EAAI,EAAA,IAAA,CAAA,OAAA;AAAA,gBACL,GAAI,EAAA,UAAA;AAAA,gBAAA,qBAAA,EAAA,MAAA,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,KACK,YAAO,UAAU,GAAA,MAAA,CAAA;AAAA,gBACzB,KAAA,EAAKM,eAAE,IAAU,CAAA,UAAA,CAAA;AAAA,gBACjB,YAAc,EAAA,IAAA,CAAA,YAAA;AAAA,gBACf,mBAAkB,EAAA,MAAA;AAAA,gBAClB,eAAc,EAAA,SAAA;AAAA,gBACd,cAAe,EAAA,KAAA;AAAA,gBACd,eAAe,EAAA,IAAA,CAAA,QAAA;AAAA,gBACf,YAAY,EAAA,IAAA,CAAA,SAAA;AAAA,gBACZ,OAAKT,cAAG,CAAA,CAAA,IAAA,CAAA,QAAA,CAAS,EAAC,OAAW,CAAA,EAAA,IAAA,CAAA,QAAA,CAAS,GAAG,IAAU,CAAA,UAAA,CAAA,CAAA,CAAA;AAAA,gBACnD,QAAU,EAAA,IAAA,CAAA,cAAA;AAAA,gBACX,IAAK,EAAA,UAAA;AAAA,gBACJ,UAAQ,CAAG,IAAA,CAAA,UAAA;AAAA,gBACZ,UAAW,EAAA,OAAA;AAAA,gBACX,IAAK,EAAA,MAAA;AAAA,gBACJ,IAAM,EAAA,IAAA,CAAA,IAAA;AAAA,gBACN,SAAK,MAAE,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,WAAA,IAAA,IAAA,CAAA,WAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,gBACP,QAAI,MAAE,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,UAAA,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,gBACN,SAAK,MAAE,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,OAAA,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,gBACP,oBAAgB,MAAE,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,sBAAA,IAAA,IAAA,CAAA,sBAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,gBAClB,qBAAiB,MAAE,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,uBAAA,IAAA,IAAA,CAAA,uBAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,gBACnB,kBAAc,MAAE,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,oBAAA,IAAA,IAAA,CAAA,oBAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,gBAChB,SAAO,EAAA;AAAA,kBAAA,MAAA,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAAY,QAAA,CAAAC,aAAA,CAAA,CAAA,MAAA,KAAkB,IAAkB,CAAA,kBAAA,CAAA,UAAA,CAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AAAA,kBAAA,MAAA,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAAD,QAAA,CAAAC,aAAA,CAAA,CAAA,MAAA,KAChB,IAAkB,CAAA,kBAAA,CAAA,SAAA,CAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,kBAAA,MAAA,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAAD,QAAA,CAAAC,aAAA,CAAA,CAAA,GAAA,IAAA,KACjB,IAAgB,CAAA,gBAAA,IAAA,IAAA,CAAA,gBAAA,CAAA,GAAA,IAAA,CAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AAAA,kBAAA,MAAA,CAAA,EAAA,CAAA,KAAA,MAAA,CAAA,EAAA,CAAA,GAAAD,QAAA,CAAAC,aAAA,CAAA,CAAA,GAAA,IAAA,KAClB,IAAS,CAAA,SAAA,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAA,CAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,kBAAA,MAAA,CAAA,EAAA,CAAA,KAAA,MAAA,CAAA,EAAA,CAAA,GAAAD,QAAA,CAAAC,aAAA,CAAA,CAAA,GAAA,IAAA,KACd,IAAS,CAAA,SAAA,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA,iBAAA;AAC9B,gBAAA,OAAA,EAAK,uDAAO,IAAU,CAAA,UAAA,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,eAAA,EAAA,IAAA,EAAA,EAAA,EAAA,UAAA,CAAA,EAAA;AA1Bd,gBAAA,CAAAC,UAAA,EAAA,IAAA,CAAA,MAAA,CAAO,UAAU,CAAA;AAAA,eAAA,CAAA;AA6BpB,cAAA,IAAA,CAAA,UAAA,IAAAR,SAAA,EAAA,EADRP,kBAME,CAAA,MAAA,EAAA;AAAA,gBAAA,GAAA,EAAA,CAAA;gBAJA,GAAI,EAAA,eAAA;AAAA,gBACJ,aAAY,EAAA,MAAA;AAAA,gBACX,KAAA,EAAKC,cAAE,CAAA,IAAA,CAAA,QAAA,CAAS,CAAC,CAAA,kBAAA,CAAA,CAAA;AAAA,gBAClB,WAAA,EAAAU,eAAA,CAAQ,IAAkB,CAAA,MAAA,CAAX,UAAU,CAAA;AAAA,eAAA,EAAA,IAAA,EAAA,EAAA,EAAA,UAAA,CAAA,IAAAL,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;;AAIrB,YAAA,IAAA,CAAA,qBAAA,IAAAC,SAAA,EAAA,EADRP,kBAYM,CAAA,KAAA,EAAA;AAAA,cAAA,GAAA,EAAA,CAAA;cAVH,KAAK,EAAAC,cAAA,CAAA;AAAA,gBAAoB,cAAS,CAAC,CAAA,eAAA,CAAA;AAAA,gBAAmC,cAAS,CAAC,CAAA,aAAA,CAAA;AAAA,gBAAiC,IAAS,CAAA,QAAA,CAAA,EAAA,CAAA,aAAA,EAAA,CAAwD,IAAkB,CAAA,aAAA,IAAA,IAAA,CAAA,QAAA,IAAQ,CAAK,IAAO,CAAA,MAAA,CAAA,UAAA,CAAA;;;AASzN,cAAAG,kBAAA,CAAqC,8BAA5B,IAAkB,CAAA,kBAAA,CAAA,EAAA,CAAA,CAAA;AAAA,aAAA,EAAA,CAAA,CAAA,IAAAE,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;;UAG/BF,kBAqBM,CAAA,KAAA,EAAA;AAAA,YArBD,GAAI,EAAA,WAAA;AAAA,YAAa,KAAA,EAAKH,cAAE,CAAA,IAAA,CAAA,QAAA,CAAS,CAAC,CAAA,QAAA,CAAA,CAAA;AAAA,WAAA,EAAA;AAE7B,YAAA,IAAA,CAAA,aAAA,GAAAe,cAAA,EAAAT,SAAA,EAAA,EADRK,WAMU,CAAA,kBAAA,EAAA;AAAA,cAAA,GAAA,EAAA,CAAA;AAHP,cAAA,KAAA,EAAKX,gBAAG,IAAS,CAAA,QAAA,CAAA,CAAA,CAAC,UAAW,IAAQ,CAAA,OAAA,CAAA,CAAA,CAAC,SAAU,IAAW,CAAA,WAAA,CAAA,CAAA;AAAA,aAAA,EAAA;+BAE5D,MAAiC;AAAA,iBAAjCM,SAAA,EAAA,EAAAK,WAAA,CAAiCK,wBAAjB,IAAa,CAAA,aAAA,CAAA,CAAA;AAAA,eAAA,CAAA;;;uBAHpB,IAAY,CAAA,YAAA,CAAA;AAAA,aAAA,CAAA,GAAAX,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAMf,YAAA,IAAA,CAAA,YAAA,IAAgB,+BADxBM,WAMU,CAAA,kBAAA,EAAA;AAAA,cAAA,GAAA,EAAA,CAAA;AAJP,cAAA,KAAA,EAAKX,cAAG,CAAA,CAAA,IAAA,CAAA,QAAA,CAAS,CAAC,CAAA,OAAA,CAAA,EAAW,aAAQ,CAAC,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,cACtC,OAAA,EAAKa,cAAe,IAAW,CAAA,WAAA,EAAA,CAAA,SAAA,EAAA,MAAA,CAAA,CAAA;AAAA,aAAA,EAAA;+BAEhC,MAA6B;AAAA,iBAA7BP,SAAA,EAAA,EAAAK,WAAA,CAA6BK,wBAAb,IAAS,CAAA,SAAA,CAAA,CAAA;AAAA,eAAA,CAAA;;;AAGnB,YAAA,IAAA,CAAA,aAAA,IAAiB,kCADzBL,WAKU,CAAA,kBAAA,EAAA;AAAA,cAAA,GAAA,EAAA,CAAA;AAHP,cAAA,KAAA,EAAKX,cAAG,CAAA,CAAA,IAAA,CAAA,OAAA,CAAQ,CAAC,CAAA,MAAA,CAAA,EAAU,aAAQ,CAAC,CAAA,cAAA,CAAA,CAAA,CAAA;AAAA,aAAA,EAAA;+BAErC,MAAgC;AAAA,iBAAhCM,SAAA,EAAA,EAAAK,WAAA,CAAgCK,wBAAhB,IAAY,CAAA,YAAA,CAAA,CAAA;AAAA,eAAA,CAAA;;;;;;AAKzB,MAAA,OAAA,EAAOd,QAChB,MAgCiB;AAAA,QAhCjBD,WAgCiB,CAAA,yBAAA,EAAA;AAAA,UA/Bf,GAAI,EAAA,SAAA;AAAA,UACH,IAAM,EAAA,IAAA,CAAA,eAAA;AAAA,UACN,KAAO,EAAA,IAAA,CAAA,UAAA;AAAA,UACP,kBAAgB,IAAO,CAAA,MAAA,CAAA,aAAA;AAAA,UACvB,qBAAqB,EAAA,IAAA,CAAA,iBAAA;AAAA,SAAA,EAAAgB,WAAA,CAAA;UAOX,OAAO,EAAAf,OAAA,CAChB,CADkB,KAAK,KAAA;AAAA,YACvBE,UAAA,CAAuB,0DAAT,KAAK,CAAA,CAAA,CAAA;AAAA,WAAA,CAAA;;;UANL,IAAO,CAAA,MAAA,CAAA,MAAA,GAAA;AAAS,YAAA,IAAA,EAAA,QAAA;AAAA,YAAA,EAAA,EAAAF,OAAA,CAC9B,MAEM;AAAA,cAFNC,kBAEM,CAAA,KAAA,EAAA;AAAA,gBAFA,KAAA,EAAKH,cAAE,CAAA,IAAA,CAAA,QAAA,CAAS,EAAE,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA;AAAA,eAAA,EAAA;gBACtBI,UAAsB,CAAA,IAAA,CAAA,MAAA,EAAA,QAAA,CAAA;AAAA,eAAA,EAAA,CAAA,CAAA;;;AAMV,UAAA,IAAA,CAAA,MAAA,CAAO,OAAW,IAAA,IAAA,CAAA,OAAA,GAAA;AAAU,YAAA,IAAA,EAAA,SAAA;AAAA,YAAA,EAAA,EAAAF,OAAA,CAC1C,MAEM;AAAA,cAFNC,kBAEM,CAAA,KAAA,EAAA;AAAA,gBAFA,KAAA,EAAKH,cAAE,CAAA,IAAA,CAAA,QAAA,CAAS,EAAE,CAAA,UAAA,EAAA,SAAA,CAAA,CAAA;AAAA,eAAA,EAAA;gBACtBI,UAAuB,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA;AAAA,eAAA,EAAA,CAAA,CAAA;;AAGN,WAAA,GAAA,IAAA,CAAA,OAAA,IAAW,qBAAgB,MAAM,KAAA,CAAA,GAAA;AAAS,YAAA,IAAA,EAAA,OAAA;AAAA,YAAA,EAAA,EAAAF,OAAA,CAC7D,MAIM;AAAA,cAJNC,kBAIM,CAAA,KAAA,EAAA;AAAA,gBAJA,KAAA,EAAKH,cAAE,CAAA,IAAA,CAAA,QAAA,CAAS,EAAE,CAAA,UAAA,EAAA,OAAA,CAAA,CAAA;AAAA,eAAA,EAAA;AACtB,gBAAAI,UAAA,CAEO,0BAFP,MAEO;AAAA,kBADLD,kBAAA,CAA4B,8BAAnB,IAAS,CAAA,SAAA,CAAA,EAAA,CAAA,CAAA;AAAA,iBAAA,CAAA;;;;UAIR,IAAO,CAAA,MAAA,CAAA,MAAA,GAAA;AAAS,YAAA,IAAA,EAAA,QAAA;AAAA,YAAA,EAAA,EAAAD,OAAA,CAC9B,MAEM;AAAA,cAFNC,kBAEM,CAAA,KAAA,EAAA;AAAA,gBAFA,KAAA,EAAKH,cAAE,CAAA,IAAA,CAAA,QAAA,CAAS,EAAE,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA;AAAA,eAAA,EAAA;gBACtBI,UAAsB,CAAA,IAAA,CAAA,MAAA,EAAA,QAAA,CAAA;AAAA,eAAA,EAAA,CAAA,CAAA;;;;;;;;AA9OH,IAAA,CAAA,wBAAA,EAAA,IAAA,CAAA,kBAAA,EAAb,IAAW,CAAA,SAAA,CAAA;AAAA,GAAA,CAAA,CAAA;;;;;;"}