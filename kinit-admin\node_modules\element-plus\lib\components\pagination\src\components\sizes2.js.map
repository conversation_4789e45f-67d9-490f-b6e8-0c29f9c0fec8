{"version": 3, "file": "sizes2.js", "sources": ["../../../../../../../packages/components/pagination/src/components/sizes.vue"], "sourcesContent": ["<template>\n  <span :class=\"ns.e('sizes')\">\n    <el-select\n      :model-value=\"innerPageSize\"\n      :disabled=\"disabled\"\n      :popper-class=\"popperClass\"\n      :size=\"size\"\n      :teleported=\"teleported\"\n      :validate-event=\"false\"\n      @change=\"handleChange\"\n    >\n      <el-option\n        v-for=\"item in innerPageSizes\"\n        :key=\"item\"\n        :value=\"item\"\n        :label=\"item + t('el.pagination.pagesize')\"\n      />\n    </el-select>\n  </span>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, ref, watch } from 'vue'\nimport { isEqual } from 'lodash-unified'\nimport { ElOption, ElSelect } from '@element-plus/components/select'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { usePagination } from '../usePagination'\nimport { paginationSizesProps } from './sizes'\n\ndefineOptions({\n  name: 'ElPaginationSizes',\n})\n\nconst props = defineProps(paginationSizesProps)\nconst emit = defineEmits(['page-size-change'])\nconst { t } = useLocale()\nconst ns = useNamespace('pagination')\nconst pagination = usePagination()\nconst innerPageSize = ref<number>(props.pageSize!)\n\nwatch(\n  () => props.pageSizes,\n  (newVal, oldVal) => {\n    if (isEqual(newVal, oldVal)) return\n    if (Array.isArray(newVal)) {\n      const pageSize = newVal.includes(props.pageSize!)\n        ? props.pageSize\n        : props.pageSizes[0]\n      emit('page-size-change', pageSize)\n    }\n  }\n)\n\nwatch(\n  () => props.pageSize,\n  (newVal) => {\n    innerPageSize.value = newVal!\n  }\n)\n\nconst innerPageSizes = computed(() => props.pageSizes)\nfunction handleChange(val: number) {\n  if (val !== innerPageSize.value) {\n    innerPageSize.value = val\n    pagination.handleSizeChange?.(Number(val))\n  }\n}\n</script>\n"], "names": ["useLocale", "useNamespace", "usePagination", "ref", "watch", "isEqual", "computed"], "mappings": ";;;;;;;;;;;;;;uCA6Bc,CAAA;AAAA,EACZ,IAAM,EAAA,mBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAM,MAAA,EAAE,MAAMA,eAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAKC,qBAAa,YAAY,CAAA,CAAA;AACpC,IAAA,MAAM,aAAaC,2BAAc,EAAA,CAAA;AACjC,IAAM,MAAA,aAAA,GAAgBC,OAAY,CAAA,KAAA,CAAM,QAAS,CAAA,CAAA;AAEjD,IAAAC,SAAA,CACE,MAAM,KAAA,CAAM,SACZ,EAAA,CAAC,QAAQ,MAAW,KAAA;AAClB,MAAI,IAAAC,qBAAA,CAAQ,QAAQ,MAAM,CAAA;AAAG,QAAA,OAAA;AAC7B,MAAI,IAAA,KAAA,CAAM,OAAQ,CAAA,MAAM,CAAG,EAAA;AACzB,QAAM,MAAA,QAAA,GAAW,OAAO,QAAS,CAAA,KAAA,CAAM,QAAS,CAC5C,GAAA,KAAA,CAAM,QACN,GAAA,KAAA,CAAM,SAAU,CAAA,CAAA,CAAA,CAAA;AACpB,QAAA,IAAA,CAAK,oBAAoB,QAAQ,CAAA,CAAA;AAAA,OACnC;AAAA,KAEJ,CAAA,CAAA;AAEA,IAAAD,SAAA,CACE,MAAM,KAAA,CAAM,QACZ,EAAA,CAAC,MAAW,KAAA;AACV,MAAA,aAAA,CAAc,KAAQ,GAAA,MAAA,CAAA;AAAA,KAE1B,CAAA,CAAA;AAEA,IAAA,MAAM,cAAiB,GAAAE,YAAA,CAAS,MAAM,KAAA,CAAM,SAAS,CAAA,CAAA;AACrD,IAAA,SAAA,YAAA,CAAsB,GAAa,EAAA;AACjC,MAAI,IAAA,EAAA,CAAA;AACF,MAAA,IAAA,GAAA,KAAA,aAAsB,CAAA,KAAA,EAAA;AACtB,QAAW,aAAA,CAAA,KAAA,GAAA,GAAA,CAAA;AAA8B,QAC3C,CAAA,EAAA,GAAA,UAAA,CAAA,gBAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,UAAA,EAAA,MAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAAA,OACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}