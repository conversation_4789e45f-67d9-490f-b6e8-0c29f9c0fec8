{"version": 3, "file": "utils-helper.mjs", "sources": ["../../../../../../../packages/components/table/src/table-header/utils-helper.ts"], "sourcesContent": ["// @ts-nocheck\nimport { computed, inject } from 'vue'\nimport { TABLE_INJECTION_KEY } from '../tokens'\nimport type { TableColumnCtx } from '../table-column/defaults'\nimport type { TableHeaderProps } from '.'\n\nconst getAllColumns = <T>(\n  columns: TableColumnCtx<T>[]\n): TableColumnCtx<T>[] => {\n  const result: TableColumnCtx<T>[] = []\n  columns.forEach((column) => {\n    if (column.children) {\n      result.push(column)\n      // eslint-disable-next-line prefer-spread\n      result.push.apply(result, getAllColumns(column.children))\n    } else {\n      result.push(column)\n    }\n  })\n  return result\n}\n\nconst convertToRows = <T>(\n  originColumns: TableColumnCtx<T>[]\n): TableColumnCtx<T>[] => {\n  let maxLevel = 1\n  const traverse = (column: TableColumnCtx<T>, parent: TableColumnCtx<T>) => {\n    if (parent) {\n      column.level = parent.level + 1\n      if (maxLevel < column.level) {\n        maxLevel = column.level\n      }\n    }\n    if (column.children) {\n      let colSpan = 0\n      column.children.forEach((subColumn) => {\n        traverse(subColumn, column)\n        colSpan += subColumn.colSpan\n      })\n      column.colSpan = colSpan\n    } else {\n      column.colSpan = 1\n    }\n  }\n\n  originColumns.forEach((column) => {\n    column.level = 1\n    traverse(column, undefined)\n  })\n\n  const rows = []\n  for (let i = 0; i < maxLevel; i++) {\n    rows.push([])\n  }\n\n  const allColumns: TableColumnCtx<T>[] = getAllColumns(originColumns)\n\n  allColumns.forEach((column) => {\n    if (!column.children) {\n      column.rowSpan = maxLevel - column.level + 1\n    } else {\n      column.rowSpan = 1\n      column.children.forEach((col) => (col.isSubColumn = true))\n    }\n    rows[column.level - 1].push(column)\n  })\n\n  return rows\n}\n\nfunction useUtils<T>(props: TableHeaderProps<T>) {\n  const parent = inject(TABLE_INJECTION_KEY)\n  const columnRows = computed(() => {\n    return convertToRows(props.store.states.originColumns.value)\n  })\n  const isGroup = computed(() => {\n    const result = columnRows.value.length > 1\n    if (result && parent) {\n      parent.state.isGroup.value = true\n    }\n    return result\n  })\n  const toggleAllSelection = (event: Event) => {\n    event.stopPropagation()\n    parent?.store.commit('toggleAllSelection')\n  }\n  return {\n    isGroup,\n    toggleAllSelection,\n    columnRows,\n  }\n}\n\nexport default useUtils\n"], "names": [], "mappings": ";;;AAEA,MAAM,aAAa,GAAG,CAAC,OAAO,KAAK;AACnC,EAAE,MAAM,MAAM,GAAG,EAAE,CAAC;AACpB,EAAE,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AAC9B,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE;AACzB,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC1B,MAAM,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,aAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;AAChE,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AAC1B,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,MAAM,CAAC;AAChB,CAAC,CAAC;AACF,MAAM,aAAa,GAAG,CAAC,aAAa,KAAK;AACzC,EAAE,IAAI,QAAQ,GAAG,CAAC,CAAC;AACnB,EAAE,MAAM,QAAQ,GAAG,CAAC,MAAM,EAAE,MAAM,KAAK;AACvC,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;AACtC,MAAM,IAAI,QAAQ,GAAG,MAAM,CAAC,KAAK,EAAE;AACnC,QAAQ,QAAQ,GAAG,MAAM,CAAC,KAAK,CAAC;AAChC,OAAO;AACP,KAAK;AACL,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE;AACzB,MAAM,IAAI,OAAO,GAAG,CAAC,CAAC;AACtB,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,SAAS,KAAK;AAC7C,QAAQ,QAAQ,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;AACpC,QAAQ,OAAO,IAAI,SAAS,CAAC,OAAO,CAAC;AACrC,OAAO,CAAC,CAAC;AACT,MAAM,MAAM,CAAC,OAAO,GAAG,OAAO,CAAC;AAC/B,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC;AACzB,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,aAAa,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACpC,IAAI,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;AACrB,IAAI,QAAQ,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC;AAC7B,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,IAAI,GAAG,EAAE,CAAC;AAClB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE;AACrC,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAClB,GAAG;AACH,EAAE,MAAM,UAAU,GAAG,aAAa,CAAC,aAAa,CAAC,CAAC;AAClD,EAAE,UAAU,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;AACjC,IAAI,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE;AAC1B,MAAM,MAAM,CAAC,OAAO,GAAG,QAAQ,GAAG,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;AACnD,KAAK,MAAM;AACX,MAAM,MAAM,CAAC,OAAO,GAAG,CAAC,CAAC;AACzB,MAAM,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,CAAC;AAC/D,KAAK;AACL,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACxC,GAAG,CAAC,CAAC;AACL,EAAE,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AACF,SAAS,QAAQ,CAAC,KAAK,EAAE;AACzB,EAAE,MAAM,MAAM,GAAG,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAC7C,EAAE,MAAM,UAAU,GAAG,QAAQ,CAAC,MAAM;AACpC,IAAI,OAAO,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;AACjE,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,OAAO,GAAG,QAAQ,CAAC,MAAM;AACjC,IAAI,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAC/C,IAAI,IAAI,MAAM,IAAI,MAAM,EAAE;AAC1B,MAAM,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,GAAG,IAAI,CAAC;AACxC,KAAK;AACL,IAAI,OAAO,MAAM,CAAC;AAClB,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,kBAAkB,GAAG,CAAC,KAAK,KAAK;AACxC,IAAI,KAAK,CAAC,eAAe,EAAE,CAAC;AAC5B,IAAI,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,oBAAoB,CAAC,CAAC;AACxE,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI,kBAAkB;AACtB,IAAI,UAAU;AACd,GAAG,CAAC;AACJ;;;;"}