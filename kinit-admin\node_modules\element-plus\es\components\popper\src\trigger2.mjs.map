{"version": 3, "file": "trigger2.mjs", "sources": ["../../../../../../packages/components/popper/src/trigger.vue"], "sourcesContent": ["<template>\n  <el-only-child\n    v-if=\"!virtualTriggering\"\n    v-bind=\"$attrs\"\n    :aria-controls=\"ariaControls\"\n    :aria-describedby=\"ariaDescribedby\"\n    :aria-expanded=\"ariaExpanded\"\n    :aria-haspopup=\"ariaHaspopup\"\n  >\n    <slot />\n  </el-only-child>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, onBeforeUnmount, onMounted, watch } from 'vue'\nimport { isNil } from 'lodash-unified'\nimport { unrefElement } from '@vueuse/core'\nimport { ElOnlyChild } from '@element-plus/components/slot'\nimport { useForwardRef } from '@element-plus/hooks'\nimport { isElement } from '@element-plus/utils'\nimport { POPPER_INJECTION_KEY } from './constants'\nimport { popperTriggerProps } from './trigger'\n\nimport type { WatchStopHandle } from 'vue'\n\ndefineOptions({\n  name: 'ElPopperTrigger',\n  inheritAttrs: false,\n})\n\nconst props = defineProps(popperTriggerProps)\n\nconst { role, triggerRef } = inject(POPPER_INJECTION_KEY, undefined)!\n\nuseForwardRef(triggerRef)\n\nconst ariaControls = computed<string | undefined>(() => {\n  return ariaHaspopup.value ? props.id : undefined\n})\n\nconst ariaDescribedby = computed<string | undefined>(() => {\n  if (role && role.value === 'tooltip') {\n    return props.open && props.id ? props.id : undefined\n  }\n  return undefined\n})\n\nconst ariaHaspopup = computed<string | undefined>(() => {\n  if (role && role.value !== 'tooltip') {\n    return role.value\n  }\n  return undefined\n})\n\nconst ariaExpanded = computed<string | undefined>(() => {\n  return ariaHaspopup.value ? `${props.open}` : undefined\n})\n\nlet virtualTriggerAriaStopWatch: WatchStopHandle | undefined = undefined\n\nonMounted(() => {\n  watch(\n    () => props.virtualRef,\n    (virtualEl) => {\n      if (virtualEl) {\n        triggerRef.value = unrefElement(virtualEl as HTMLElement)\n      }\n    },\n    {\n      immediate: true,\n    }\n  )\n\n  watch(\n    triggerRef,\n    (el, prevEl) => {\n      virtualTriggerAriaStopWatch?.()\n      virtualTriggerAriaStopWatch = undefined\n      if (isElement(el)) {\n        ;(\n          [\n            'onMouseenter',\n            'onMouseleave',\n            'onClick',\n            'onKeydown',\n            'onFocus',\n            'onBlur',\n            'onContextmenu',\n          ] as const\n        ).forEach((eventName) => {\n          const handler = props[eventName]\n          if (handler) {\n            ;(el as HTMLElement).addEventListener(\n              eventName.slice(2).toLowerCase(),\n              handler\n            )\n            ;(prevEl as HTMLElement)?.removeEventListener?.(\n              eventName.slice(2).toLowerCase(),\n              handler\n            )\n          }\n        })\n        virtualTriggerAriaStopWatch = watch(\n          [ariaControls, ariaDescribedby, ariaHaspopup, ariaExpanded],\n          (watches) => {\n            ;[\n              'aria-controls',\n              'aria-describedby',\n              'aria-haspopup',\n              'aria-expanded',\n            ].forEach((key, idx) => {\n              isNil(watches[idx])\n                ? el.removeAttribute(key)\n                : el.setAttribute(key, watches[idx]!)\n            })\n          },\n          { immediate: true }\n        )\n      }\n      if (isElement(prevEl)) {\n        ;[\n          'aria-controls',\n          'aria-describedby',\n          'aria-haspopup',\n          'aria-expanded',\n        ].forEach((key) => prevEl.removeAttribute(key))\n      }\n    },\n    {\n      immediate: true,\n    }\n  )\n})\n\nonBeforeUnmount(() => {\n  virtualTriggerAriaStopWatch?.()\n  virtualTriggerAriaStopWatch = undefined\n})\n\ndefineExpose({\n  /**\n   * @description trigger element\n   */\n  triggerRef,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;mCAyBc,CAAA;AAAA,EACZ,IAAM,EAAA,iBAAA;AAAA,EACN,YAAc,EAAA,KAAA;AAChB,CAAA,CAAA,CAAA;;;;;;AAIA,IAAA,MAAM,EAAE,IAAA,EAAM,UAAe,EAAA,GAAA,MAAA,CAAO,sBAAsB,KAAS,CAAA,CAAA,CAAA;AAEnE,IAAA,aAAA,CAAc,UAAU,CAAA,CAAA;AAExB,IAAM,MAAA,YAAA,GAAe,SAA6B,MAAM;AACtD,MAAO,OAAA,YAAA,CAAa,KAAQ,GAAA,KAAA,CAAM,EAAK,GAAA,KAAA,CAAA,CAAA;AAAA,KACxC,CAAA,CAAA;AAED,IAAM,MAAA,eAAA,GAAkB,SAA6B,MAAM;AACzD,MAAI,IAAA,IAAA,IAAQ,IAAK,CAAA,KAAA,KAAU,SAAW,EAAA;AACpC,QAAA,OAAO,KAAM,CAAA,IAAA,IAAQ,KAAM,CAAA,EAAA,GAAK,MAAM,EAAK,GAAA,KAAA,CAAA,CAAA;AAAA,OAC7C;AACA,MAAO,OAAA,KAAA,CAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAe,SAA6B,MAAM;AACtD,MAAI,IAAA,IAAA,IAAQ,IAAK,CAAA,KAAA,KAAU,SAAW,EAAA;AACpC,QAAA,OAAO,IAAK,CAAA,KAAA,CAAA;AAAA,OACd;AACA,MAAO,OAAA,KAAA,CAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAe,SAA6B,MAAM;AACtD,MAAA,OAAO,YAAa,CAAA,KAAA,GAAQ,CAAG,EAAA,KAAA,CAAM,IAAS,CAAA,CAAA,GAAA,KAAA,CAAA,CAAA;AAAA,KAC/C,CAAA,CAAA;AAED,IAAA,IAAI,2BAA2D,GAAA,KAAA,CAAA,CAAA;AAE/D,IAAA,SAAA,CAAU,MAAM;AACd,MAAA,KAAA,CACE,MAAM,KAAA,CAAM,UACZ,EAAA,CAAC,SAAc,KAAA;AACb,QAAA,IAAI,SAAW,EAAA;AACb,UAAW,UAAA,CAAA,KAAA,GAAQ,aAAa,SAAwB,CAAA,CAAA;AAAA,SAC1D;AAAA,OAEF,EAAA;AAAA,QACE,SAAW,EAAA,IAAA;AAAA,OAEf,CAAA,CAAA;AAEA,MACE,KAAA,CAAA,UAAA,EACA,CAAC,EAAA,EAAI,MAAW,KAAA;AACd,QAA8B,2BAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,2BAAA,EAAA,CAAA;AAC9B,QAA8B,2BAAA,GAAA,KAAA,CAAA,CAAA;AAC9B,QAAI,IAAA,SAAA,CAAU,EAAE,CAAG,EAAA;AACjB,UAAA,CAAA;AAAC,UACC;AAAA,YACE,cAAA;AAAA,YACA,cAAA;AAAA,YACA,SAAA;AAAA,YACA,WAAA;AAAA,YACA,SAAA;AAAA,YACA,QAAA;AAAA,YACA,eAAA;AAAA,WACF,CACA,OAAQ,CAAA,CAAC,SAAc,KAAA;AACvB,YAAA,IAAA,EAAM;AACN,YAAA,MAAa,OAAA,GAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AACX,YAAA,IAAA,OAAA,EAAA;AAAC,cAAC,CAAA;AAID,cAAC,EAAA,CAAA,2BACA,KAAU,CAAA,CAAA,CAAA,CAAA,WAAS,EAAA,EAAA,OAAY;AAEjC,cACF,CAAA,EAAA,GAAA,MAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,MAAA,CAAA,mBAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,WAAA,EAAA,EAAA,OAAA,CAAA,CAAA;AAAA,aACD;AACD,WAA8B,CAAA,CAAA;AAG1B,UAAA,2BAAA,GAAA,KAAA,CAAA,CAAA,YAAA,EAAA,eAAA,EAAA,YAAA,EAAA,YAAA,CAAA,EAAA,CAAA,OAAA,KAAA;AAAC,YAAA,CAAA;AAAA,YACC;AAAA,cACA,eAAA;AAAA,cACA,kBAAA;AAAA,cACA,eAAA;AAAA,cACA,eAAsB;AACtB,aAAM,CAAA,OAAA,CAAA,CAAA,GAAA,EAAQ,GAAI,KACd;AACkC,cACvC,KAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,GAAA,EAAA,CAAA,eAAA,CAAA,GAAA,CAAA,GAAA,EAAA,CAAA,YAAA,CAAA,GAAA,EAAA,OAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAAA,aAEH,CAAA,CAAE;AACJ,WACF,EAAA,EAAA,SAAA,EAAA,IAAA,EAAA,CAAA,CAAA;AACA,SAAI;AACF,QAAA,IAAA,SAAA,CAAA,MAAA,CAAA,EAAA;AAAC,UAAA,CAAA;AAAA,UACC;AAAA,YACA,eAAA;AAAA,YACA,kBAAA;AAAA,YACA,eAAA;AAAA,YACA;AAA4C,WAChD,CAAA,OAAA,CAAA,CAAA,GAAA,KAAA,MAAA,CAAA,eAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AAAA,SAEF;AAAA,OAAA,EACa;AAAA,QAEf,SAAA,EAAA,IAAA;AAAA,OACD,CAAA,CAAA;AAED,KAAA,CAAA,CAAA;AACE,IAA8B,eAAA,CAAA,MAAA;AAC9B,MAA8B,2BAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,2BAAA,EAAA,CAAA;AAAA,MAC/B,2BAAA,GAAA,KAAA,CAAA,CAAA;AAED,KAAa,CAAA,CAAA;AAAA,IAIX,MAAA,CAAA;AAAA,MACD,UAAA;;;;;;;;;;;;;;;;;;;;;"}