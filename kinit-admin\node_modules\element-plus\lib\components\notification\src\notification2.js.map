{"version": 3, "file": "notification2.js", "sources": ["../../../../../../packages/components/notification/src/notification.vue"], "sourcesContent": ["<template>\n  <transition\n    :name=\"ns.b('fade')\"\n    @before-leave=\"onClose\"\n    @after-leave=\"$emit('destroy')\"\n  >\n    <div\n      v-show=\"visible\"\n      :id=\"id\"\n      :class=\"[ns.b(), customClass, horizontalClass]\"\n      :style=\"positionStyle\"\n      role=\"alert\"\n      @mouseenter=\"clearTimer\"\n      @mouseleave=\"startTimer\"\n      @click=\"onClick\"\n    >\n      <el-icon v-if=\"iconComponent\" :class=\"[ns.e('icon'), typeClass]\">\n        <component :is=\"iconComponent\" />\n      </el-icon>\n      <div :class=\"ns.e('group')\">\n        <h2 :class=\"ns.e('title')\" v-text=\"title\" />\n        <div\n          v-show=\"message\"\n          :class=\"ns.e('content')\"\n          :style=\"!!title ? undefined : { margin: 0 }\"\n        >\n          <slot>\n            <p v-if=\"!dangerouslyUseHTMLString\">{{ message }}</p>\n            <!-- Caution here, message could've been compromised, never use user's input as message -->\n            <p v-else v-html=\"message\" />\n          </slot>\n        </div>\n        <el-icon v-if=\"showClose\" :class=\"ns.e('closeBtn')\" @click.stop=\"close\">\n          <Close />\n        </el-icon>\n      </div>\n    </div>\n  </transition>\n</template>\n<script lang=\"ts\" setup>\nimport { computed, onMounted, ref } from 'vue'\nimport { useEventListener, useTimeoutFn } from '@vueuse/core'\nimport { CloseComponents, TypeComponentsMap } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { useGlobalComponentSettings } from '@element-plus/components/config-provider'\nimport { notificationEmits, notificationProps } from './notification'\n\nimport type { CSSProperties } from 'vue'\n\ndefineOptions({\n  name: 'ElNotification',\n})\n\nconst props = defineProps(notificationProps)\ndefineEmits(notificationEmits)\n\nconst { ns, zIndex } = useGlobalComponentSettings('notification')\nconst { nextZIndex, currentZIndex } = zIndex\n\nconst { Close } = CloseComponents\n\nconst visible = ref(false)\nlet timer: (() => void) | undefined = undefined\n\nconst typeClass = computed(() => {\n  const type = props.type\n  return type && TypeComponentsMap[props.type] ? ns.m(type) : ''\n})\n\nconst iconComponent = computed(() => {\n  if (!props.type) return props.icon\n  return TypeComponentsMap[props.type] || props.icon\n})\n\nconst horizontalClass = computed(() =>\n  props.position.endsWith('right') ? 'right' : 'left'\n)\n\nconst verticalProperty = computed(() =>\n  props.position.startsWith('top') ? 'top' : 'bottom'\n)\n\nconst positionStyle = computed<CSSProperties>(() => {\n  return {\n    [verticalProperty.value]: `${props.offset}px`,\n    zIndex: props.zIndex ?? currentZIndex.value,\n  }\n})\n\nfunction startTimer() {\n  if (props.duration > 0) {\n    ;({ stop: timer } = useTimeoutFn(() => {\n      if (visible.value) close()\n    }, props.duration))\n  }\n}\n\nfunction clearTimer() {\n  timer?.()\n}\n\nfunction close() {\n  visible.value = false\n}\n\nfunction onKeydown({ code }: KeyboardEvent) {\n  if (code === EVENT_CODE.delete || code === EVENT_CODE.backspace) {\n    clearTimer() // press delete/backspace clear timer\n  } else if (code === EVENT_CODE.esc) {\n    // press esc to close the notification\n    if (visible.value) {\n      close()\n    }\n  } else {\n    startTimer() // resume timer\n  }\n}\n\n// lifecycle\nonMounted(() => {\n  startTimer()\n  nextZIndex()\n  visible.value = true\n})\n\nuseEventListener(document, 'keydown', onKeydown)\n\ndefineExpose({\n  visible,\n  /** @description close notification */\n  close,\n})\n</script>\n"], "names": ["useGlobalComponentSettings", "CloseComponents", "ref", "computed", "TypeComponentsMap", "useTimeoutFn", "EVENT_CODE", "onMounted", "useEventListener"], "mappings": ";;;;;;;;;;;;;;;;;;;;uCAkDc,CAAA;AAAA,EACZ,IAAM,EAAA,gBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAA,MAAM,EAAE,EAAA,EAAI,MAAW,EAAA,GAAAA,0CAAA,CAA2B,cAAc,CAAA,CAAA;AAChE,IAAM,MAAA,EAAE,YAAY,aAAkB,EAAA,GAAA,MAAA,CAAA;AAEtC,IAAA,MAAM,EAAE,KAAU,EAAA,GAAAC,oBAAA,CAAA;AAElB,IAAM,MAAA,OAAA,GAAUC,QAAI,KAAK,CAAA,CAAA;AACzB,IAAA,IAAI,KAAkC,GAAA,KAAA,CAAA,CAAA;AAEtC,IAAM,MAAA,SAAA,GAAYC,aAAS,MAAM;AAC/B,MAAA,MAAM,OAAO,KAAM,CAAA,IAAA,CAAA;AACnB,MAAA,OAAO,QAAQC,sBAAkB,CAAA,KAAA,CAAM,QAAQ,EAAG,CAAA,CAAA,CAAE,IAAI,CAAI,GAAA,EAAA,CAAA;AAAA,KAC7D,CAAA,CAAA;AAED,IAAM,MAAA,aAAA,GAAgBD,aAAS,MAAM;AACnC,MAAA,IAAI,CAAC,KAAM,CAAA,IAAA;AAAM,QAAA,OAAO,KAAM,CAAA,IAAA,CAAA;AAC9B,MAAO,OAAAC,sBAAA,CAAkB,KAAM,CAAA,IAAA,CAAA,IAAS,KAAM,CAAA,IAAA,CAAA;AAAA,KAC/C,CAAA,CAAA;AAED,IAAM,MAAA,eAAA,GAAkBD,aAAS,MAC/B,KAAA,CAAM,SAAS,QAAS,CAAA,OAAO,CAAI,GAAA,OAAA,GAAU,MAC/C,CAAA,CAAA;AAEA,IAAM,MAAA,gBAAA,GAAmBA,aAAS,MAChC,KAAA,CAAM,SAAS,UAAW,CAAA,KAAK,CAAI,GAAA,KAAA,GAAQ,QAC7C,CAAA,CAAA;AAEA,IAAM,MAAA,aAAA,GAAgBA,aAAwB,MAAM;AAClD,MAAO,IAAA,EAAA,CAAA;AAAA,MAAA,OACJ;AAAkC,QACnC,CAAA,gBAAc,CAAA,KAAA,GAAwB,CAAA,EAAA,KAAA,CAAA,MAAA,CAAA,EAAA,CAAA;AAAA,QACxC,MAAA,EAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,EAAA,GAAA,aAAA,CAAA,KAAA;AAAA,OACD,CAAA;AAED,KAAsB,CAAA,CAAA;AACpB,IAAI,SAAA;AACF,MAAA,IAAA,KAAA,CAAA,QAAA,GAAA,CAAA,EAAA;AAAC,QAAC,CAAE;AACF,QAAA,CAAA,EAAA,IAAY,EAAA,KAAA,EAAA,GAAAE,iBAAA,CAAA,MAAA;AAAO,UAAM,IAAA,OAAA,CAAA,KAAA;AAAA,iBAClB,EAAQ,CAAA;AAAA,SACnB,EAAA,KAAA,CAAA,QAAA,CAAA,EAAA;AAAA,OACF;AAEA,KAAsB;AACpB,IAAQ,SAAA,UAAA,GAAA;AAAA,MACV,KAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,KAAA,EAAA,CAAA;AAEA,KAAiB;AACf,IAAA,SAAA,KAAgB,GAAA;AAAA,MAClB,OAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,KAAA;AACE,IAAA,SAAa,SAAA,CAAA,EAAA,IAAA,EAAqB,EAAA;AAChC,MAAW,IAAA,IAAA,KAAAC,eAAA,CAAA,MAAA,IAAA,IAAA,KAAAA,eAAA,CAAA,SAAA,EAAA;AAAA,QACb,UAAoB,EAAA,CAAA;AAElB,OAAA,cAAmB,KAAAA,eAAA,CAAA,GAAA,EAAA;AACjB,QAAM,IAAA,OAAA,CAAA,KAAA,EAAA;AAAA,UACR,KAAA,EAAA,CAAA;AAAA,SACK;AACL,OAAW,MAAA;AAAA,QACb,UAAA,EAAA,CAAA;AAAA,OACF;AAGA,KAAA;AACE,IAAWC,aAAA,CAAA,MAAA;AACX,MAAW,UAAA,EAAA,CAAA;AACX,MAAA,UAAgB,EAAA,CAAA;AAAA,MACjB,OAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAED,KAAiB,CAAA,CAAA;AAEjB,IAAaC,qBAAA,CAAA,QAAA,EAAA,SAAA,EAAA,SAAA,CAAA,CAAA;AAAA,IACX,MAAA,CAAA;AAAA,MAEA,OAAA;AAAA,MACD,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}