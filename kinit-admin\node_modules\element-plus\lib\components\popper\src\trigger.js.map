{"version": 3, "file": "trigger.js", "sources": ["../../../../../../packages/components/popper/src/trigger.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\n\nimport type { Measurable } from './constants'\nimport type Trigger from './trigger.vue'\n\nexport const popperTriggerProps = buildProps({\n  virtualRef: {\n    type: definePropType<Measurable>(Object),\n  },\n  virtualTriggering: Boolean,\n  onMouseenter: {\n    type: definePropType<(e: Event) => void>(Function),\n  },\n  onMouseleave: {\n    type: definePropType<(e: Event) => void>(Function),\n  },\n  onClick: {\n    type: definePropType<(e: Event) => void>(Function),\n  },\n  onKeydown: {\n    type: definePropType<(e: Event) => void>(Function),\n  },\n  onFocus: {\n    type: definePropType<(e: Event) => void>(Function),\n  },\n  onBlur: {\n    type: definePropType<(e: Event) => void>(Function),\n  },\n  onContextmenu: {\n    type: definePropType<(e: Event) => void>(Function),\n  },\n  id: String,\n  open: <PERSON>olean,\n} as const)\n\nexport type PopperTriggerProps = typeof popperTriggerProps\n\nexport type PopperTriggerInstance = InstanceType<typeof Trigger>\n\n/** @deprecated use `popperTriggerProps` instead, and it will be deprecated in the next major version */\nexport const usePopperTriggerProps = popperTriggerProps\n\n/** @deprecated use `PopperTriggerInstance` instead, and it will be deprecated in the next major version */\nexport type ElPopperArrowTrigger = PopperTriggerInstance\n"], "names": ["buildProps", "definePropType"], "mappings": ";;;;;;;AACY,MAAC,kBAAkB,GAAGA,kBAAU,CAAC;AAC7C,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAEC,sBAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,iBAAiB,EAAE,OAAO;AAC5B,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAEA,sBAAc,CAAC,QAAQ,CAAC;AAClC,GAAG;AACH,EAAE,EAAE,EAAE,MAAM;AACZ,EAAE,IAAI,EAAE,OAAO;AACf,CAAC,EAAE;AACS,MAAC,qBAAqB,GAAG;;;;;"}