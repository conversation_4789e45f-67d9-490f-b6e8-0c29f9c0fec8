{"version": 3, "file": "use-checkbox.js", "sources": ["../../../../../../../packages/components/checkbox/src/composables/use-checkbox.ts"], "sourcesContent": ["import { useFormItem, useFormItemInputId } from '@element-plus/components/form'\nimport { isArray } from '@element-plus/utils'\nimport { useCheckboxDisabled } from './use-checkbox-disabled'\nimport { useCheckboxEvent } from './use-checkbox-event'\nimport { useCheckboxModel } from './use-checkbox-model'\nimport { useCheckboxStatus } from './use-checkbox-status'\n\nimport type { ComponentInternalInstance } from 'vue'\nimport type { CheckboxProps } from '../checkbox'\nimport type { CheckboxModel } from './use-checkbox-model'\n\nconst setStoreValue = (\n  props: CheckboxProps,\n  { model }: Pick<CheckboxModel, 'model'>\n) => {\n  function addToStore() {\n    if (isArray(model.value) && !model.value.includes(props.label)) {\n      model.value.push(props.label)\n    } else {\n      model.value = props.trueLabel || true\n    }\n  }\n  props.checked && addToStore()\n}\n\nexport const useCheckbox = (\n  props: CheckboxProps,\n  slots: ComponentInternalInstance['slots']\n) => {\n  const { formItem: elFormItem } = useFormItem()\n  const { model, isGroup, isLimitExceeded } = useCheckboxModel(props)\n  const {\n    isFocused,\n    isChecked,\n    checkboxButtonSize,\n    checkboxSize,\n    hasOwnLabel,\n  } = useCheckboxStatus(props, slots, { model })\n  const { isDisabled } = useCheckboxDisabled({ model, isChecked })\n  const { inputId, isLabeledByFormItem } = useFormItemInputId(props, {\n    formItemContext: elFormItem,\n    disableIdGeneration: hasOwnLabel,\n    disableIdManagement: isGroup,\n  })\n  const { handleChange, onClickRoot } = useCheckboxEvent(props, {\n    model,\n    isLimitExceeded,\n    hasOwnLabel,\n    isDisabled,\n    isLabeledByFormItem,\n  })\n\n  setStoreValue(props, { model })\n\n  return {\n    inputId,\n    isLabeledByFormItem,\n    isChecked,\n    isDisabled,\n    isFocused,\n    checkboxButtonSize,\n    checkboxSize,\n    hasOwnLabel,\n    model,\n    handleChange,\n    onClickRoot,\n  }\n}\n"], "names": ["isArray", "useFormItem", "useCheckboxModel", "useCheckboxStatus", "useCheckboxDisabled", "useFormItemInputId", "useCheckboxEvent"], "mappings": ";;;;;;;;;;;;;AAMA,MAAM,aAAa,GAAG,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,KAAK;AAC5C,EAAE,SAAS,UAAU,GAAG;AACxB,IAAI,IAAIA,cAAO,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;AACpE,MAAM,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACpC,KAAK,MAAM;AACX,MAAM,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,SAAS,IAAI,IAAI,CAAC;AAC5C,KAAK;AACL,GAAG;AACH,EAAE,KAAK,CAAC,OAAO,IAAI,UAAU,EAAE,CAAC;AAChC,CAAC,CAAC;AACU,MAAC,WAAW,GAAG,CAAC,KAAK,EAAE,KAAK,KAAK;AAC7C,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAGC,uBAAW,EAAE,CAAC;AACjD,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,eAAe,EAAE,GAAGC,iCAAgB,CAAC,KAAK,CAAC,CAAC;AACtE,EAAE,MAAM;AACR,IAAI,SAAS;AACb,IAAI,SAAS;AACb,IAAI,kBAAkB;AACtB,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,GAAG,GAAGC,mCAAiB,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;AACjD,EAAE,MAAM,EAAE,UAAU,EAAE,GAAGC,uCAAmB,CAAC,EAAE,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC;AACnE,EAAE,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE,GAAGC,8BAAkB,CAAC,KAAK,EAAE;AACrE,IAAI,eAAe,EAAE,UAAU;AAC/B,IAAI,mBAAmB,EAAE,WAAW;AACpC,IAAI,mBAAmB,EAAE,OAAO;AAChC,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,GAAGC,iCAAgB,CAAC,KAAK,EAAE;AAChE,IAAI,KAAK;AACT,IAAI,eAAe;AACnB,IAAI,WAAW;AACf,IAAI,UAAU;AACd,IAAI,mBAAmB;AACvB,GAAG,CAAC,CAAC;AACL,EAAE,aAAa,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;AAClC,EAAE,OAAO;AACT,IAAI,OAAO;AACX,IAAI,mBAAmB;AACvB,IAAI,SAAS;AACb,IAAI,UAAU;AACd,IAAI,SAAS;AACb,IAAI,kBAAkB;AACtB,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,IAAI,KAAK;AACT,IAAI,YAAY;AAChB,IAAI,WAAW;AACf,GAAG,CAAC;AACJ;;;;"}