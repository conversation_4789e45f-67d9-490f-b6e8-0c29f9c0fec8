{"version": 3, "file": "statistic2.mjs", "sources": ["../../../../../../packages/components/statistic/src/statistic.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b()\">\n    <div v-if=\"$slots.title || title\" :class=\"ns.e('head')\">\n      <slot name=\"title\">\n        {{ title }}\n      </slot>\n    </div>\n    <div :class=\"ns.e('content')\">\n      <div v-if=\"$slots.prefix || prefix\" :class=\"ns.e('prefix')\">\n        <slot name=\"prefix\">\n          <span>{{ prefix }}</span>\n        </slot>\n      </div>\n      <span :class=\"ns.e('number')\" :style=\"valueStyle\">\n        {{ displayValue }}\n      </span>\n      <div v-if=\"$slots.suffix || suffix\" :class=\"ns.e('suffix')\">\n        <slot name=\"suffix\">\n          <span>{{ suffix }}</span>\n        </slot>\n      </div>\n    </div>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { isFunction, isNumber } from '@element-plus/utils'\nimport { statisticProps } from './statistic'\n\ndefineOptions({\n  name: 'ElStatistic',\n})\n\nconst props = defineProps(statisticProps)\nconst ns = useNamespace('statistic')\n\nconst displayValue = computed(() => {\n  const { value, formatter, precision, decimalSeparator, groupSeparator } =\n    props\n\n  if (isFunction(formatter)) return formatter(value)\n\n  if (!isNumber(value)) return value\n\n  let [integer, decimal = ''] = String(value).split('.')\n  decimal = decimal\n    .padEnd(precision, '0')\n    .slice(0, precision > 0 ? precision : 0)\n  integer = integer.replace(/\\B(?=(\\d{3})+(?!\\d))/g, groupSeparator)\n  return [integer, decimal].join(decimal ? decimalSeparator : '')\n})\n\ndefineExpose({\n  /**\n   * @description current display value\n   */\n  displayValue,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;mCA8Bc,CAAA;AAAA,EACZ,IAAM,EAAA,aAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAGA,IAAM,MAAA,EAAA,GAAK,aAAa,WAAW,CAAA,CAAA;AAEnC,IAAM,MAAA,YAAA,GAAe,SAAS,MAAM;AAClC,MAAA,MAAM,EAAE,KAAA,EAAO,SAAW,EAAA,SAAA,EAAW,kBAAkB,cACrD,EAAA,GAAA,KAAA,CAAA;AAEF,MAAA,IAAI,WAAW,SAAS,CAAA;AAAG,QAAA,OAAO,UAAU,KAAK,CAAA,CAAA;AAEjD,MAAI,IAAA,CAAC,SAAS,KAAK,CAAA;AAAG,QAAO,OAAA,KAAA,CAAA;AAE7B,MAAI,IAAA,CAAC,SAAS,OAAU,GAAA,EAAA,CAAA,GAAM,OAAO,KAAK,CAAA,CAAE,MAAM,GAAG,CAAA,CAAA;AACrD,MAAU,OAAA,GAAA,OAAA,CACP,MAAO,CAAA,SAAA,EAAW,GAAG,CAAA,CACrB,MAAM,CAAG,EAAA,SAAA,GAAY,CAAI,GAAA,SAAA,GAAY,CAAC,CAAA,CAAA;AACzC,MAAU,OAAA,GAAA,OAAA,CAAQ,OAAQ,CAAA,uBAAA,EAAyB,cAAc,CAAA,CAAA;AACjE,MAAA,OAAO,CAAC,OAAS,EAAA,OAAO,EAAE,IAAK,CAAA,OAAA,GAAU,mBAAmB,EAAE,CAAA,CAAA;AAAA,KAC/D,CAAA,CAAA;AAED,IAAa,MAAA,CAAA;AAAA,MAIX,YAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}