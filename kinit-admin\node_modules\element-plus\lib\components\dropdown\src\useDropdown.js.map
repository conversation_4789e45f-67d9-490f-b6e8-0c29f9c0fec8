{"version": 3, "file": "useDropdown.js", "sources": ["../../../../../../packages/components/dropdown/src/useDropdown.ts"], "sourcesContent": ["// @ts-nocheck\nimport { computed, inject, ref } from 'vue'\nimport { addClass } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport { useId, useNamespace } from '@element-plus/hooks'\nimport type { Nullable } from '@element-plus/utils'\nimport type { IElDropdownInstance } from './dropdown'\n\nexport const useDropdown = () => {\n  const elDropdown = inject<IElDropdownInstance>('elDropdown', {})\n  const _elDropdownSize = computed(() => elDropdown?.dropdownSize)\n\n  return {\n    elDropdown,\n    _elDropdownSize,\n  }\n}\n\nexport const initDropdownDomEvent = (\n  dropdownChildren,\n  triggerElm,\n  _instance\n) => {\n  const ns = useNamespace('dropdown')\n  const menuItems = ref<Nullable<HTMLButtonElement[]>>(null)\n  const menuItemsArray = ref<Nullable<HTMLElement[]>>(null)\n  const dropdownElm = ref<Nullable<HTMLElement>>(null)\n  const listId = useId()\n  dropdownElm.value = dropdownChildren?.subTree.el\n\n  function removeTabindex() {\n    triggerElm.setAttribute('tabindex', '-1')\n    menuItemsArray.value?.forEach((item) => {\n      item.setAttribute('tabindex', '-1')\n    })\n  }\n\n  function resetTabindex(ele) {\n    removeTabindex()\n    ele?.setAttribute('tabindex', '0')\n  }\n\n  function handleTriggerKeyDown(ev: KeyboardEvent) {\n    const code = ev.code\n    if ([EVENT_CODE.up, EVENT_CODE.down].includes(code)) {\n      removeTabindex()\n      resetTabindex(menuItems.value[0])\n      menuItems.value[0].focus()\n      ev.preventDefault()\n      ev.stopPropagation()\n    } else if (code === EVENT_CODE.enter) {\n      _instance.handleClick()\n    } else if ([EVENT_CODE.tab, EVENT_CODE.esc].includes(code)) {\n      _instance.hide()\n    }\n  }\n\n  function handleItemKeyDown(ev) {\n    const code = ev.code\n    const target = ev.target\n    const currentIndex = menuItemsArray.value.indexOf(target)\n    const max = menuItemsArray.value.length - 1\n    let nextIndex\n    if ([EVENT_CODE.up, EVENT_CODE.down].includes(code)) {\n      if (code === EVENT_CODE.up) {\n        nextIndex = currentIndex !== 0 ? currentIndex - 1 : 0\n      } else {\n        nextIndex = currentIndex < max ? currentIndex + 1 : max\n      }\n      removeTabindex()\n      resetTabindex(menuItems.value[nextIndex])\n      menuItems.value[nextIndex].focus()\n      ev.preventDefault()\n      ev.stopPropagation()\n    } else if (code === EVENT_CODE.enter) {\n      triggerElmFocus()\n      target.click()\n      if (_instance.props.hideOnClick) {\n        _instance.hide()\n      }\n    } else if ([EVENT_CODE.tab, EVENT_CODE.esc].includes(code)) {\n      _instance.hide()\n      triggerElmFocus()\n    }\n  }\n\n  function initAria() {\n    dropdownElm.value.setAttribute('id', listId.value)\n    triggerElm.setAttribute('aria-haspopup', 'list')\n    triggerElm.setAttribute('aria-controls', listId.value)\n    if (!_instance.props.splitButton) {\n      triggerElm.setAttribute('role', 'button')\n      triggerElm.setAttribute('tabindex', _instance.props.tabindex)\n      addClass(triggerElm, ns.b('selfdefine'))\n    }\n  }\n\n  function initEvent() {\n    triggerElm?.addEventListener('keydown', handleTriggerKeyDown)\n    dropdownElm.value?.addEventListener('keydown', handleItemKeyDown, true)\n  }\n\n  function initDomOperation() {\n    menuItems.value = dropdownElm.value.querySelectorAll(\n      \"[tabindex='-1']\"\n    ) as unknown as HTMLButtonElement[]\n    menuItemsArray.value = Array.from(menuItems.value)\n\n    initEvent()\n    initAria()\n  }\n\n  function triggerElmFocus() {\n    triggerElm.focus()\n  }\n\n  initDomOperation()\n}\n"], "names": ["inject", "computed", "useNamespace", "ref", "useId", "EVENT_CODE", "addClass"], "mappings": ";;;;;;;;;;;;;AAIY,MAAC,WAAW,GAAG,MAAM;AACjC,EAAE,MAAM,UAAU,GAAGA,UAAM,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;AAC9C,EAAE,MAAM,eAAe,GAAGC,YAAQ,CAAC,MAAM,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,YAAY,CAAC,CAAC;AAChG,EAAE,OAAO;AACT,IAAI,UAAU;AACd,IAAI,eAAe;AACnB,GAAG,CAAC;AACJ,EAAE;AACU,MAAC,oBAAoB,GAAG,CAAC,gBAAgB,EAAE,UAAU,EAAE,SAAS,KAAK;AACjF,EAAE,MAAM,EAAE,GAAGC,kBAAY,CAAC,UAAU,CAAC,CAAC;AACtC,EAAE,MAAM,SAAS,GAAGC,OAAG,CAAC,IAAI,CAAC,CAAC;AAC9B,EAAE,MAAM,cAAc,GAAGA,OAAG,CAAC,IAAI,CAAC,CAAC;AACnC,EAAE,MAAM,WAAW,GAAGA,OAAG,CAAC,IAAI,CAAC,CAAC;AAChC,EAAE,MAAM,MAAM,GAAGC,aAAK,EAAE,CAAC;AACzB,EAAE,WAAW,CAAC,KAAK,GAAG,gBAAgB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,gBAAgB,CAAC,OAAO,CAAC,EAAE,CAAC;AACtF,EAAE,SAAS,cAAc,GAAG;AAC5B,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,UAAU,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AAC9C,IAAI,CAAC,EAAE,GAAG,cAAc,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AACxE,MAAM,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;AAC1C,KAAK,CAAC,CAAC;AACP,GAAG;AACH,EAAE,SAAS,aAAa,CAAC,GAAG,EAAE;AAC9B,IAAI,cAAc,EAAE,CAAC;AACrB,IAAI,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,GAAG,CAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;AAC7D,GAAG;AACH,EAAE,SAAS,oBAAoB,CAAC,EAAE,EAAE;AACpC,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AACzB,IAAI,IAAI,CAACC,eAAU,CAAC,EAAE,EAAEA,eAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACzD,MAAM,cAAc,EAAE,CAAC;AACvB,MAAM,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AACxC,MAAM,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC;AACjC,MAAM,EAAE,CAAC,cAAc,EAAE,CAAC;AAC1B,MAAM,EAAE,CAAC,eAAe,EAAE,CAAC;AAC3B,KAAK,MAAM,IAAI,IAAI,KAAKA,eAAU,CAAC,KAAK,EAAE;AAC1C,MAAM,SAAS,CAAC,WAAW,EAAE,CAAC;AAC9B,KAAK,MAAM,IAAI,CAACA,eAAU,CAAC,GAAG,EAAEA,eAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AAChE,MAAM,SAAS,CAAC,IAAI,EAAE,CAAC;AACvB,KAAK;AACL,GAAG;AACH,EAAE,SAAS,iBAAiB,CAAC,EAAE,EAAE;AACjC,IAAI,MAAM,IAAI,GAAG,EAAE,CAAC,IAAI,CAAC;AACzB,IAAI,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC;AAC7B,IAAI,MAAM,YAAY,GAAG,cAAc,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AAC9D,IAAI,MAAM,GAAG,GAAG,cAAc,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;AAChD,IAAI,IAAI,SAAS,CAAC;AAClB,IAAI,IAAI,CAACA,eAAU,CAAC,EAAE,EAAEA,eAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACzD,MAAM,IAAI,IAAI,KAAKA,eAAU,CAAC,EAAE,EAAE;AAClC,QAAQ,SAAS,GAAG,YAAY,KAAK,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;AAC9D,OAAO,MAAM;AACb,QAAQ,SAAS,GAAG,YAAY,GAAG,GAAG,GAAG,YAAY,GAAG,CAAC,GAAG,GAAG,CAAC;AAChE,OAAO;AACP,MAAM,cAAc,EAAE,CAAC;AACvB,MAAM,aAAa,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AAChD,MAAM,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,KAAK,EAAE,CAAC;AACzC,MAAM,EAAE,CAAC,cAAc,EAAE,CAAC;AAC1B,MAAM,EAAE,CAAC,eAAe,EAAE,CAAC;AAC3B,KAAK,MAAM,IAAI,IAAI,KAAKA,eAAU,CAAC,KAAK,EAAE;AAC1C,MAAM,eAAe,EAAE,CAAC;AACxB,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC;AACrB,MAAM,IAAI,SAAS,CAAC,KAAK,CAAC,WAAW,EAAE;AACvC,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;AACzB,OAAO;AACP,KAAK,MAAM,IAAI,CAACA,eAAU,CAAC,GAAG,EAAEA,eAAU,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AAChE,MAAM,SAAS,CAAC,IAAI,EAAE,CAAC;AACvB,MAAM,eAAe,EAAE,CAAC;AACxB,KAAK;AACL,GAAG;AACH,EAAE,SAAS,QAAQ,GAAG;AACtB,IAAI,WAAW,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;AACvD,IAAI,UAAU,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC;AACrD,IAAI,UAAU,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC;AAC3D,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,EAAE;AACtC,MAAM,UAAU,CAAC,YAAY,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAChD,MAAM,UAAU,CAAC,YAAY,CAAC,UAAU,EAAE,SAAS,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AACpE,MAAMC,cAAQ,CAAC,UAAU,EAAE,EAAE,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC;AAC/C,KAAK;AACL,GAAG;AACH,EAAE,SAAS,SAAS,GAAG;AACvB,IAAI,IAAI,EAAE,CAAC;AACX,IAAI,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,UAAU,CAAC,gBAAgB,CAAC,SAAS,EAAE,oBAAoB,CAAC,CAAC;AAC/F,IAAI,CAAC,EAAE,GAAG,WAAW,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG,EAAE,CAAC,gBAAgB,CAAC,SAAS,EAAE,iBAAiB,EAAE,IAAI,CAAC,CAAC;AACxG,GAAG;AACH,EAAE,SAAS,gBAAgB,GAAG;AAC9B,IAAI,SAAS,CAAC,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,CAAC;AAC5E,IAAI,cAAc,CAAC,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;AACvD,IAAI,SAAS,EAAE,CAAC;AAChB,IAAI,QAAQ,EAAE,CAAC;AACf,GAAG;AACH,EAAE,SAAS,eAAe,GAAG;AAC7B,IAAI,UAAU,CAAC,KAAK,EAAE,CAAC;AACvB,GAAG;AACH,EAAE,gBAAgB,EAAE,CAAC;AACrB;;;;;"}