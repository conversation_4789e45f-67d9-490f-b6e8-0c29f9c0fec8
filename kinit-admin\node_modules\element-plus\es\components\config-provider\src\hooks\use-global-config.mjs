import { ref, getCurrentInstance, inject, computed, unref, provide } from 'vue';
import '../../../../utils/index.mjs';
import '../../../../hooks/index.mjs';
import { configProviderContextKey } from '../constants.mjs';
import { useNamespace, defaultNamespace, namespaceContextKey } from '../../../../hooks/use-namespace/index.mjs';
import { useLocale, localeContextKey } from '../../../../hooks/use-locale/index.mjs';
import { useZIndex, defaultInitialZIndex, zIndexContextKey } from '../../../../hooks/use-z-index/index.mjs';
import { debugWarn } from '../../../../utils/error.mjs';
import { SIZE_INJECTION_KEY } from '../../../../hooks/use-size/index.mjs';
import { keysOf } from '../../../../utils/objects.mjs';

const globalConfig = ref();
function useGlobalConfig(key, defaultValue = void 0) {
  const config = getCurrentInstance() ? inject(configProviderContextKey, globalConfig) : globalConfig;
  if (key) {
    return computed(() => {
      var _a, _b;
      return (_b = (_a = config.value) == null ? void 0 : _a[key]) != null ? _b : defaultValue;
    });
  } else {
    return config;
  }
}
function useGlobalComponentSettings(block, sizeFallback) {
  const config = useGlobalConfig();
  const ns = useNamespace(block, computed(() => {
    var _a;
    return ((_a = config.value) == null ? void 0 : _a.namespace) || defaultNamespace;
  }));
  const locale = useLocale(computed(() => {
    var _a;
    return (_a = config.value) == null ? void 0 : _a.locale;
  }));
  const zIndex = useZIndex(computed(() => {
    var _a;
    return ((_a = config.value) == null ? void 0 : _a.zIndex) || defaultInitialZIndex;
  }));
  const size = computed(() => {
    var _a;
    return unref(sizeFallback) || ((_a = config.value) == null ? void 0 : _a.size) || "";
  });
  provideGlobalConfig(computed(() => unref(config) || {}));
  return {
    ns,
    locale,
    zIndex,
    size
  };
}
const provideGlobalConfig = (config, app, global = false) => {
  var _a;
  const inSetup = !!getCurrentInstance();
  const oldConfig = inSetup ? useGlobalConfig() : void 0;
  const provideFn = (_a = app == null ? void 0 : app.provide) != null ? _a : inSetup ? provide : void 0;
  if (!provideFn) {
    debugWarn("provideGlobalConfig", "provideGlobalConfig() can only be used inside setup().");
    return;
  }
  const context = computed(() => {
    const cfg = unref(config);
    if (!(oldConfig == null ? void 0 : oldConfig.value))
      return cfg;
    return mergeConfig(oldConfig.value, cfg);
  });
  provideFn(configProviderContextKey, context);
  provideFn(localeContextKey, computed(() => context.value.locale));
  provideFn(namespaceContextKey, computed(() => context.value.namespace));
  provideFn(zIndexContextKey, computed(() => context.value.zIndex));
  provideFn(SIZE_INJECTION_KEY, {
    size: computed(() => context.value.size || "")
  });
  if (global || !globalConfig.value) {
    globalConfig.value = context.value;
  }
  return context;
};
const mergeConfig = (a, b) => {
  var _a;
  const keys = [.../* @__PURE__ */ new Set([...keysOf(a), ...keysOf(b)])];
  const obj = {};
  for (const key of keys) {
    obj[key] = (_a = b[key]) != null ? _a : a[key];
  }
  return obj;
};

export { provideGlobalConfig, useGlobalComponentSettings, useGlobalConfig };
//# sourceMappingURL=use-global-config.mjs.map
