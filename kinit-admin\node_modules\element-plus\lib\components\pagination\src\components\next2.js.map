{"version": 3, "file": "next2.js", "sources": ["../../../../../../../packages/components/pagination/src/components/next.vue"], "sourcesContent": ["<template>\n  <button\n    type=\"button\"\n    class=\"btn-next\"\n    :disabled=\"internalDisabled\"\n    :aria-label=\"nextText || t('el.pagination.next')\"\n    :aria-disabled=\"internalDisabled\"\n    @click=\"$emit('click', $event)\"\n  >\n    <span v-if=\"nextText\">{{ nextText }}</span>\n    <el-icon v-else>\n      <component :is=\"nextIcon\" />\n    </el-icon>\n  </button>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { useLocale } from '@element-plus/hooks'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { paginationNextProps } from './next'\n\ndefineOptions({\n  name: 'ElPaginationNext',\n})\n\nconst props = defineProps(paginationNextProps)\n\ndefineEmits(['click'])\n\nconst { t } = useLocale()\n\nconst internalDisabled = computed(\n  () =>\n    props.disabled ||\n    props.currentPage === props.pageCount ||\n    props.pageCount === 0\n)\n</script>\n"], "names": ["useLocale", "computed"], "mappings": ";;;;;;;;;;;;;uCAsBc,CAAA;AAAA,EACZ,IAAM,EAAA,kBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAMA,IAAM,MAAA,EAAE,MAAMA,eAAU,EAAA,CAAA;AAExB,IAAM,MAAA,gBAAA,GAAmBC,YACvB,CAAA,MACE,KAAM,CAAA,QAAA,IACN,KAAM,CAAA,WAAA,KAAgB,KAAM,CAAA,SAAA,IAC5B,KAAM,CAAA,SAAA,KAAc,CACxB,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;"}