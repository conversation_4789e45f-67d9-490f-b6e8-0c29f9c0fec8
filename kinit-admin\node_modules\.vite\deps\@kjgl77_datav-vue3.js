import "./chunk-2ECQ6UXT.js";
import {
  Fragment,
  computed2 as computed,
  createBaseVNode,
  createBlock,
  createCommentVNode,
  createElementBlock,
  createStaticVNode,
  createVNode,
  defineComponent,
  getCurrentInstance,
  getCurrentScope,
  h,
  nextTick,
  normalizeClass,
  normalizeStyle,
  onBeforeUnmount,
  onMounted,
  onScopeDispose,
  onUnmounted,
  openBlock,
  popScopeId,
  pushScopeId,
  reactive,
  ref,
  renderList,
  renderSlot,
  resolveDynamicComponent,
  toDisplayString,
  toRef,
  unref,
  useCssVars,
  watch,
  watchEffect
} from "./chunk-NS63M6FW.js";
import "./chunk-GFT2G5UO.js";

// node_modules/@kjgl77/datav-vue3/dist/datav-vue3.es.js
import "F:/share/mxtt/kinit-admin/node_modules/@kjgl77/datav-vue3/dist/style.css";
var ii = ((e) => (e.transparent = "rgba(0,0,0,0)", e.black = "#000000", e.silver = "#C0C0C0", e.gray = "#808080", e.white = "#FFFFFF", e.maroon = "#800000", e.red = "#FF0000", e.purple = "#800080", e.fuchsia = "#FF00FF", e.green = "#008000", e.lime = "#00FF00", e.olive = "#808000", e.yellow = "#FFFF00", e.navy = "#000080", e.blue = "#0000FF", e.teal = "#008080", e.aqua = "#00FFFF", e.aliceblue = "#f0f8ff", e.antiquewhite = "#faebd7", e.aquamarine = "#7fffd4", e.azure = "#f0ffff", e.beige = "#f5f5dc", e.bisque = "#ffe4c4", e.blanchedalmond = "#ffebcd", e.blueviolet = "#8a2be2", e.brown = "#a52a2a", e.burlywood = "#deb887", e.cadetblue = "#5f9ea0", e.chartreuse = "#7fff00", e.chocolate = "#d2691e", e.coral = "#ff7f50", e.cornflowerblue = "#6495ed", e.cornsilk = "#fff8dc", e.crimson = "#dc143c", e.cyan = "#00ffff", e.darkblue = "#00008b", e.darkcyan = "#008b8b", e.darkgoldenrod = "#b8860b", e.darkgray = "#a9a9a9", e.darkgreen = "#006400", e.darkgrey = "#a9a9a9", e.darkkhaki = "#bdb76b", e.darkmagenta = "#8b008b", e.darkolivegreen = "#556b2f", e.darkorange = "#ff8c00", e.darkorchid = "#9932cc", e.darkred = "#8b0000", e.darksalmon = "#e9967a", e.darkseagreen = "#8fbc8f", e.darkslateblue = "#483d8b", e.darkslategray = "#2f4f4f", e.darkslategrey = "#2f4f4f", e.darkturquoise = "#00ced1", e.darkviolet = "#9400d3", e.deeppink = "#ff1493", e.deepskyblue = "#00bfff", e.dimgray = "#696969", e.dimgrey = "#696969", e.dodgerblue = "#1e90ff", e.firebrick = "#b22222", e.floralwhite = "#fffaf0", e.forestgreen = "#228b22", e.gainsboro = "#dcdcdc", e.ghostwhite = "#f8f8ff", e.gold = "#ffd700", e.goldenrod = "#daa520", e.greenyellow = "#adff2f", e.grey = "#808080", e.honeydew = "#f0fff0", e.hotpink = "#ff69b4", e.indianred = "#cd5c5c", e.indigo = "#4b0082", e.ivory = "#fffff0", e.khaki = "#f0e68c", e.lavender = "#e6e6fa", e.lavenderblush = "#fff0f5", e.lawngreen = "#7cfc00", e.lemonchiffon = "#fffacd", e.lightblue = "#add8e6", e.lightcoral = "#f08080", e.lightcyan = "#e0ffff", e.lightgoldenrodyellow = "#fafad2", e.lightgray = "#d3d3d3", e.lightgreen = "#90ee90", e.lightgrey = "#d3d3d3", e.lightpink = "#ffb6c1", e.lightsalmon = "#ffa07a", e.lightseagreen = "#20b2aa", e.lightskyblue = "#87cefa", e.lightslategray = "#778899", e.lightslategrey = "#778899", e.lightsteelblue = "#b0c4de", e.lightyellow = "#ffffe0", e.limegreen = "#32cd32", e.linen = "#faf0e6", e.magenta = "#ff00ff", e.mediumaquamarine = "#66cdaa", e.mediumblue = "#0000cd", e.mediumorchid = "#ba55d3", e.mediumpurple = "#9370db", e.mediumseagreen = "#3cb371", e.mediumslateblue = "#7b68ee", e.mediumspringgreen = "#00fa9a", e.mediumturquoise = "#48d1cc", e.mediumvioletred = "#c71585", e.midnightblue = "#191970", e.mintcream = "#f5fffa", e.mistyrose = "#ffe4e1", e.moccasin = "#ffe4b5", e.navajowhite = "#ffdead", e.oldlace = "#fdf5e6", e.olivedrab = "#6b8e23", e.orange = "#ffa500", e.orangered = "#ff4500", e.orchid = "#da70d6", e.palegoldenrod = "#eee8aa", e.palegreen = "#98fb98", e.paleturquoise = "#afeeee", e.palevioletred = "#db7093", e.papayawhip = "#ffefd5", e.peachpuff = "#ffdab9", e.peru = "#cd853f", e.pink = "#ffc0cb", e.plum = "#dda0dd", e.powderblue = "#b0e0e6", e.rosybrown = "#bc8f8f", e.royalblue = "#4169e1", e.saddlebrown = "#8b4513", e.salmon = "#fa8072", e.sandybrown = "#f4a460", e.seagreen = "#2e8b57", e.seashell = "#fff5ee", e.sienna = "#a0522d", e.skyblue = "#87ceeb", e.slateblue = "#6a5acd", e.slategray = "#708090", e.snow = "#fffafa", e.springgreen = "#00ff7f", e.steelblue = "#4682b4", e.tan = "#d2b48c", e.thistle = "#d8bfd8", e.tomato = "#ff6347", e.turquoise = "#40e0d0", e.violet = "#ee82ee", e.wheat = "#f5deb3", e.whitesmoke = "#f5f5f5", e.yellowgreen = "#9acd32", e))(ii || {});
function Kt(e) {
  return typeof e != "string" ? false : (e = e.toLowerCase(), /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/.test(e));
}
function Yi(e) {
  return typeof e != "string" ? false : (e = e.toLowerCase(), /^(rgb\(|RGB\()/.test(e));
}
function oi(e) {
  return typeof e != "string" ? false : (e = e.toLowerCase(), /^(rgba|RGBA)/.test(e));
}
function li(e) {
  return /^(rgb|rgba|RGB|RGBA)/.test(e);
}
function Ki(e) {
  return ii[e];
}
function si(e) {
  if (Kt(e) || li(e))
    return e;
  const t = Ki(e);
  if (!t)
    throw new Error(`Color: Invalid Input of ${e}`);
  return t;
}
function Ji(e) {
  e = e.replace("#", ""), e.length === 3 && (e = Array.from(e).map((r) => r + r).join(""));
  const t = e.split("");
  return new Array(3).fill(0).map((r, n) => parseInt(`0x${t[n * 2]}${t[n * 2 + 1]}`));
}
function Zi(e) {
  return e.replace(/rgb\(|rgba\(|\)/g, "").split(",").slice(0, 3).map((t) => parseInt(t));
}
function At(e) {
  const t = si(e).toLowerCase();
  return Kt(t) ? Ji(t) : Zi(t);
}
function ui(e) {
  const t = si(e);
  return oi(t) ? Number(
    t.toLowerCase().split(",").slice(-1)[0].replace(/[)|\s]/g, "")
  ) : 1;
}
function On(e) {
  const t = At(e);
  return t && [...t, ui(e)];
}
function eo(e, t) {
  const r = At(e);
  return typeof t == "number" ? `rgba(${r.join(",")},${t})` : `rgb(${r.join(",")})`;
}
function to(e) {
  if (Kt(e))
    return e;
  const t = At(e), r = (n) => Number(n).toString(16).padStart(2, "0");
  return `#${t.map(r).join("")}`;
}
function Jt(e) {
  if (!Array.isArray(e))
    throw new Error(`getColorFromRgbValue: ${e} is not an array`);
  const { length: t } = e;
  if (t !== 3 && t !== 4)
    throw new Error("getColorFromRgbValue: value length should be 3 or 4");
  return (t === 3 ? "rgb(" : "rgba(") + e.join(",") + ")";
}
function ro(e, t = 0) {
  let r = On(e);
  return r = r.map((n, a) => a === 3 ? n : n - Math.ceil(2.55 * t)).map((n) => n < 0 ? 0 : n), Jt(r);
}
function Pn(e, t = 0) {
  let r = On(e);
  return r = r.map((n, a) => a === 3 ? n : n + Math.ceil(2.55 * t)).map((n) => n > 255 ? 255 : n), Jt(r);
}
function De(e, t = 100) {
  const r = At(e);
  return Jt([...r, t / 100]);
}
var no = Object.freeze(Object.defineProperty({
  __proto__: null,
  darken: ro,
  fade: De,
  getColorFromRgbValue: Jt,
  getOpacity: ui,
  getRgbValue: At,
  getRgbaValue: On,
  isHex: Kt,
  isRgb: Yi,
  isRgbOrRgba: li,
  isRgba: oi,
  lighten: Pn,
  toHex: to,
  toRgb: eo
}, Symbol.toStringTag, { value: "Module" }));
var Ze = (e, t) => {
  const r = e.__vccOpts || e;
  for (const [n, a] of t)
    r[n] = a;
  return r;
};
var ao = {};
var io = {
  viewBox: "0 0 187 38",
  preserveAspectRatio: "none",
  class: "dv-button-svg"
};
var oo = createBaseVNode("g", { style: { transform: "translate(2px, 2px)" } }, [
  createBaseVNode("g", null, [
    createBaseVNode("path", {
      "data-type": "shape",
      d: "M0,0 L0,34 L168,34 L183,19 L183,0",
      class: "dv-button-svg-bg"
    })
  ]),
  createBaseVNode("path", {
    "data-type": "polyline",
    d: "M0,34 L168,34 L183,19",
    class: "dv-button-svg-line"
  })
], -1);
var lo = [
  oo
];
function so(e, t) {
  return openBlock(), createElementBlock("svg", io, lo);
}
var uo = Ze(ao, [["render", so]]);
var co = {};
var fo = {
  viewBox: "0 0 167 38",
  preserveAspectRatio: "none",
  class: "dv-button-svg"
};
var ho = createStaticVNode('<g style="transform:translate(2px, 2px);"><g><path data-type="shape" d="M0,0 L0,34 L163,34 L163,0" class="dv-button-svg-bg"></path></g><path data-type="polyline" d="M0,0 L164.1,0" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,0 L163,34" class="dv-button-svg-line"></path><path data-type="polyline" d="M164.1,34 L0,34" class="dv-button-svg-line"></path><path data-type="polyline" d="M1.1,34 L1.1,0" class="dv-button-svg-line"></path></g>', 1);
var vo = [
  ho
];
function po(e, t) {
  return openBlock(), createElementBlock("svg", fo, vo);
}
var go = Ze(co, [["render", po]]);
var mo = {};
var yo = {
  viewBox: "0 0 167 38",
  preserveAspectRatio: "none",
  class: "dv-button-svg"
};
var bo = createStaticVNode('<g style="transform:translate(2px, 2px);"><g><path data-type="shape" d="M1,1 L1,33 L162,33 L162,1" class="dv-button-svg-bg"></path></g><path data-type="polyline" d="M0,0 L0,10" class="dv-button-svg-line"></path><path data-type="polyline" d="M-1.1,0 L10,0" class="dv-button-svg-line"></path><path data-type="polyline" d="M164.1,0 L153,0" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,0 L163,10" class="dv-button-svg-line"></path><path data-type="polyline" d="M164.1,34 L153,34" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,34 L163,24" class="dv-button-svg-line"></path><path data-type="polyline" d="M0,34 L0,24" class="dv-button-svg-line"></path><path data-type="polyline" d="M-1.1,34 L10,34" class="dv-button-svg-line"></path></g>', 1);
var xo = [
  bo
];
function Co(e, t) {
  return openBlock(), createElementBlock("svg", yo, xo);
}
var _o = Ze(mo, [["render", Co]]);
var $o = {};
var Po = {
  viewBox: "0 0 187 38",
  preserveAspectRatio: "none",
  class: "dv-button-svg"
};
var wo = createBaseVNode("g", { style: { transform: "translate(2px, 2px)" } }, [
  createBaseVNode("g", null, [
    createBaseVNode("path", {
      "data-type": "shape",
      d: "M0,34 L168,34 L183,19 L183,0 L0,0",
      class: "dv-button-svg-bg"
    })
  ]),
  createBaseVNode("path", {
    "data-type": "polyline",
    d: "M0,34 L168,34 L183,19 L183,0",
    class: "dv-button-svg-line"
  }),
  createBaseVNode("path", {
    "data-type": "polyline",
    d: "M184.1,0 L0,0 L0,34.7",
    class: "dv-button-svg-line"
  })
], -1);
var ko = [
  wo
];
function Ao(e, t) {
  return openBlock(), createElementBlock("svg", Po, ko);
}
var Lo = Ze($o, [["render", Ao]]);
var So = {};
var Oo = {
  viewBox: "0 0 187 38",
  preserveAspectRatio: "none",
  class: "dv-button-svg"
};
var Go = createBaseVNode("g", { style: { transform: "translate(2px, 2px)" } }, [
  createBaseVNode("g", null, [
    createBaseVNode("path", {
      "data-type": "shape",
      d: "M0,34 L168,34 L183,19 L183,0 L15,0 L0,15",
      class: "dv-button-svg-bg"
    })
  ]),
  createBaseVNode("path", {
    "data-type": "polyline",
    d: "M0,34 L168,34 L183,19 L183,0",
    class: "dv-button-svg-line"
  }),
  createBaseVNode("path", {
    "data-type": "polyline",
    d: "M183,0 L15,0 L0,15 L0,34",
    class: "dv-button-svg-line"
  })
], -1);
var Mo = [
  Go
];
function Ro(e, t) {
  return openBlock(), createElementBlock("svg", Oo, Mo);
}
var To = Ze(So, [["render", Ro]]);
var Do = {};
var Bo = {
  viewBox: "0 0 167 38",
  preserveAspectRatio: "none",
  class: "dv-button-svg"
};
var Fo = createStaticVNode('<g style="transform:translate(2px, 2px);"><g><path data-type="shape" d="M0,0 L0,34 L163,34 L163,0" class="dv-button-svg-bg"></path></g><path data-type="polyline" d="M0,0 L81.6,0" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,0 L81.4,0" class="dv-button-svg-line"></path><path data-type="polyline" d="M0,34 L81.6,34" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,34 L81.4,34" class="dv-button-svg-line"></path><path data-type="polyline" d="M0,1 L10,1" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,1 L153,1" class="dv-button-svg-line"></path><path data-type="polyline" d="M0,33 L10,33" class="dv-button-svg-line"></path><path data-type="polyline" d="M163,33 L153,33" class="dv-button-svg-line"></path></g>', 1);
var No = [
  Fo
];
function jo(e, t) {
  return openBlock(), createElementBlock("svg", Bo, No);
}
var Eo = Ze(Do, [["render", jo]]);
var Wo = { class: "dv-button-wrapper" };
var zo = { class: "dv-button" };
var qo = { class: "dv-button-svg-container" };
var Io = { class: "dv-button-text" };
var Ho = defineComponent({
  components: {
    Border1: uo,
    Border2: go,
    Border3: _o,
    Border4: Lo,
    Border5: To,
    Border6: Eo
  },
  __name: "index",
  props: {
    color: { default: "#2058c7" },
    fontColor: { default: "" },
    bg: { type: Boolean, default: true },
    border: { type: [String, Object, Function], default: "Border1" },
    fontSize: { default: 14 }
  },
  setup(e) {
    const t = e;
    useCssVars((s) => ({
      "3b09a6e4": unref(l),
      "5f757885": s.color,
      "505f902a": unref(n),
      "714af7a5": unref(r),
      ea6738d4: unref(a),
      "1e0a24df": unref(o)
    }));
    const r = computed(() => Pn(t.color, 40)), n = computed(() => t.fontColor === "" ? t.color : t.fontColor), a = computed(() => Pn(n.value, 40)), o = computed(() => t.bg ? 0.1 : 0), l = computed(() => `${t.fontSize}px`);
    return (s, D) => (openBlock(), createElementBlock("div", Wo, [
      createBaseVNode("button", zo, [
        createBaseVNode("div", qo, [
          (openBlock(), createBlock(resolveDynamicComponent(s.border)))
        ]),
        createBaseVNode("div", Io, [
          renderSlot(s.$slots, "default")
        ])
      ])
    ]));
  }
});
var ir = {
  install(e) {
    e.component("DvButton", Ho);
  }
};
function Vo(e) {
  return getCurrentScope() ? (onScopeDispose(e), true) : false;
}
function Ht(e) {
  return typeof e == "function" ? e() : unref(e);
}
var Uo = typeof window < "u";
var wn = () => {
};
function Xo(e, t) {
  function r(...n) {
    return new Promise((a, o) => {
      Promise.resolve(e(() => t.apply(this, n), { fn: t, thisArg: this, args: n })).then(a).catch(o);
    });
  }
  return r;
}
function Qo(e, t = {}) {
  let r, n, a = wn;
  const o = (s) => {
    clearTimeout(s), a(), a = wn;
  };
  return (s) => {
    const D = Ht(e), W = Ht(t.maxWait);
    return r && o(r), D <= 0 || W !== void 0 && W <= 0 ? (n && (o(n), n = null), Promise.resolve(s())) : new Promise((M, U) => {
      a = t.rejectOnCancel ? U : M, W && !n && (n = setTimeout(() => {
        r && o(r), n = null, M(s());
      }, W)), r = setTimeout(() => {
        n && o(n), n = null, M(s());
      }, D);
    });
  };
}
function Yo(e, t = 200, r = {}) {
  return Xo(
    Qo(t, r),
    e
  );
}
function Ko(e) {
  var t;
  const r = Ht(e);
  return (t = r == null ? void 0 : r.$el) != null ? t : r;
}
var Jo = Uo ? window : void 0;
function Zo(...e) {
  let t, r, n, a;
  if (typeof e[0] == "string" || Array.isArray(e[0]) ? ([r, n, a] = e, t = Jo) : [t, r, n, a] = e, !t)
    return wn;
  Array.isArray(r) || (r = [r]), Array.isArray(n) || (n = [n]);
  const o = [], l = () => {
    o.forEach((M) => M()), o.length = 0;
  }, s = (M, U, F, A) => (M.addEventListener(U, F, A), () => M.removeEventListener(U, F, A)), D = watch(
    () => [Ko(t), Ht(a)],
    ([M, U]) => {
      l(), M && o.push(
        ...r.flatMap((F) => n.map((A) => s(M, F, A, U)))
      );
    },
    { immediate: true, flush: "post" }
  ), W = () => {
    D(), l();
  };
  return Vo(W), W;
}
function _t(e, t) {
  return arguments.length === 1 ? parseInt((Math.random() * e + 1).toString(), 10) : parseInt((Math.random() * (t - e + 1) + e).toString(), 10);
}
function el(e, t) {
  const r = window.MutationObserver, n = new r(t);
  return n.observe(e, { attributes: true, attributeFilter: ["style"], attributeOldValue: true }), n;
}
function Vt(e, t) {
  const r = Math.abs(e[0] - t[0]), n = Math.abs(e[1] - t[1]);
  return Math.sqrt(r * r + n * n);
}
function at(e, t, r, n) {
  return [e + Math.cos(n) * r, t + Math.sin(n) * r];
}
function tl(e) {
  return e.filter((t) => typeof t == "number");
}
function rl(e) {
  return e = tl(e), e.reduce((t, r) => t + r, 0);
}
function nl(e, t) {
  const r = Math.abs(e.x - t.x), n = Math.abs(e.y - t.y);
  return Math.sqrt(r * r + n * n);
}
function Nn(e) {
  const r = new Array(e.length - 1).fill(0).map((n, a) => [e[a], e[a + 1]]).map((n) => nl(n[0], n[1]));
  return rl(r);
}
function al(e) {
  return `${e.x},${e.y}`;
}
function jn(e) {
  return e.map(al).join(" ");
}
function Ve(e) {
  return (e ? "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx" : "xxxxxxxxxxxx4xxxyxxxxxxxxxxxxxxx").replace(/[xy]/g, (t) => {
    const r = Math.random() * 16 | 0;
    return (t === "x" ? r : r & 3 | 8).toString(16);
  });
}
function Ce(e, t) {
  for (const r in t) {
    if (e[r] && typeof e[r] == "object") {
      Ce(e[r], t[r]);
      continue;
    }
    if (typeof t[r] == "object") {
      e[r] = $e(t[r], true);
      continue;
    }
    e[r] = t[r];
  }
  return e;
}
function $e(e, t) {
  if (!e)
    return e;
  const { parse: r, stringify: n } = JSON;
  if (!t)
    return r(n(e));
  const a = Array.isArray(e) ? [] : {};
  if (e && typeof e == "object")
    for (const o in e)
      Object.prototype.hasOwnProperty.call(e, o) && (e[o] && typeof e[o] == "object" ? a[o] = $e(e[o], true) : a[o] = e[o]);
  return a;
}
var xe = (e, t, r) => {
  const n = ref(0), a = ref(0);
  let o, l = null, s = null;
  const D = (A = true) => new Promise((v) => {
    nextTick(() => {
      s = e.value, n.value = e.value ? e.value.clientWidth : 0, a.value = e.value ? e.value.clientHeight : 0, e.value ? (!n.value || !a.value) && console.warn("DataV: Component width or height is 0px, rendering abnormality may occur!") : console.warn("DataV: Failed to get dom node, component rendering may be abnormal!"), typeof t == "function" && A && t(), v(true);
    });
  }), W = () => {
    o = Yo(D, 200);
  }, M = () => {
    l = el(s, o), Zo(window, "resize", o);
  }, U = () => {
    l && (l.disconnect(), l.takeRecords(), l = null);
  }, F = async () => {
    await D(false), W(), M(), typeof r == "function" && r();
  };
  return onMounted(() => {
    F();
  }), onUnmounted(() => {
    U();
  }), {
    width: n,
    height: a,
    initWH: D
  };
};
var il = ["width", "height"];
var ol = ["d", "fill"];
var ll = ["fill", "x", "y"];
var sl = ["xlink:href", "width", "height", "x", "y"];
var ul = ["fill", "x", "y"];
var cl = {
  __name: "index",
  props: {
    config: {
      type: Object,
      default: () => ({})
    }
  },
  setup(e) {
    const t = e;
    useCssVars((A) => ({
      "5914205c": unref(l)
    }));
    const r = ref(null), { width: n, height: a } = xe(r, D, s), o = reactive({
      defaultConfig: {
        /**
             * @description Chart data
             * @type {Array<Object>}
             * @default data = []
             */
        data: [],
        /**
             * @description Chart img
             * @type {Array<String>}
             * @default img = []
             */
        img: [],
        /**
             * @description Chart font size
             * @type {Number}
             * @default fontSize = 12
             */
        fontSize: 12,
        /**
             * @description Img side length
             * @type {Number}
             * @default imgSideLength = 30
             */
        imgSideLength: 30,
        /**
             * @description Column color
             * @type {String}
             * @default columnColor = 'rgba(0, 194, 255, 0.4)'
             */
        columnColor: "rgba(0, 194, 255, 0.4)",
        /**
             * @description Text color
             * @type {String}
             * @default textColor = '#fff'
             */
        textColor: "#fff",
        /**
             * @description Show value
             * @type {Boolean}
             * @default showValue = false
             */
        showValue: false,
        /**
             * @description Auto sort by value
             * @type {Boolean}
             * @default sort = true
             */
        sort: true
      },
      mergedConfig: null,
      column: []
    }), l = computed(() => `${t.config.fontSize ? t.config.fontSize : o.defaultConfig.fontSize}px`);
    watch(() => t.config, () => {
      W();
    }, {
      deep: true
    });
    function s() {
      W();
    }
    function D() {
      W();
    }
    function W() {
      M(), U(), F();
    }
    function M() {
      o.mergedConfig = Ce($e(o.defaultConfig, true), t.config || {});
    }
    function U() {
      let { data: A } = o.mergedConfig;
      const { sort: v } = o.mergedConfig;
      A = $e(A, true), v && A.sort(({ value: N }, { value: I }) => N > I ? -1 : N < I ? 1 : 0);
      const R = Math.max(...A.map((N) => N.value));
      A = A.map((N) => ({
        ...N,
        percent: R === 0 ? 0 : N.value / R
      })), o.mergedConfig.data = A;
    }
    function F() {
      const { imgSideLength: A, fontSize: v, data: R } = o.mergedConfig, N = R.length, I = n.value / (N + 1), E = a.value - A - v - 5, b = a.value - v - 5;
      o.column = R.map(($, f) => {
        const { percent: _ } = $, O = I * (f + 1), m = I * f, C = I * (f + 2), d = b - E * _, G = E * _ * 0.6 + d, L = `
          M${m}, ${b}
          Q${O}, ${G} ${O},${d}
          M${O},${d}
          Q${O}, ${G} ${C},${b}
          L${m}, ${b}
          Z
        `, g = (b + d) / 2 + v / 2;
        return {
          ...$,
          d: L,
          x: O,
          y: d,
          textY: g
        };
      });
    }
    return (A, v) => (openBlock(), createElementBlock("div", {
      ref_key: "conicalColumnChart",
      ref: r,
      class: "dv-conical-column-chart"
    }, [
      (openBlock(), createElementBlock("svg", {
        width: unref(n),
        height: unref(a)
      }, [
        (openBlock(true), createElementBlock(Fragment, null, renderList(unref(o).column, (R, N) => (openBlock(), createElementBlock("g", { key: N }, [
          createBaseVNode("path", {
            d: R.d,
            fill: unref(o).mergedConfig.columnColor
          }, null, 8, ol),
          createBaseVNode("text", {
            fill: unref(o).mergedConfig.textColor,
            x: R.x,
            y: unref(a) - 4
          }, toDisplayString(R.name), 9, ll),
          unref(o).mergedConfig.img.length ? (openBlock(), createElementBlock("image", {
            key: 0,
            "xlink:href": unref(o).mergedConfig.img[N % unref(o).mergedConfig.img.length],
            width: unref(o).mergedConfig.imgSideLength,
            height: unref(o).mergedConfig.imgSideLength,
            x: R.x - unref(o).mergedConfig.imgSideLength / 2,
            y: R.y - unref(o).mergedConfig.imgSideLength
          }, null, 8, sl)) : createCommentVNode("", true),
          unref(o).mergedConfig.showValue ? (openBlock(), createElementBlock("text", {
            key: 1,
            fill: unref(o).mergedConfig.textColor,
            x: R.x,
            y: R.textY
          }, toDisplayString(R.value), 9, ul)) : createCommentVNode("", true)
        ]))), 128))
      ], 8, il))
    ], 512));
  }
};
var or = {
  install(e) {
    e.component("DvConicalColumnChart", cl);
  }
};
var fl = ["id"];
var dl = ["offset", "stop-color"];
var hl = ["id", "x2"];
var vl = ["offset", "stop-color"];
var pl = ["x", "y", "rx", "ry", "stroke-width", "stroke", "width", "height"];
var gl = ["stroke-width", "stroke-dasharray", "stroke", "points"];
var ml = ["stroke", "fill", "x", "y"];
var yl = {
  __name: "index",
  props: {
    config: {
      type: Object,
      default: () => ({})
    }
  },
  setup(e) {
    const t = e, r = Ve(), n = ref(null), a = reactive({
      gradientId1: `percent-pond-gradientId1-${r}`,
      gradientId2: `percent-pond-gradientId2-${r}`,
      width: 0,
      height: 0,
      defaultConfig: {
        /**
             * @description Value
             * @type {Number}
             * @default value = 0
             */
        value: 0,
        /**
             * @description Colors (hex|rgb|rgba|color keywords)
             * @type {Array<String>}
             * @default colors = ['#00BAFF', '#3DE7C9']
             * @example colors = ['#000', 'rgb(0, 0, 0)', 'rgba(0, 0, 0, 1)', 'red']
             */
        colors: ["#3DE7C9", "#00BAFF"],
        /**
             * @description Border width
             * @type {Number}
             * @default borderWidth = 3
             */
        borderWidth: 3,
        /**
             * @description Gap between border and pond
             * @type {Number}
             * @default borderGap = 3
             */
        borderGap: 3,
        /**
             * @description Line dash
             * @type {Array<Number>}
             * @default lineDash = [5, 1]
             */
        lineDash: [5, 1],
        /**
             * @description Text color
             * @type {String}
             * @default textColor = '#fff'
             */
        textColor: "#fff",
        /**
             * @description Border radius
             * @type {Number}
             * @default borderRadius = 5
             */
        borderRadius: 5,
        /**
             * @description Local Gradient
             * @type {Boolean}
             * @default localGradient = false
             * @example localGradient = false | true
             */
        localGradient: false,
        /**
             * @description Formatter
             * @type {String}
             * @default formatter = '{value}%'
             */
        formatter: "{value}%"
      },
      mergedConfig: null
    }), o = computed(() => {
      if (!a.mergedConfig)
        return 0;
      const { borderWidth: N } = a.mergedConfig;
      return a.width - N;
    }), l = computed(() => {
      if (!a.mergedConfig)
        return 0;
      const { borderWidth: N } = a.mergedConfig;
      return a.height - N;
    }), s = computed(() => {
      const N = a.height / 2;
      if (!a.mergedConfig)
        return `0, ${N} 0, ${N}`;
      const { borderWidth: I, borderGap: E, value: b } = a.mergedConfig, $ = (a.width - (I + E) * 2) / 100 * b;
      return `
        ${I + E}, ${N}
        ${I + E + $}, ${N + 1e-3}
      `;
    }), D = computed(() => {
      if (!a.mergedConfig)
        return 0;
      const { borderWidth: N, borderGap: I } = a.mergedConfig;
      return a.height - (N + I) * 2;
    }), W = computed(() => {
      if (!a.mergedConfig)
        return [];
      const { colors: N } = a.mergedConfig, E = 100 / (N.length - 1);
      return N.map((b, $) => [E * $, b]);
    }), M = computed(() => a.mergedConfig && a.mergedConfig.localGradient ? a.gradientId1 : a.gradientId2), U = computed(() => {
      if (!a.mergedConfig)
        return "100%";
      const { value: N } = a.mergedConfig;
      return `${200 - N}%`;
    }), F = computed(() => {
      if (!a.mergedConfig)
        return "";
      const { value: N, formatter: I } = a.mergedConfig;
      return I.replace("{value}", N);
    });
    watch(() => t.config, () => {
      R();
    }, {
      deep: true
    }), onMounted(() => {
      A();
    });
    async function A() {
      await v(), t.config && R();
    }
    async function v() {
      await nextTick();
      const { clientWidth: N, clientHeight: I } = n.value;
      a.width = N, a.height = I;
    }
    function R() {
      a.mergedConfig = Ce($e(a.defaultConfig, true), t.config || {});
    }
    return (N, I) => (openBlock(), createElementBlock("div", {
      ref_key: "percentPond",
      ref: n,
      class: "dv-percent-pond"
    }, [
      (openBlock(), createElementBlock("svg", null, [
        createBaseVNode("defs", null, [
          createBaseVNode("linearGradient", {
            id: unref(a).gradientId1,
            x1: "0%",
            y1: "0%",
            x2: "100%",
            y2: "0%"
          }, [
            (openBlock(true), createElementBlock(Fragment, null, renderList(unref(W), (E) => (openBlock(), createElementBlock("stop", {
              key: E[0],
              offset: `${E[0]}%`,
              "stop-color": E[1]
            }, null, 8, dl))), 128))
          ], 8, fl),
          createBaseVNode("linearGradient", {
            id: unref(a).gradientId2,
            x1: "0%",
            y1: "0%",
            x2: unref(U),
            y2: "0%"
          }, [
            (openBlock(true), createElementBlock(Fragment, null, renderList(unref(W), (E) => (openBlock(), createElementBlock("stop", {
              key: E[0],
              offset: `${E[0]}%`,
              "stop-color": E[1]
            }, null, 8, vl))), 128))
          ], 8, hl)
        ]),
        createBaseVNode("rect", {
          x: unref(a).mergedConfig ? unref(a).mergedConfig.borderWidth / 2 : "0",
          y: unref(a).mergedConfig ? unref(a).mergedConfig.borderWidth / 2 : "0",
          rx: unref(a).mergedConfig ? unref(a).mergedConfig.borderRadius : "0",
          ry: unref(a).mergedConfig ? unref(a).mergedConfig.borderRadius : "0",
          fill: "transparent",
          "stroke-width": unref(a).mergedConfig ? unref(a).mergedConfig.borderWidth : "0",
          stroke: `url(#${unref(a).gradientId1})`,
          width: unref(o) > 0 ? unref(o) : 0,
          height: unref(l) > 0 ? unref(l) : 0
        }, null, 8, pl),
        createBaseVNode("polyline", {
          "stroke-width": unref(D),
          "stroke-dasharray": unref(a).mergedConfig ? unref(a).mergedConfig.lineDash.join(",") : "0",
          stroke: `url(#${unref(M)})`,
          points: unref(s)
        }, null, 8, gl),
        createBaseVNode("text", {
          stroke: unref(a).mergedConfig ? unref(a).mergedConfig.textColor : "#fff",
          fill: unref(a).mergedConfig ? unref(a).mergedConfig.textColor : "#fff",
          x: unref(a).width / 2,
          y: unref(a).height / 2
        }, toDisplayString(unref(F)), 9, ml)
      ]))
    ], 512));
  }
};
var lr = {
  install(e) {
    e.component("DvPercentPond", yl);
  }
};
function ci(e) {
  return e && e.__esModule && Object.prototype.hasOwnProperty.call(e, "default") ? e.default : e;
}
function bl(e) {
  if (e.__esModule)
    return e;
  var t = e.default;
  if (typeof t == "function") {
    var r = function n() {
      if (this instanceof n) {
        var a = [null];
        a.push.apply(a, arguments);
        var o = Function.bind.apply(t, a);
        return new o();
      }
      return t.apply(this, arguments);
    };
    r.prototype = t.prototype;
  } else
    r = {};
  return Object.defineProperty(r, "__esModule", { value: true }), Object.keys(e).forEach(function(n) {
    var a = Object.getOwnPropertyDescriptor(e, n);
    Object.defineProperty(r, n, a.get ? a : {
      enumerable: true,
      get: function() {
        return e[n];
      }
    });
  }), r;
}
var Zt = {};
var fi = { exports: {} };
(function(e) {
  function t(r) {
    return r && r.__esModule ? r : {
      default: r
    };
  }
  e.exports = t, e.exports.__esModule = true, e.exports.default = e.exports;
})(fi);
var we = fi.exports;
var sr = {};
var ur = { exports: {} };
var En;
function Ue() {
  return En || (En = 1, function(e) {
    function t(r, n, a) {
      return n in r ? Object.defineProperty(r, n, {
        value: a,
        enumerable: true,
        configurable: true,
        writable: true
      }) : r[n] = a, r;
    }
    e.exports = t, e.exports.__esModule = true, e.exports.default = e.exports;
  }(ur)), ur.exports;
}
var cr = { exports: {} };
var fr = { exports: {} };
var dr = { exports: {} };
var Wn;
function di() {
  return Wn || (Wn = 1, function(e) {
    function t(r, n) {
      (n == null || n > r.length) && (n = r.length);
      for (var a = 0, o = new Array(n); a < n; a++)
        o[a] = r[a];
      return o;
    }
    e.exports = t, e.exports.__esModule = true, e.exports.default = e.exports;
  }(dr)), dr.exports;
}
var zn;
function xl() {
  return zn || (zn = 1, function(e) {
    var t = di();
    function r(n) {
      if (Array.isArray(n))
        return t(n);
    }
    e.exports = r, e.exports.__esModule = true, e.exports.default = e.exports;
  }(fr)), fr.exports;
}
var hr = { exports: {} };
var qn;
function Cl() {
  return qn || (qn = 1, function(e) {
    function t(r) {
      if (typeof Symbol < "u" && r[Symbol.iterator] != null || r["@@iterator"] != null)
        return Array.from(r);
    }
    e.exports = t, e.exports.__esModule = true, e.exports.default = e.exports;
  }(hr)), hr.exports;
}
var vr = { exports: {} };
var In;
function hi() {
  return In || (In = 1, function(e) {
    var t = di();
    function r(n, a) {
      if (n) {
        if (typeof n == "string")
          return t(n, a);
        var o = Object.prototype.toString.call(n).slice(8, -1);
        if (o === "Object" && n.constructor && (o = n.constructor.name), o === "Map" || o === "Set")
          return Array.from(n);
        if (o === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(o))
          return t(n, a);
      }
    }
    e.exports = r, e.exports.__esModule = true, e.exports.default = e.exports;
  }(vr)), vr.exports;
}
var pr = { exports: {} };
var Hn;
function _l() {
  return Hn || (Hn = 1, function(e) {
    function t() {
      throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`);
    }
    e.exports = t, e.exports.__esModule = true, e.exports.default = e.exports;
  }(pr)), pr.exports;
}
var Vn;
function Be() {
  return Vn || (Vn = 1, function(e) {
    var t = xl(), r = Cl(), n = hi(), a = _l();
    function o(l) {
      return t(l) || r(l) || n(l) || a();
    }
    e.exports = o, e.exports.__esModule = true, e.exports.default = e.exports;
  }(cr)), cr.exports;
}
var gr = { exports: {} };
var Un;
function Lt() {
  return Un || (Un = 1, function(e) {
    function t(r, n) {
      if (!(r instanceof n))
        throw new TypeError("Cannot call a class as a function");
    }
    e.exports = t, e.exports.__esModule = true, e.exports.default = e.exports;
  }(gr)), gr.exports;
}
var St = bl(no);
var mr = {};
var yr = {};
var br = { exports: {} };
var xr = { exports: {} };
var Xn;
function $l() {
  return Xn || (Xn = 1, function(e) {
    function t(r) {
      if (Array.isArray(r))
        return r;
    }
    e.exports = t, e.exports.__esModule = true, e.exports.default = e.exports;
  }(xr)), xr.exports;
}
var Cr = { exports: {} };
var Qn;
function Pl() {
  return Qn || (Qn = 1, function(e) {
    function t(r, n) {
      var a = r == null ? null : typeof Symbol < "u" && r[Symbol.iterator] || r["@@iterator"];
      if (a != null) {
        var o = [], l = true, s = false, D, W;
        try {
          for (a = a.call(r); !(l = (D = a.next()).done) && (o.push(D.value), !(n && o.length === n)); l = true)
            ;
        } catch (M) {
          s = true, W = M;
        } finally {
          try {
            !l && a.return != null && a.return();
          } finally {
            if (s)
              throw W;
          }
        }
        return o;
      }
    }
    e.exports = t, e.exports.__esModule = true, e.exports.default = e.exports;
  }(Cr)), Cr.exports;
}
var _r = { exports: {} };
var Yn;
function wl() {
  return Yn || (Yn = 1, function(e) {
    function t() {
      throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`);
    }
    e.exports = t, e.exports.__esModule = true, e.exports.default = e.exports;
  }(_r)), _r.exports;
}
var Kn;
function Ne() {
  return Kn || (Kn = 1, function(e) {
    var t = $l(), r = Pl(), n = hi(), a = wl();
    function o(l, s) {
      return t(l) || r(l, s) || n(l, s) || a();
    }
    e.exports = o, e.exports.__esModule = true, e.exports.default = e.exports;
  }(br)), br.exports;
}
var Jn;
function kl() {
  return Jn || (Jn = 1, function(e) {
    var t = we;
    Object.defineProperty(e, "__esModule", {
      value: true
    }), e.bezierCurveToPolyline = I, e.getBezierCurveLength = E, e.default = void 0;
    var r = t(Ne()), n = t(Be()), a = Math.sqrt, o = Math.pow, l = Math.ceil, s = Math.abs, D = 50;
    function W($) {
      var f = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5, _ = $.length - 1, O = $[0], m = $[_][2], C = $.slice(1), d = C.map(function(V, X) {
        var Z = X === 0 ? O : C[X - 1][2];
        return M.apply(void 0, [Z].concat((0, n.default)(V)));
      }), G = new Array(_).fill(D), L = v(d, G), g = N(L, d, C, f);
      return g.segmentPoints.push(m), g;
    }
    function M($, f, _, O) {
      return function(m) {
        var C = 1 - m, d = o(C, 3), G = o(C, 2), L = o(m, 3), g = o(m, 2);
        return [$[0] * d + 3 * f[0] * m * G + 3 * _[0] * g * C + O[0] * L, $[1] * d + 3 * f[1] * m * G + 3 * _[1] * g * C + O[1] * L];
      };
    }
    function U($, f) {
      var _ = (0, r.default)($, 2), O = _[0], m = _[1], C = (0, r.default)(f, 2), d = C[0], G = C[1];
      return a(o(O - d, 2) + o(m - G, 2));
    }
    function F($) {
      return $.reduce(function(f, _) {
        return f + _;
      }, 0);
    }
    function A($) {
      return $.map(function(f, _) {
        return new Array(f.length - 1).fill(0).map(function(O, m) {
          return U(f[m], f[m + 1]);
        });
      });
    }
    function v($, f) {
      return $.map(function(_, O) {
        var m = 1 / f[O];
        return new Array(f[O]).fill("").map(function(C, d) {
          return _(d * m);
        });
      });
    }
    function R($, f) {
      return $.map(function(_) {
        return _.map(function(O) {
          return s(O - f);
        });
      }).map(function(_) {
        return F(_);
      }).reduce(function(_, O) {
        return _ + O;
      }, 0);
    }
    function N($, f, _, O) {
      var m = 4, C = 1, d = function() {
        var g = $.reduce(function(ee, ae) {
          return ee + ae.length;
        }, 0);
        $.forEach(function(ee, ae) {
          return ee.push(_[ae][2]);
        });
        var V = A($), X = V.reduce(function(ee, ae) {
          return ee + ae.length;
        }, 0), Z = V.map(function(ee) {
          return F(ee);
        }), c = F(Z), y = c / X, h2 = R(V, y);
        if (h2 <= O)
          return "break";
        g = l(y / O * g * 1.1);
        var P = Z.map(function(ee) {
          return l(ee / c * g);
        });
        $ = v(f, P), g = $.reduce(function(ee, ae) {
          return ee + ae.length;
        }, 0);
        var q = JSON.parse(JSON.stringify($));
        q.forEach(function(ee, ae) {
          return ee.push(_[ae][2]);
        }), V = A(q), X = V.reduce(function(ee, ae) {
          return ee + ae.length;
        }, 0), Z = V.map(function(ee) {
          return F(ee);
        }), c = F(Z), y = c / X;
        var K = 1 / g / 10;
        f.forEach(function(ee, ae) {
          for (var oe = P[ae], ve = new Array(oe).fill("").map(function(k, S) {
            return S / P[ae];
          }), Q = 0; Q < m; Q++)
            for (var ie = A([$[ae]])[0], ce = ie.map(function(k) {
              return k - y;
            }), fe = 0, j = 0; j < oe; j++) {
              if (j === 0)
                return;
              fe += ce[j - 1], ve[j] -= K * fe, ve[j] > 1 && (ve[j] = 1), ve[j] < 0 && (ve[j] = 0), $[ae][j] = ee(ve[j]);
            }
        }), m *= 4, C++;
      };
      do {
        var G = d();
        if (G === "break")
          break;
      } while (m <= 1025);
      return $ = $.reduce(function(L, g) {
        return L.concat(g);
      }, []), {
        segmentPoints: $,
        cycles: C,
        rounds: m
      };
    }
    function I($) {
      var f = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5;
      if (!$)
        return console.error("bezierCurveToPolyline: Missing parameters!"), false;
      if (!($ instanceof Array))
        return console.error("bezierCurveToPolyline: Parameter bezierCurve must be an array!"), false;
      if (typeof f != "number")
        return console.error("bezierCurveToPolyline: Parameter precision must be a number!"), false;
      var _ = W($, f), O = _.segmentPoints;
      return O;
    }
    function E($) {
      var f = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 5;
      if (!$)
        return console.error("getBezierCurveLength: Missing parameters!"), false;
      if (!($ instanceof Array))
        return console.error("getBezierCurveLength: Parameter bezierCurve must be an array!"), false;
      if (typeof f != "number")
        return console.error("getBezierCurveLength: Parameter precision must be a number!"), false;
      var _ = W($, f), O = _.segmentPoints, m = A([O])[0], C = F(m);
      return C;
    }
    var b = I;
    e.default = b;
  }(yr)), yr;
}
var $r = {};
var Zn;
function Al() {
  return Zn || (Zn = 1, function(e) {
    var t = we;
    Object.defineProperty(e, "__esModule", {
      value: true
    }), e.default = void 0;
    var r = t(Ne()), n = t(Be());
    function a(W) {
      var M = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, U = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 0.25, F = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 0.25;
      if (!(W instanceof Array))
        return console.error("polylineToBezierCurve: Parameter polyline must be an array!"), false;
      if (W.length <= 2)
        return console.error("polylineToBezierCurve: Converting to a curve requires at least 3 points!"), false;
      var A = W[0], v = W.length - 1, R = new Array(v).fill(0).map(function(N, I) {
        return [].concat((0, n.default)(o(W, I, M, U, F)), [W[I + 1]]);
      });
      return M && l(R, A), R.unshift(W[0]), R;
    }
    function o(W, M) {
      var U = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false, F = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 0.25, A = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 0.25, v = W.length;
      if (!(v < 3 || M >= v)) {
        var R = M - 1;
        R < 0 && (R = U ? v + R : 0);
        var N = M + 1;
        N >= v && (N = U ? N - v : v - 1);
        var I = M + 2;
        I >= v && (I = U ? I - v : v - 1);
        var E = W[R], b = W[M], $ = W[N], f = W[I];
        return [[b[0] + F * ($[0] - E[0]), b[1] + F * ($[1] - E[1])], [$[0] - A * (f[0] - b[0]), $[1] - A * (f[1] - b[1])]];
      }
    }
    function l(W, M) {
      var U = W[0], F = W.slice(-1)[0];
      return W.push([s(F[1], F[2]), s(U[0], M), M]), W;
    }
    function s(W, M) {
      var U = (0, r.default)(W, 2), F = U[0], A = U[1], v = (0, r.default)(M, 2), R = v[0], N = v[1], I = R - F, E = N - A;
      return [R + I, N + E];
    }
    var D = a;
    e.default = D;
  }($r)), $r;
}
var ea;
function Gn() {
  return ea || (ea = 1, function(e) {
    var t = we;
    Object.defineProperty(e, "__esModule", {
      value: true
    }), Object.defineProperty(e, "bezierCurveToPolyline", {
      enumerable: true,
      get: function() {
        return r.bezierCurveToPolyline;
      }
    }), Object.defineProperty(e, "getBezierCurveLength", {
      enumerable: true,
      get: function() {
        return r.getBezierCurveLength;
      }
    }), Object.defineProperty(e, "polylineToBezierCurve", {
      enumerable: true,
      get: function() {
        return n.default;
      }
    }), e.default = void 0;
    var r = kl(), n = t(Al()), a = {
      bezierCurveToPolyline: r.bezierCurveToPolyline,
      getBezierCurveLength: r.getBezierCurveLength,
      polylineToBezierCurve: n.default
    };
    e.default = a;
  }(mr)), mr;
}
var Pr = {};
var wr = { exports: {} };
var ta;
function ze() {
  return ta || (ta = 1, function(e) {
    function t(r) {
      return e.exports = t = typeof Symbol == "function" && typeof Symbol.iterator == "symbol" ? function(n) {
        return typeof n;
      } : function(n) {
        return n && typeof Symbol == "function" && n.constructor === Symbol && n !== Symbol.prototype ? "symbol" : typeof n;
      }, e.exports.__esModule = true, e.exports.default = e.exports, t(r);
    }
    e.exports = t, e.exports.__esModule = true, e.exports.default = e.exports;
  }(wr)), wr.exports;
}
var ra;
function Fe() {
  return ra || (ra = 1, function(e) {
    var t = we;
    Object.defineProperty(e, "__esModule", {
      value: true
    }), e.deepClone = F, e.eliminateBlur = A, e.checkPointIsInCircle = v, e.getTwoPointDistance = R, e.checkPointIsInPolygon = N, e.checkPointIsInSector = I, e.checkPointIsNearPolyline = b, e.checkPointIsInRect = $, e.getRotatePointPos = f, e.getScalePointPos = _, e.getTranslatePointPos = O, e.getDistanceBetweenPointAndLine = m, e.getCircleRadianPoint = C, e.getRegularPolygonPoints = d, e.default = void 0;
    var r = t(Be()), n = t(Ne()), a = t(ze()), o = Math.abs, l = Math.sqrt, s = Math.sin, D = Math.cos, W = Math.max, M = Math.min, U = Math.PI;
    function F(L) {
      var g = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;
      if (!L)
        return L;
      var V = JSON.parse, X = JSON.stringify;
      if (!g)
        return V(X(L));
      var Z = L instanceof Array ? [] : {};
      if (L && (0, a.default)(L) === "object")
        for (var c in L)
          L.hasOwnProperty(c) && (L[c] && (0, a.default)(L[c]) === "object" ? Z[c] = F(L[c], true) : Z[c] = L[c]);
      return Z;
    }
    function A(L) {
      return L.map(function(g) {
        var V = (0, n.default)(g, 2), X = V[0], Z = V[1];
        return [parseInt(X) + 0.5, parseInt(Z) + 0.5];
      });
    }
    function v(L, g, V, X) {
      return R(L, [g, V]) <= X;
    }
    function R(L, g) {
      var V = (0, n.default)(L, 2), X = V[0], Z = V[1], c = (0, n.default)(g, 2), y = c[0], h2 = c[1], P = o(X - y), q = o(Z - h2);
      return l(P * P + q * q);
    }
    function N(L, g) {
      for (var V = 0, X = (0, n.default)(L, 2), Z = X[0], c = X[1], y = g.length, h2 = 1, P = g[0]; h2 <= y; h2++) {
        var q = g[h2 % y];
        if (Z > M(P[0], q[0]) && Z <= W(P[0], q[0]) && c <= W(P[1], q[1]) && P[0] !== q[0]) {
          var K = (Z - P[0]) * (q[1] - P[1]) / (q[0] - P[0]) + P[1];
          (P[1] === q[1] || c <= K) && V++;
        }
        P = q;
      }
      return V % 2 === 1;
    }
    function I(L, g, V, X, Z, c, y) {
      if (!L || R(L, [g, V]) > X)
        return false;
      if (!y) {
        var h2 = F([c, Z]), P = (0, n.default)(h2, 2);
        Z = P[0], c = P[1];
      }
      var q = Z > c;
      if (q) {
        var K = [c, Z];
        Z = K[0], c = K[1];
      }
      var ee = c - Z;
      if (ee >= U * 2)
        return true;
      var ae = (0, n.default)(L, 2), oe = ae[0], ve = ae[1], Q = C(g, V, X, Z), ie = (0, n.default)(Q, 2), ce = ie[0], fe = ie[1], j = C(g, V, X, c), k = (0, n.default)(j, 2), S = k[0], B = k[1], H = [oe - g, ve - V], te = [ce - g, fe - V], p = [S - g, B - V], z = ee > U;
      if (z) {
        var u = F([p, te]), x = (0, n.default)(u, 2);
        te = x[0], p = x[1];
      }
      var w = E(te, H) && !E(p, H);
      return z && (w = !w), q && (w = !w), w;
    }
    function E(L, g) {
      var V = (0, n.default)(L, 2), X = V[0], Z = V[1], c = (0, n.default)(g, 2), y = c[0], h2 = c[1];
      return -Z * y + X * h2 > 0;
    }
    function b(L, g, V) {
      var X = V / 2, Z = g.map(function(h2) {
        var P = (0, n.default)(h2, 2), q = P[0], K = P[1];
        return [q, K - X];
      }), c = g.map(function(h2) {
        var P = (0, n.default)(h2, 2), q = P[0], K = P[1];
        return [q, K + X];
      }), y = [].concat((0, r.default)(Z), (0, r.default)(c.reverse()));
      return N(L, y);
    }
    function $(L, g, V, X, Z) {
      var c = (0, n.default)(L, 2), y = c[0], h2 = c[1];
      return !(y < g || h2 < V || y > g + X || h2 > V + Z);
    }
    function f() {
      var L = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 0, g = arguments.length > 1 ? arguments[1] : void 0, V = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [0, 0];
      if (!g)
        return false;
      if (L % 360 === 0)
        return g;
      var X = (0, n.default)(g, 2), Z = X[0], c = X[1], y = (0, n.default)(V, 2), h2 = y[0], P = y[1];
      return L *= U / 180, [(Z - h2) * D(L) - (c - P) * s(L) + h2, (Z - h2) * s(L) + (c - P) * D(L) + P];
    }
    function _() {
      var L = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : [1, 1], g = arguments.length > 1 ? arguments[1] : void 0, V = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : [0, 0];
      if (!g)
        return false;
      if (L === 1)
        return g;
      var X = (0, n.default)(g, 2), Z = X[0], c = X[1], y = (0, n.default)(V, 2), h2 = y[0], P = y[1], q = (0, n.default)(L, 2), K = q[0], ee = q[1], ae = Z - h2, oe = c - P;
      return [ae * K + h2, oe * ee + P];
    }
    function O(L, g) {
      if (!L || !g)
        return false;
      var V = (0, n.default)(g, 2), X = V[0], Z = V[1], c = (0, n.default)(L, 2), y = c[0], h2 = c[1];
      return [X + y, Z + h2];
    }
    function m(L, g, V) {
      if (!L || !g || !V)
        return false;
      var X = (0, n.default)(L, 2), Z = X[0], c = X[1], y = (0, n.default)(g, 2), h2 = y[0], P = y[1], q = (0, n.default)(V, 2), K = q[0], ee = q[1], ae = ee - P, oe = h2 - K, ve = P * (K - h2) - h2 * (ee - P), Q = o(ae * Z + oe * c + ve), ie = l(ae * ae + oe * oe);
      return Q / ie;
    }
    function C(L, g, V, X) {
      return [L + D(X) * V, g + s(X) * V];
    }
    function d(L, g, V, X) {
      var Z = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : U * -0.5, c = U * 2 / X, y = new Array(X).fill("").map(function(h2, P) {
        return P * c + Z;
      });
      return y.map(function(h2) {
        return C(L, g, V, h2);
      });
    }
    var G = {
      deepClone: F,
      eliminateBlur: A,
      checkPointIsInCircle: v,
      checkPointIsInPolygon: N,
      checkPointIsInSector: I,
      checkPointIsNearPolyline: b,
      getTwoPointDistance: R,
      getRotatePointPos: f,
      getScalePointPos: _,
      getTranslatePointPos: O,
      getCircleRadianPoint: C,
      getRegularPolygonPoints: d,
      getDistanceBetweenPointAndLine: m
    };
    e.default = G;
  }(Pr)), Pr;
}
var kr = {};
var Ar = {};
var na;
function Ll() {
  return na || (na = 1, function(e) {
    var t = we;
    Object.defineProperty(e, "__esModule", {
      value: true
    }), e.drawPolylinePath = n, e.drawBezierCurvePath = a, e.default = void 0;
    var r = t(Be());
    function n(l, s) {
      var D = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false, W = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;
      if (!l || s.length < 2)
        return false;
      D && l.beginPath(), s.forEach(function(M, U) {
        return M && (U === 0 ? l.moveTo.apply(l, (0, r.default)(M)) : l.lineTo.apply(l, (0, r.default)(M)));
      }), W && l.closePath();
    }
    function a(l, s) {
      var D = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false, W = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false, M = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : false;
      if (!l || !s)
        return false;
      W && l.beginPath(), D && l.moveTo.apply(l, (0, r.default)(D)), s.forEach(function(U) {
        return U && l.bezierCurveTo.apply(l, (0, r.default)(U[0]).concat((0, r.default)(U[1]), (0, r.default)(U[2])));
      }), M && l.closePath();
    }
    var o = {
      drawPolylinePath: n,
      drawBezierCurvePath: a
    };
    e.default = o;
  }(Ar)), Ar;
}
var aa;
function Mn() {
  return aa || (aa = 1, function(e) {
    var t = we;
    Object.defineProperty(e, "__esModule", {
      value: true
    }), e.extendNewGraph = _, e.default = e.text = e.bezierCurve = e.smoothline = e.polyline = e.regPolygon = e.sector = e.arc = e.ring = e.rect = e.ellipse = e.circle = void 0;
    var r = t(Be()), n = t(Ne()), a = t(Gn()), o = Fe(), l = Ll(), s = a.default.polylineToBezierCurve, D = a.default.bezierCurveToPolyline, W = {
      shape: {
        rx: 0,
        ry: 0,
        r: 0
      },
      validator: function(m) {
        var C = m.shape, d = C.rx, G = C.ry, L = C.r;
        return typeof d != "number" || typeof G != "number" || typeof L != "number" ? (console.error("Circle shape configuration is abnormal!"), false) : true;
      },
      draw: function(m, C) {
        var d = m.ctx, G = C.shape;
        d.beginPath();
        var L = G.rx, g = G.ry, V = G.r;
        d.arc(L, g, V > 0 ? V : 0.01, 0, Math.PI * 2), d.fill(), d.stroke(), d.closePath();
      },
      hoverCheck: function(m, C) {
        var d = C.shape, G = d.rx, L = d.ry, g = d.r;
        return (0, o.checkPointIsInCircle)(m, G, L, g);
      },
      setGraphCenter: function(m, C) {
        var d = C.shape, G = C.style, L = d.rx, g = d.ry;
        G.graphCenter = [L, g];
      },
      move: function(m, C) {
        var d = m.movementX, G = m.movementY, L = C.shape;
        this.attr("shape", {
          rx: L.rx + d,
          ry: L.ry + G
        });
      }
    };
    e.circle = W;
    var M = {
      shape: {
        rx: 0,
        ry: 0,
        hr: 0,
        vr: 0
      },
      validator: function(m) {
        var C = m.shape, d = C.rx, G = C.ry, L = C.hr, g = C.vr;
        return typeof d != "number" || typeof G != "number" || typeof L != "number" || typeof g != "number" ? (console.error("Ellipse shape configuration is abnormal!"), false) : true;
      },
      draw: function(m, C) {
        var d = m.ctx, G = C.shape;
        d.beginPath();
        var L = G.rx, g = G.ry, V = G.hr, X = G.vr;
        d.ellipse(L, g, V > 0 ? V : 0.01, X > 0 ? X : 0.01, 0, 0, Math.PI * 2), d.fill(), d.stroke(), d.closePath();
      },
      hoverCheck: function(m, C) {
        var d = C.shape, G = d.rx, L = d.ry, g = d.hr, V = d.vr, X = Math.max(g, V), Z = Math.min(g, V), c = Math.sqrt(X * X - Z * Z), y = [G - c, L], h2 = [G + c, L], P = (0, o.getTwoPointDistance)(m, y) + (0, o.getTwoPointDistance)(m, h2);
        return P <= 2 * X;
      },
      setGraphCenter: function(m, C) {
        var d = C.shape, G = C.style, L = d.rx, g = d.ry;
        G.graphCenter = [L, g];
      },
      move: function(m, C) {
        var d = m.movementX, G = m.movementY, L = C.shape;
        this.attr("shape", {
          rx: L.rx + d,
          ry: L.ry + G
        });
      }
    };
    e.ellipse = M;
    var U = {
      shape: {
        x: 0,
        y: 0,
        w: 0,
        h: 0
      },
      validator: function(m) {
        var C = m.shape, d = C.x, G = C.y, L = C.w, g = C.h;
        return typeof d != "number" || typeof G != "number" || typeof L != "number" || typeof g != "number" ? (console.error("Rect shape configuration is abnormal!"), false) : true;
      },
      draw: function(m, C) {
        var d = m.ctx, G = C.shape;
        d.beginPath();
        var L = G.x, g = G.y, V = G.w, X = G.h;
        d.rect(L, g, V, X), d.fill(), d.stroke(), d.closePath();
      },
      hoverCheck: function(m, C) {
        var d = C.shape, G = d.x, L = d.y, g = d.w, V = d.h;
        return (0, o.checkPointIsInRect)(m, G, L, g, V);
      },
      setGraphCenter: function(m, C) {
        var d = C.shape, G = C.style, L = d.x, g = d.y, V = d.w, X = d.h;
        G.graphCenter = [L + V / 2, g + X / 2];
      },
      move: function(m, C) {
        var d = m.movementX, G = m.movementY, L = C.shape;
        this.attr("shape", {
          x: L.x + d,
          y: L.y + G
        });
      }
    };
    e.rect = U;
    var F = {
      shape: {
        rx: 0,
        ry: 0,
        r: 0
      },
      validator: function(m) {
        var C = m.shape, d = C.rx, G = C.ry, L = C.r;
        return typeof d != "number" || typeof G != "number" || typeof L != "number" ? (console.error("Ring shape configuration is abnormal!"), false) : true;
      },
      draw: function(m, C) {
        var d = m.ctx, G = C.shape;
        d.beginPath();
        var L = G.rx, g = G.ry, V = G.r;
        d.arc(L, g, V > 0 ? V : 0.01, 0, Math.PI * 2), d.stroke(), d.closePath();
      },
      hoverCheck: function(m, C) {
        var d = C.shape, G = C.style, L = d.rx, g = d.ry, V = d.r, X = G.lineWidth, Z = X / 2, c = V - Z, y = V + Z, h2 = (0, o.getTwoPointDistance)(m, [L, g]);
        return h2 >= c && h2 <= y;
      },
      setGraphCenter: function(m, C) {
        var d = C.shape, G = C.style, L = d.rx, g = d.ry;
        G.graphCenter = [L, g];
      },
      move: function(m, C) {
        var d = m.movementX, G = m.movementY, L = C.shape;
        this.attr("shape", {
          rx: L.rx + d,
          ry: L.ry + G
        });
      }
    };
    e.ring = F;
    var A = {
      shape: {
        rx: 0,
        ry: 0,
        r: 0,
        startAngle: 0,
        endAngle: 0,
        clockWise: true
      },
      validator: function(m) {
        var C = m.shape, d = ["rx", "ry", "r", "startAngle", "endAngle"];
        return d.find(function(G) {
          return typeof C[G] != "number";
        }) ? (console.error("Arc shape configuration is abnormal!"), false) : true;
      },
      draw: function(m, C) {
        var d = m.ctx, G = C.shape;
        d.beginPath();
        var L = G.rx, g = G.ry, V = G.r, X = G.startAngle, Z = G.endAngle, c = G.clockWise;
        d.arc(L, g, V > 0 ? V : 1e-3, X, Z, !c), d.stroke(), d.closePath();
      },
      hoverCheck: function(m, C) {
        var d = C.shape, G = C.style, L = d.rx, g = d.ry, V = d.r, X = d.startAngle, Z = d.endAngle, c = d.clockWise, y = G.lineWidth, h2 = y / 2, P = V - h2, q = V + h2;
        return !(0, o.checkPointIsInSector)(m, L, g, P, X, Z, c) && (0, o.checkPointIsInSector)(m, L, g, q, X, Z, c);
      },
      setGraphCenter: function(m, C) {
        var d = C.shape, G = C.style, L = d.rx, g = d.ry;
        G.graphCenter = [L, g];
      },
      move: function(m, C) {
        var d = m.movementX, G = m.movementY, L = C.shape;
        this.attr("shape", {
          rx: L.rx + d,
          ry: L.ry + G
        });
      }
    };
    e.arc = A;
    var v = {
      shape: {
        rx: 0,
        ry: 0,
        r: 0,
        startAngle: 0,
        endAngle: 0,
        clockWise: true
      },
      validator: function(m) {
        var C = m.shape, d = ["rx", "ry", "r", "startAngle", "endAngle"];
        return d.find(function(G) {
          return typeof C[G] != "number";
        }) ? (console.error("Sector shape configuration is abnormal!"), false) : true;
      },
      draw: function(m, C) {
        var d = m.ctx, G = C.shape;
        d.beginPath();
        var L = G.rx, g = G.ry, V = G.r, X = G.startAngle, Z = G.endAngle, c = G.clockWise;
        d.arc(L, g, V > 0 ? V : 0.01, X, Z, !c), d.lineTo(L, g), d.closePath(), d.stroke(), d.fill();
      },
      hoverCheck: function(m, C) {
        var d = C.shape, G = d.rx, L = d.ry, g = d.r, V = d.startAngle, X = d.endAngle, Z = d.clockWise;
        return (0, o.checkPointIsInSector)(m, G, L, g, V, X, Z);
      },
      setGraphCenter: function(m, C) {
        var d = C.shape, G = C.style, L = d.rx, g = d.ry;
        G.graphCenter = [L, g];
      },
      move: function(m, C) {
        var d = m.movementX, G = m.movementY, L = C.shape, g = L.rx, V = L.ry;
        this.attr("shape", {
          rx: g + d,
          ry: V + G
        });
      }
    };
    e.sector = v;
    var R = {
      shape: {
        rx: 0,
        ry: 0,
        r: 0,
        side: 0
      },
      validator: function(m) {
        var C = m.shape, d = C.side, G = ["rx", "ry", "r", "side"];
        return G.find(function(L) {
          return typeof C[L] != "number";
        }) ? (console.error("RegPolygon shape configuration is abnormal!"), false) : d < 3 ? (console.error("RegPolygon at least trigon!"), false) : true;
      },
      draw: function(m, C) {
        var d = m.ctx, G = C.shape, L = C.cache;
        d.beginPath();
        var g = G.rx, V = G.ry, X = G.r, Z = G.side;
        if (!L.points || L.rx !== g || L.ry !== V || L.r !== X || L.side !== Z) {
          var c = (0, o.getRegularPolygonPoints)(g, V, X, Z);
          Object.assign(L, {
            points: c,
            rx: g,
            ry: V,
            r: X,
            side: Z
          });
        }
        var y = L.points;
        (0, l.drawPolylinePath)(d, y), d.closePath(), d.stroke(), d.fill();
      },
      hoverCheck: function(m, C) {
        var d = C.cache, G = d.points;
        return (0, o.checkPointIsInPolygon)(m, G);
      },
      setGraphCenter: function(m, C) {
        var d = C.shape, G = C.style, L = d.rx, g = d.ry;
        G.graphCenter = [L, g];
      },
      move: function(m, C) {
        var d = m.movementX, G = m.movementY, L = C.shape, g = C.cache, V = L.rx, X = L.ry;
        g.rx += d, g.ry += G, this.attr("shape", {
          rx: V + d,
          ry: X + G
        }), g.points = g.points.map(function(Z) {
          var c = (0, n.default)(Z, 2), y = c[0], h2 = c[1];
          return [y + d, h2 + G];
        });
      }
    };
    e.regPolygon = R;
    var N = {
      shape: {
        points: [],
        close: false
      },
      validator: function(m) {
        var C = m.shape, d = C.points;
        return d instanceof Array ? true : (console.error("Polyline points should be an array!"), false);
      },
      draw: function(m, C) {
        var d = m.ctx, G = C.shape, L = C.style.lineWidth;
        d.beginPath();
        var g = G.points, V = G.close;
        L === 1 && (g = (0, o.eliminateBlur)(g)), (0, l.drawPolylinePath)(d, g), V && (d.closePath(), d.fill()), d.stroke();
      },
      hoverCheck: function(m, C) {
        var d = C.shape, G = C.style, L = d.points, g = d.close, V = G.lineWidth;
        return g ? (0, o.checkPointIsInPolygon)(m, L) : (0, o.checkPointIsNearPolyline)(m, L, V);
      },
      setGraphCenter: function(m, C) {
        var d = C.shape, G = C.style, L = d.points;
        G.graphCenter = L[0];
      },
      move: function(m, C) {
        var d = m.movementX, G = m.movementY, L = C.shape, g = L.points, V = g.map(function(X) {
          var Z = (0, n.default)(X, 2), c = Z[0], y = Z[1];
          return [c + d, y + G];
        });
        this.attr("shape", {
          points: V
        });
      }
    };
    e.polyline = N;
    var I = {
      shape: {
        points: [],
        close: false
      },
      validator: function(m) {
        var C = m.shape, d = C.points;
        return d instanceof Array ? true : (console.error("Smoothline points should be an array!"), false);
      },
      draw: function(m, C) {
        var d = m.ctx, G = C.shape, L = C.cache, g = G.points, V = G.close;
        if (!L.points || L.points.toString() !== g.toString()) {
          var X = s(g, V), Z = D(X);
          Object.assign(L, {
            points: (0, o.deepClone)(g, true),
            bezierCurve: X,
            hoverPoints: Z
          });
        }
        var c = L.bezierCurve;
        d.beginPath(), (0, l.drawBezierCurvePath)(d, c.slice(1), c[0]), V && (d.closePath(), d.fill()), d.stroke();
      },
      hoverCheck: function(m, C) {
        var d = C.cache, G = C.shape, L = C.style, g = d.hoverPoints, V = G.close, X = L.lineWidth;
        return V ? (0, o.checkPointIsInPolygon)(m, g) : (0, o.checkPointIsNearPolyline)(m, g, X);
      },
      setGraphCenter: function(m, C) {
        var d = C.shape, G = C.style, L = d.points;
        G.graphCenter = L[0];
      },
      move: function(m, C) {
        var d = m.movementX, G = m.movementY, L = C.shape, g = C.cache, V = L.points, X = V.map(function(P) {
          var q = (0, n.default)(P, 2), K = q[0], ee = q[1];
          return [K + d, ee + G];
        });
        g.points = X;
        var Z = (0, n.default)(g.bezierCurve[0], 2), c = Z[0], y = Z[1], h2 = g.bezierCurve.slice(1);
        g.bezierCurve = [[c + d, y + G]].concat((0, r.default)(h2.map(function(P) {
          return P.map(function(q) {
            var K = (0, n.default)(q, 2), ee = K[0], ae = K[1];
            return [ee + d, ae + G];
          });
        }))), g.hoverPoints = g.hoverPoints.map(function(P) {
          var q = (0, n.default)(P, 2), K = q[0], ee = q[1];
          return [K + d, ee + G];
        }), this.attr("shape", {
          points: X
        });
      }
    };
    e.smoothline = I;
    var E = {
      shape: {
        points: [],
        close: false
      },
      validator: function(m) {
        var C = m.shape, d = C.points;
        return d instanceof Array ? true : (console.error("BezierCurve points should be an array!"), false);
      },
      draw: function(m, C) {
        var d = m.ctx, G = C.shape, L = C.cache, g = G.points, V = G.close;
        if (!L.points || L.points.toString() !== g.toString()) {
          var X = D(g, 20);
          Object.assign(L, {
            points: (0, o.deepClone)(g, true),
            hoverPoints: X
          });
        }
        d.beginPath(), (0, l.drawBezierCurvePath)(d, g.slice(1), g[0]), V && (d.closePath(), d.fill()), d.stroke();
      },
      hoverCheck: function(m, C) {
        var d = C.cache, G = C.shape, L = C.style, g = d.hoverPoints, V = G.close, X = L.lineWidth;
        return V ? (0, o.checkPointIsInPolygon)(m, g) : (0, o.checkPointIsNearPolyline)(m, g, X);
      },
      setGraphCenter: function(m, C) {
        var d = C.shape, G = C.style, L = d.points;
        G.graphCenter = L[0];
      },
      move: function(m, C) {
        var d = m.movementX, G = m.movementY, L = C.shape, g = C.cache, V = L.points, X = (0, n.default)(V[0], 2), Z = X[0], c = X[1], y = V.slice(1), h2 = [[Z + d, c + G]].concat((0, r.default)(y.map(function(P) {
          return P.map(function(q) {
            var K = (0, n.default)(q, 2), ee = K[0], ae = K[1];
            return [ee + d, ae + G];
          });
        })));
        g.points = h2, g.hoverPoints = g.hoverPoints.map(function(P) {
          var q = (0, n.default)(P, 2), K = q[0], ee = q[1];
          return [K + d, ee + G];
        }), this.attr("shape", {
          points: h2
        });
      }
    };
    e.bezierCurve = E;
    var b = {
      shape: {
        content: "",
        position: [],
        maxWidth: void 0,
        rowGap: 0
      },
      validator: function(m) {
        var C = m.shape, d = C.content, G = C.position, L = C.rowGap;
        return typeof d != "string" ? (console.error("Text content should be a string!"), false) : G instanceof Array ? typeof L != "number" ? (console.error("Text rowGap should be a number!"), false) : true : (console.error("Text position should be an array!"), false);
      },
      draw: function(m, C) {
        var d = m.ctx, G = C.shape, L = G.content, g = G.position, V = G.maxWidth, X = G.rowGap, Z = d.textBaseline, c = d.font, y = parseInt(c.replace(/\D/g, "")), h2 = g, P = (0, n.default)(h2, 2), q = P[0], K = P[1];
        L = L.split(`
`);
        var ee = L.length, ae = y + X, oe = ee * ae - X, ve = 0;
        Z === "middle" && (ve = oe / 2, K += y / 2), Z === "bottom" && (ve = oe, K += y), g = new Array(ee).fill(0).map(function(Q, ie) {
          return [q, K + ie * ae - ve];
        }), d.beginPath(), L.forEach(function(Q, ie) {
          d.fillText.apply(d, [Q].concat((0, r.default)(g[ie]), [V])), d.strokeText.apply(d, [Q].concat((0, r.default)(g[ie]), [V]));
        }), d.closePath();
      },
      hoverCheck: function(m, C) {
        return C.shape, C.style, false;
      },
      setGraphCenter: function(m, C) {
        var d = C.shape, G = C.style, L = d.position;
        G.graphCenter = (0, r.default)(L);
      },
      move: function(m, C) {
        var d = m.movementX, G = m.movementY, L = C.shape, g = (0, n.default)(L.position, 2), V = g[0], X = g[1];
        this.attr("shape", {
          position: [V + d, X + G]
        });
      }
    };
    e.text = b;
    var $ = /* @__PURE__ */ new Map([["circle", W], ["ellipse", M], ["rect", U], ["ring", F], ["arc", A], ["sector", v], ["regPolygon", R], ["polyline", N], ["smoothline", I], ["bezierCurve", E], ["text", b]]), f = $;
    e.default = f;
    function _(O, m) {
      if (!O || !m) {
        console.error("ExtendNewGraph Missing Parameters!");
        return;
      }
      if (!m.shape) {
        console.error("Required attribute of shape to extendNewGraph!");
        return;
      }
      if (!m.validator) {
        console.error("Required function of validator to extendNewGraph!");
        return;
      }
      if (!m.draw) {
        console.error("Required function of draw to extendNewGraph!");
        return;
      }
      $.set(O, m);
    }
  }(kr)), kr;
}
var Lr = {};
var Sr = { exports: {} };
var ia;
function Sl() {
  return ia || (ia = 1, function(e) {
    var t = function(r) {
      var n = Object.prototype, a = n.hasOwnProperty, o, l = typeof Symbol == "function" ? Symbol : {}, s = l.iterator || "@@iterator", D = l.asyncIterator || "@@asyncIterator", W = l.toStringTag || "@@toStringTag";
      function M(y, h2, P) {
        return Object.defineProperty(y, h2, {
          value: P,
          enumerable: true,
          configurable: true,
          writable: true
        }), y[h2];
      }
      try {
        M({}, "");
      } catch {
        M = function(h2, P, q) {
          return h2[P] = q;
        };
      }
      function U(y, h2, P, q) {
        var K = h2 && h2.prototype instanceof E ? h2 : E, ee = Object.create(K.prototype), ae = new X(q || []);
        return ee._invoke = G(y, P, ae), ee;
      }
      r.wrap = U;
      function F(y, h2, P) {
        try {
          return { type: "normal", arg: y.call(h2, P) };
        } catch (q) {
          return { type: "throw", arg: q };
        }
      }
      var A = "suspendedStart", v = "suspendedYield", R = "executing", N = "completed", I = {};
      function E() {
      }
      function b() {
      }
      function $() {
      }
      var f = {};
      M(f, s, function() {
        return this;
      });
      var _ = Object.getPrototypeOf, O = _ && _(_(Z([])));
      O && O !== n && a.call(O, s) && (f = O);
      var m = $.prototype = E.prototype = Object.create(f);
      b.prototype = $, M(m, "constructor", $), M($, "constructor", b), b.displayName = M(
        $,
        W,
        "GeneratorFunction"
      );
      function C(y) {
        ["next", "throw", "return"].forEach(function(h2) {
          M(y, h2, function(P) {
            return this._invoke(h2, P);
          });
        });
      }
      r.isGeneratorFunction = function(y) {
        var h2 = typeof y == "function" && y.constructor;
        return h2 ? h2 === b || // For the native GeneratorFunction constructor, the best we can
        // do is to check its .name property.
        (h2.displayName || h2.name) === "GeneratorFunction" : false;
      }, r.mark = function(y) {
        return Object.setPrototypeOf ? Object.setPrototypeOf(y, $) : (y.__proto__ = $, M(y, W, "GeneratorFunction")), y.prototype = Object.create(m), y;
      }, r.awrap = function(y) {
        return { __await: y };
      };
      function d(y, h2) {
        function P(ee, ae, oe, ve) {
          var Q = F(y[ee], y, ae);
          if (Q.type === "throw")
            ve(Q.arg);
          else {
            var ie = Q.arg, ce = ie.value;
            return ce && typeof ce == "object" && a.call(ce, "__await") ? h2.resolve(ce.__await).then(function(fe) {
              P("next", fe, oe, ve);
            }, function(fe) {
              P("throw", fe, oe, ve);
            }) : h2.resolve(ce).then(function(fe) {
              ie.value = fe, oe(ie);
            }, function(fe) {
              return P("throw", fe, oe, ve);
            });
          }
        }
        var q;
        function K(ee, ae) {
          function oe() {
            return new h2(function(ve, Q) {
              P(ee, ae, ve, Q);
            });
          }
          return q = // If enqueue has been called before, then we want to wait until
          // all previous Promises have been resolved before calling invoke,
          // so that results are always delivered in the correct order. If
          // enqueue has not been called before, then it is important to
          // call invoke immediately, without waiting on a callback to fire,
          // so that the async generator function has the opportunity to do
          // any necessary setup in a predictable way. This predictability
          // is why the Promise constructor synchronously invokes its
          // executor callback, and why async functions synchronously
          // execute code before the first await. Since we implement simple
          // async functions in terms of async generators, it is especially
          // important to get this right, even though it requires care.
          q ? q.then(
            oe,
            // Avoid propagating failures to Promises returned by later
            // invocations of the iterator.
            oe
          ) : oe();
        }
        this._invoke = K;
      }
      C(d.prototype), M(d.prototype, D, function() {
        return this;
      }), r.AsyncIterator = d, r.async = function(y, h2, P, q, K) {
        K === void 0 && (K = Promise);
        var ee = new d(
          U(y, h2, P, q),
          K
        );
        return r.isGeneratorFunction(h2) ? ee : ee.next().then(function(ae) {
          return ae.done ? ae.value : ee.next();
        });
      };
      function G(y, h2, P) {
        var q = A;
        return function(ee, ae) {
          if (q === R)
            throw new Error("Generator is already running");
          if (q === N) {
            if (ee === "throw")
              throw ae;
            return c();
          }
          for (P.method = ee, P.arg = ae; ; ) {
            var oe = P.delegate;
            if (oe) {
              var ve = L(oe, P);
              if (ve) {
                if (ve === I)
                  continue;
                return ve;
              }
            }
            if (P.method === "next")
              P.sent = P._sent = P.arg;
            else if (P.method === "throw") {
              if (q === A)
                throw q = N, P.arg;
              P.dispatchException(P.arg);
            } else
              P.method === "return" && P.abrupt("return", P.arg);
            q = R;
            var Q = F(y, h2, P);
            if (Q.type === "normal") {
              if (q = P.done ? N : v, Q.arg === I)
                continue;
              return {
                value: Q.arg,
                done: P.done
              };
            } else
              Q.type === "throw" && (q = N, P.method = "throw", P.arg = Q.arg);
          }
        };
      }
      function L(y, h2) {
        var P = y.iterator[h2.method];
        if (P === o) {
          if (h2.delegate = null, h2.method === "throw") {
            if (y.iterator.return && (h2.method = "return", h2.arg = o, L(y, h2), h2.method === "throw"))
              return I;
            h2.method = "throw", h2.arg = new TypeError(
              "The iterator does not provide a 'throw' method"
            );
          }
          return I;
        }
        var q = F(P, y.iterator, h2.arg);
        if (q.type === "throw")
          return h2.method = "throw", h2.arg = q.arg, h2.delegate = null, I;
        var K = q.arg;
        if (!K)
          return h2.method = "throw", h2.arg = new TypeError("iterator result is not an object"), h2.delegate = null, I;
        if (K.done)
          h2[y.resultName] = K.value, h2.next = y.nextLoc, h2.method !== "return" && (h2.method = "next", h2.arg = o);
        else
          return K;
        return h2.delegate = null, I;
      }
      C(m), M(m, W, "Generator"), M(m, s, function() {
        return this;
      }), M(m, "toString", function() {
        return "[object Generator]";
      });
      function g(y) {
        var h2 = { tryLoc: y[0] };
        1 in y && (h2.catchLoc = y[1]), 2 in y && (h2.finallyLoc = y[2], h2.afterLoc = y[3]), this.tryEntries.push(h2);
      }
      function V(y) {
        var h2 = y.completion || {};
        h2.type = "normal", delete h2.arg, y.completion = h2;
      }
      function X(y) {
        this.tryEntries = [{ tryLoc: "root" }], y.forEach(g, this), this.reset(true);
      }
      r.keys = function(y) {
        var h2 = [];
        for (var P in y)
          h2.push(P);
        return h2.reverse(), function q() {
          for (; h2.length; ) {
            var K = h2.pop();
            if (K in y)
              return q.value = K, q.done = false, q;
          }
          return q.done = true, q;
        };
      };
      function Z(y) {
        if (y) {
          var h2 = y[s];
          if (h2)
            return h2.call(y);
          if (typeof y.next == "function")
            return y;
          if (!isNaN(y.length)) {
            var P = -1, q = function K() {
              for (; ++P < y.length; )
                if (a.call(y, P))
                  return K.value = y[P], K.done = false, K;
              return K.value = o, K.done = true, K;
            };
            return q.next = q;
          }
        }
        return { next: c };
      }
      r.values = Z;
      function c() {
        return { value: o, done: true };
      }
      return X.prototype = {
        constructor: X,
        reset: function(y) {
          if (this.prev = 0, this.next = 0, this.sent = this._sent = o, this.done = false, this.delegate = null, this.method = "next", this.arg = o, this.tryEntries.forEach(V), !y)
            for (var h2 in this)
              h2.charAt(0) === "t" && a.call(this, h2) && !isNaN(+h2.slice(1)) && (this[h2] = o);
        },
        stop: function() {
          this.done = true;
          var y = this.tryEntries[0], h2 = y.completion;
          if (h2.type === "throw")
            throw h2.arg;
          return this.rval;
        },
        dispatchException: function(y) {
          if (this.done)
            throw y;
          var h2 = this;
          function P(ve, Q) {
            return ee.type = "throw", ee.arg = y, h2.next = ve, Q && (h2.method = "next", h2.arg = o), !!Q;
          }
          for (var q = this.tryEntries.length - 1; q >= 0; --q) {
            var K = this.tryEntries[q], ee = K.completion;
            if (K.tryLoc === "root")
              return P("end");
            if (K.tryLoc <= this.prev) {
              var ae = a.call(K, "catchLoc"), oe = a.call(K, "finallyLoc");
              if (ae && oe) {
                if (this.prev < K.catchLoc)
                  return P(K.catchLoc, true);
                if (this.prev < K.finallyLoc)
                  return P(K.finallyLoc);
              } else if (ae) {
                if (this.prev < K.catchLoc)
                  return P(K.catchLoc, true);
              } else if (oe) {
                if (this.prev < K.finallyLoc)
                  return P(K.finallyLoc);
              } else
                throw new Error("try statement without catch or finally");
            }
          }
        },
        abrupt: function(y, h2) {
          for (var P = this.tryEntries.length - 1; P >= 0; --P) {
            var q = this.tryEntries[P];
            if (q.tryLoc <= this.prev && a.call(q, "finallyLoc") && this.prev < q.finallyLoc) {
              var K = q;
              break;
            }
          }
          K && (y === "break" || y === "continue") && K.tryLoc <= h2 && h2 <= K.finallyLoc && (K = null);
          var ee = K ? K.completion : {};
          return ee.type = y, ee.arg = h2, K ? (this.method = "next", this.next = K.finallyLoc, I) : this.complete(ee);
        },
        complete: function(y, h2) {
          if (y.type === "throw")
            throw y.arg;
          return y.type === "break" || y.type === "continue" ? this.next = y.arg : y.type === "return" ? (this.rval = this.arg = y.arg, this.method = "return", this.next = "end") : y.type === "normal" && h2 && (this.next = h2), I;
        },
        finish: function(y) {
          for (var h2 = this.tryEntries.length - 1; h2 >= 0; --h2) {
            var P = this.tryEntries[h2];
            if (P.finallyLoc === y)
              return this.complete(P.completion, P.afterLoc), V(P), I;
          }
        },
        catch: function(y) {
          for (var h2 = this.tryEntries.length - 1; h2 >= 0; --h2) {
            var P = this.tryEntries[h2];
            if (P.tryLoc === y) {
              var q = P.completion;
              if (q.type === "throw") {
                var K = q.arg;
                V(P);
              }
              return K;
            }
          }
          throw new Error("illegal catch attempt");
        },
        delegateYield: function(y, h2, P) {
          return this.delegate = {
            iterator: Z(y),
            resultName: h2,
            nextLoc: P
          }, this.method === "next" && (this.arg = o), I;
        }
      }, r;
    }(
      // If this script is executing as a CommonJS module, use module.exports
      // as the regeneratorRuntime namespace. Otherwise create a new empty
      // object. Either way, the resulting object will be used to initialize
      // the regeneratorRuntime variable at the top of this file.
      e.exports
    );
    try {
      regeneratorRuntime = t;
    } catch {
      typeof globalThis == "object" ? globalThis.regeneratorRuntime = t : Function("r", "regeneratorRuntime = r")(t);
    }
  }(Sr)), Sr.exports;
}
var Or;
var oa;
function Ol() {
  return oa || (oa = 1, Or = Sl()), Or;
}
var Gr = { exports: {} };
var la;
function Gl() {
  return la || (la = 1, function(e) {
    function t(n, a, o, l, s, D, W) {
      try {
        var M = n[D](W), U = M.value;
      } catch (F) {
        o(F);
        return;
      }
      M.done ? a(U) : Promise.resolve(U).then(l, s);
    }
    function r(n) {
      return function() {
        var a = this, o = arguments;
        return new Promise(function(l, s) {
          var D = n.apply(a, o);
          function W(U) {
            t(D, l, s, W, M, "next", U);
          }
          function M(U) {
            t(D, l, s, W, M, "throw", U);
          }
          W(void 0);
        });
      };
    }
    e.exports = r, e.exports.__esModule = true, e.exports.default = e.exports;
  }(Gr)), Gr.exports;
}
var Mr = {};
var sa;
function Ml() {
  return sa || (sa = 1, function(e) {
    var t = we;
    Object.defineProperty(e, "__esModule", {
      value: true
    }), e.default = void 0;
    var r = t(Be()), n = t(Lt()), a = St, o = Fe(), l = function A(v) {
      (0, n.default)(this, A), this.colorProcessor(v);
      var R = {
        /**
         * @description Rgba value of graph fill color
         * @type {Array}
         * @default fill = [0, 0, 0, 1]
         */
        fill: [0, 0, 0, 1],
        /**
         * @description Rgba value of graph stroke color
         * @type {Array}
         * @default stroke = [0, 0, 0, 1]
         */
        stroke: [0, 0, 0, 0],
        /**
         * @description Opacity of graph
         * @type {Number}
         * @default opacity = 1
         */
        opacity: 1,
        /**
         * @description LineCap of Ctx
         * @type {String}
         * @default lineCap = null
         * @example lineCap = 'butt'|'round'|'square'
         */
        lineCap: null,
        /**
         * @description Linejoin of Ctx
         * @type {String}
         * @default lineJoin = null
         * @example lineJoin = 'round'|'bevel'|'miter'
         */
        lineJoin: null,
        /**
         * @description LineDash of Ctx
         * @type {Array}
         * @default lineDash = null
         * @example lineDash = [10, 10]
         */
        lineDash: null,
        /**
         * @description LineDashOffset of Ctx
         * @type {Number}
         * @default lineDashOffset = null
         * @example lineDashOffset = 10
         */
        lineDashOffset: null,
        /**
         * @description ShadowBlur of Ctx
         * @type {Number}
         * @default shadowBlur = 0
         */
        shadowBlur: 0,
        /**
         * @description Rgba value of graph shadow color
         * @type {Array}
         * @default shadowColor = [0, 0, 0, 0]
         */
        shadowColor: [0, 0, 0, 0],
        /**
         * @description ShadowOffsetX of Ctx
         * @type {Number}
         * @default shadowOffsetX = 0
         */
        shadowOffsetX: 0,
        /**
         * @description ShadowOffsetY of Ctx
         * @type {Number}
         * @default shadowOffsetY = 0
         */
        shadowOffsetY: 0,
        /**
         * @description LineWidth of Ctx
         * @type {Number}
         * @default lineWidth = 0
         */
        lineWidth: 0,
        /**
         * @description Center point of the graph
         * @type {Array}
         * @default graphCenter = null
         * @example graphCenter = [10, 10]
         */
        graphCenter: null,
        /**
         * @description Graph scale
         * @type {Array}
         * @default scale = null
         * @example scale = [1.5, 1.5]
         */
        scale: null,
        /**
         * @description Graph rotation degree
         * @type {Number}
         * @default rotate = null
         * @example rotate = 10
         */
        rotate: null,
        /**
         * @description Graph translate distance
         * @type {Array}
         * @default translate = null
         * @example translate = [10, 10]
         */
        translate: null,
        /**
         * @description Cursor status when hover
         * @type {String}
         * @default hoverCursor = 'pointer'
         * @example hoverCursor = 'default'|'pointer'|'auto'|'crosshair'|'move'|'wait'|...
         */
        hoverCursor: "pointer",
        /**
         * @description Font style of Ctx
         * @type {String}
         * @default fontStyle = 'normal'
         * @example fontStyle = 'normal'|'italic'|'oblique'
         */
        fontStyle: "normal",
        /**
         * @description Font varient of Ctx
         * @type {String}
         * @default fontVarient = 'normal'
         * @example fontVarient = 'normal'|'small-caps'
         */
        fontVarient: "normal",
        /**
         * @description Font weight of Ctx
         * @type {String|Number}
         * @default fontWeight = 'normal'
         * @example fontWeight = 'normal'|'bold'|'bolder'|'lighter'|Number
         */
        fontWeight: "normal",
        /**
         * @description Font size of Ctx
         * @type {Number}
         * @default fontSize = 10
         */
        fontSize: 10,
        /**
         * @description Font family of Ctx
         * @type {String}
         * @default fontFamily = 'Arial'
         */
        fontFamily: "Arial",
        /**
         * @description TextAlign of Ctx
         * @type {String}
         * @default textAlign = 'center'
         * @example textAlign = 'start'|'end'|'left'|'right'|'center'
         */
        textAlign: "center",
        /**
         * @description TextBaseline of Ctx
         * @type {String}
         * @default textBaseline = 'middle'
         * @example textBaseline = 'top'|'bottom'|'middle'|'alphabetic'|'hanging'
         */
        textBaseline: "middle",
        /**
         * @description The color used to create the gradient
         * @type {Array}
         * @default gradientColor = null
         * @example gradientColor = ['#000', '#111', '#222']
         */
        gradientColor: null,
        /**
         * @description Gradient type
         * @type {String}
         * @default gradientType = 'linear'
         * @example gradientType = 'linear' | 'radial'
         */
        gradientType: "linear",
        /**
         * @description Gradient params
         * @type {Array}
         * @default gradientParams = null
         * @example gradientParams = [x0, y0, x1, y1] (Linear Gradient)
         * @example gradientParams = [x0, y0, r0, x1, y1, r1] (Radial Gradient)
         */
        gradientParams: null,
        /**
         * @description When to use gradients
         * @type {String}
         * @default gradientWith = 'stroke'
         * @example gradientWith = 'stroke' | 'fill'
         */
        gradientWith: "stroke",
        /**
         * @description Gradient color stops
         * @type {String}
         * @default gradientStops = 'auto'
         * @example gradientStops = 'auto' | [0, .2, .3, 1]
         */
        gradientStops: "auto",
        /**
         * @description Extended color that supports animation transition
         * @type {Array|Object}
         * @default colors = null
         * @example colors = ['#000', '#111', '#222', 'red' ]
         * @example colors = { a: '#000', b: '#111' }
         */
        colors: null
      };
      Object.assign(this, R, v);
    };
    e.default = l, l.prototype.colorProcessor = function(A) {
      var v = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, R = v ? a.getColorFromRgbValue : a.getRgbaValue, N = ["fill", "stroke", "shadowColor"], I = Object.keys(A), E = I.filter(function(_) {
        return N.find(function(O) {
          return O === _;
        });
      });
      E.forEach(function(_) {
        return A[_] = R(A[_]);
      });
      var b = A.gradientColor, $ = A.colors;
      if (b && (A.gradientColor = b.map(function(_) {
        return R(_);
      })), $) {
        var f = Object.keys($);
        f.forEach(function(_) {
          return $[_] = R($[_]);
        });
      }
    }, l.prototype.initStyle = function(A) {
      s(A, this), W(A, this), M(A, this);
    };
    function s(A, v) {
      A.save();
      var R = v.graphCenter, N = v.rotate, I = v.scale, E = v.translate;
      R instanceof Array && (A.translate.apply(A, (0, r.default)(R)), N && A.rotate(N * Math.PI / 180), I instanceof Array && A.scale.apply(A, (0, r.default)(I)), E && A.translate.apply(A, (0, r.default)(E)), A.translate(-R[0], -R[1]));
    }
    var D = ["lineCap", "lineJoin", "lineDashOffset", "shadowOffsetX", "shadowOffsetY", "lineWidth", "textAlign", "textBaseline"];
    function W(A, v) {
      var R = v.fill, N = v.stroke, I = v.shadowColor, E = v.opacity;
      D.forEach(function(d) {
        (d || typeof d == "number") && (A[d] = v[d]);
      }), R = (0, r.default)(R), N = (0, r.default)(N), I = (0, r.default)(I), R[3] *= E, N[3] *= E, I[3] *= E, A.fillStyle = (0, a.getColorFromRgbValue)(R), A.strokeStyle = (0, a.getColorFromRgbValue)(N), A.shadowColor = (0, a.getColorFromRgbValue)(I);
      var b = v.lineDash, $ = v.shadowBlur;
      b && (b = b.map(function(d) {
        return d >= 0 ? d : 0;
      }), A.setLineDash(b)), typeof $ == "number" && (A.shadowBlur = $ > 0 ? $ : 1e-3);
      var f = v.fontStyle, _ = v.fontVarient, O = v.fontWeight, m = v.fontSize, C = v.fontFamily;
      A.font = f + " " + _ + " " + O + " " + m + "px " + C;
    }
    function M(A, v) {
      if (U(v)) {
        var R = v.gradientColor, N = v.gradientParams, I = v.gradientType, E = v.gradientWith, b = v.gradientStops, $ = v.opacity;
        R = R.map(function(_) {
          var O = _[3] * $, m = (0, r.default)(_);
          return m[3] = O, m;
        }), R = R.map(function(_) {
          return (0, a.getColorFromRgbValue)(_);
        }), b === "auto" && (b = F(R));
        var f = A["create".concat(I.slice(0, 1).toUpperCase() + I.slice(1), "Gradient")].apply(A, (0, r.default)(N));
        b.forEach(function(_, O) {
          return f.addColorStop(_, R[O]);
        }), A["".concat(E, "Style")] = f;
      }
    }
    function U(A) {
      var v = A.gradientColor, R = A.gradientParams, N = A.gradientType, I = A.gradientWith, E = A.gradientStops;
      if (!v || !R)
        return false;
      if (v.length === 1)
        return console.warn("The gradient needs to provide at least two colors"), false;
      if (N !== "linear" && N !== "radial")
        return console.warn("GradientType only supports linear or radial, current value is " + N), false;
      var b = R.length;
      return N === "linear" && b !== 4 || N === "radial" && b !== 6 ? (console.warn("The expected length of gradientParams is " + (N === "linear" ? "4" : "6")), false) : I !== "fill" && I !== "stroke" ? (console.warn("GradientWith only supports fill or stroke, current value is " + I), false) : E !== "auto" && !(E instanceof Array) ? (console.warn("gradientStops only supports 'auto' or Number Array ([0, .5, 1]), current value is " + E), false) : true;
    }
    function F(A) {
      var v = 1 / (A.length - 1);
      return A.map(function(R, N) {
        return v * N;
      });
    }
    l.prototype.restoreTransform = function(A) {
      A.restore();
    }, l.prototype.update = function(A) {
      this.colorProcessor(A), Object.assign(this, A);
    }, l.prototype.getStyle = function() {
      var A = (0, o.deepClone)(this, true);
      return this.colorProcessor(A, true), A;
    };
  }(Mr)), Mr;
}
var Rr = {};
var Tr = {};
var ua;
function Rl() {
  return ua || (ua = 1, function(e) {
    Object.defineProperty(e, "__esModule", {
      value: true
    }), e.default = e.easeInOutBounce = e.easeOutBounce = e.easeInBounce = e.easeInOutElastic = e.easeOutElastic = e.easeInElastic = e.easeInOutBack = e.easeOutBack = e.easeInBack = e.easeInOutQuint = e.easeOutQuint = e.easeInQuint = e.easeInOutQuart = e.easeOutQuart = e.easeInQuart = e.easeInOutCubic = e.easeOutCubic = e.easeInCubic = e.easeInOutQuad = e.easeOutQuad = e.easeInQuad = e.easeInOutSine = e.easeOutSine = e.easeInSine = e.linear = void 0;
    var t = [[[0, 1], "", [0.33, 0.67]], [[1, 0], [0.67, 0.33]]];
    e.linear = t;
    var r = [[[0, 1]], [[0.538, 0.564], [0.169, 0.912], [0.88, 0.196]], [[1, 0]]];
    e.easeInSine = r;
    var n = [[[0, 1]], [[0.444, 0.448], [0.169, 0.736], [0.718, 0.16]], [[1, 0]]];
    e.easeOutSine = n;
    var a = [[[0, 1]], [[0.5, 0.5], [0.2, 1], [0.8, 0]], [[1, 0]]];
    e.easeInOutSine = a;
    var o = [[[0, 1]], [[0.55, 0.584], [0.231, 0.904], [0.868, 0.264]], [[1, 0]]];
    e.easeInQuad = o;
    var l = [[[0, 1]], [[0.413, 0.428], [0.065, 0.816], [0.76, 0.04]], [[1, 0]]];
    e.easeOutQuad = l;
    var s = [[[0, 1]], [[0.5, 0.5], [0.3, 0.9], [0.7, 0.1]], [[1, 0]]];
    e.easeInOutQuad = s;
    var D = [[[0, 1]], [[0.679, 0.688], [0.366, 0.992], [0.992, 0.384]], [[1, 0]]];
    e.easeInCubic = D;
    var W = [[[0, 1]], [[0.321, 0.312], [8e-3, 0.616], [0.634, 8e-3]], [[1, 0]]];
    e.easeOutCubic = W;
    var M = [[[0, 1]], [[0.5, 0.5], [0.3, 1], [0.7, 0]], [[1, 0]]];
    e.easeInOutCubic = M;
    var U = [[[0, 1]], [[0.812, 0.74], [0.611, 0.988], [1.013, 0.492]], [[1, 0]]];
    e.easeInQuart = U;
    var F = [[[0, 1]], [[0.152, 0.244], [1e-3, 0.448], [0.285, -0.02]], [[1, 0]]];
    e.easeOutQuart = F;
    var A = [[[0, 1]], [[0.5, 0.5], [0.4, 1], [0.6, 0]], [[1, 0]]];
    e.easeInOutQuart = A;
    var v = [[[0, 1]], [[0.857, 0.856], [0.714, 1], [1, 0.712]], [[1, 0]]];
    e.easeInQuint = v;
    var R = [[[0, 1]], [[0.108, 0.2], [1e-3, 0.4], [0.214, -0.012]], [[1, 0]]];
    e.easeOutQuint = R;
    var N = [[[0, 1]], [[0.5, 0.5], [0.5, 1], [0.5, 0]], [[1, 0]]];
    e.easeInOutQuint = N;
    var I = [[[0, 1]], [[0.667, 0.896], [0.38, 1.184], [0.955, 0.616]], [[1, 0]]];
    e.easeInBack = I;
    var E = [[[0, 1]], [[0.335, 0.028], [0.061, 0.22], [0.631, -0.18]], [[1, 0]]];
    e.easeOutBack = E;
    var b = [[[0, 1]], [[0.5, 0.5], [0.4, 1.4], [0.6, -0.4]], [[1, 0]]];
    e.easeInOutBack = b;
    var $ = [[[0, 1]], [[0.474, 0.964], [0.382, 0.988], [0.557, 0.952]], [[0.619, 1.076], [0.565, 1.088], [0.669, 1.08]], [[0.77, 0.916], [0.712, 0.924], [0.847, 0.904]], [[0.911, 1.304], [0.872, 1.316], [0.961, 1.34]], [[1, 0]]];
    e.easeInElastic = $;
    var f = [[[0, 1]], [[0.073, -0.32], [0.034, -0.328], [0.104, -0.344]], [[0.191, 0.092], [0.11, 0.06], [0.256, 0.08]], [[0.31, -0.076], [0.26, -0.068], [0.357, -0.076]], [[0.432, 0.032], [0.362, 0.028], [0.683, -4e-3]], [[1, 0]]];
    e.easeOutElastic = f;
    var _ = [[[0, 1]], [[0.21, 0.94], [0.167, 0.884], [0.252, 0.98]], [[0.299, 1.104], [0.256, 1.092], [0.347, 1.108]], [[0.5, 0.496], [0.451, 0.672], [0.548, 0.324]], [[0.696, -0.108], [0.652, -0.112], [0.741, -0.124]], [[0.805, 0.064], [0.756, 0.012], [0.866, 0.096]], [[1, 0]]];
    e.easeInOutElastic = _;
    var O = [[[0, 1]], [[0.148, 1], [0.075, 0.868], [0.193, 0.848]], [[0.326, 1], [0.276, 0.836], [0.405, 0.712]], [[0.6, 1], [0.511, 0.708], [0.671, 0.348]], [[1, 0]]];
    e.easeInBounce = O;
    var m = [[[0, 1]], [[0.357, 4e-3], [0.27, 0.592], [0.376, 0.252]], [[0.604, -4e-3], [0.548, 0.312], [0.669, 0.184]], [[0.82, 0], [0.749, 0.184], [0.905, 0.132]], [[1, 0]]];
    e.easeOutBounce = m;
    var C = [[[0, 1]], [[0.102, 1], [0.05, 0.864], [0.117, 0.86]], [[0.216, 0.996], [0.208, 0.844], [0.227, 0.808]], [[0.347, 0.996], [0.343, 0.8], [0.48, 0.292]], [[0.635, 4e-3], [0.511, 0.676], [0.656, 0.208]], [[0.787, 0], [0.76, 0.2], [0.795, 0.144]], [[0.905, -4e-3], [0.899, 0.164], [0.944, 0.144]], [[1, 0]]];
    e.easeInOutBounce = C;
    var d = /* @__PURE__ */ new Map([["linear", t], ["easeInSine", r], ["easeOutSine", n], ["easeInOutSine", a], ["easeInQuad", o], ["easeOutQuad", l], ["easeInOutQuad", s], ["easeInCubic", D], ["easeOutCubic", W], ["easeInOutCubic", M], ["easeInQuart", U], ["easeOutQuart", F], ["easeInOutQuart", A], ["easeInQuint", v], ["easeOutQuint", R], ["easeInOutQuint", N], ["easeInBack", I], ["easeOutBack", E], ["easeInOutBack", b], ["easeInElastic", $], ["easeOutElastic", f], ["easeInOutElastic", _], ["easeInBounce", O], ["easeOutBounce", m], ["easeInOutBounce", C]]);
    e.default = d;
  }(Tr)), Tr;
}
var ca;
function Tl() {
  return ca || (ca = 1, function(e) {
    var t = we;
    Object.defineProperty(e, "__esModule", {
      value: true
    }), e.transition = l, e.injectNewCurve = b, e.default = void 0;
    var r = t(Ne()), n = t(ze()), a = t(Rl()), o = "linear";
    function l(f) {
      var _ = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : null, O = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : null, m = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 30, C = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : false;
      if (!s.apply(void 0, arguments))
        return false;
      try {
        var d = D(f), G = W(d, m);
        return !C || typeof O == "number" ? v(_, O, G) : E(_, O, G);
      } catch {
        return console.warn("Transition parameter may be abnormal!"), [O];
      }
    }
    function s(f) {
      var _ = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false, O = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : false, m = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 30;
      if (!f || _ === false || O === false || !m)
        return console.error("transition: Missing Parameters!"), false;
      if ((0, n.default)(_) !== (0, n.default)(O))
        return console.error("transition: Inconsistent Status Types!"), false;
      var C = (0, n.default)(O);
      return C === "string" || C === "boolean" || !f.length ? (console.error("transition: Unsupported Data Type of State!"), false) : (!a.default.has(f) && !(f instanceof Array) && console.warn("transition: Transition curve not found, default curve will be used!"), true);
    }
    function D(f) {
      var _ = "";
      return a.default.has(f) ? _ = a.default.get(f) : f instanceof Array ? _ = f : _ = a.default.get(o), _;
    }
    function W(f, _) {
      var O = 1 / (_ - 1), m = new Array(_).fill(0).map(function(d, G) {
        return G * O;
      }), C = m.map(function(d) {
        return M(f, d);
      });
      return C;
    }
    function M(f, _) {
      var O = U(f, _), m = F(O, _);
      return A(O, m);
    }
    function U(f, _) {
      var O = f.length - 1, m = "", C = "";
      f.findIndex(function(V, X) {
        if (X !== O) {
          m = V, C = f[X + 1];
          var Z = m[0][0], c = C[0][0];
          return _ >= Z && _ < c;
        }
      });
      var d = m[0], G = m[2] || m[0], L = C[1] || C[0], g = C[0];
      return [d, G, L, g];
    }
    function F(f, _) {
      var O = f[0][0], m = f[3][0], C = m - O, d = _ - O;
      return d / C;
    }
    function A(f, _) {
      var O = (0, r.default)(f, 4), m = (0, r.default)(O[0], 2), C = m[1], d = (0, r.default)(O[1], 2), G = d[1], L = (0, r.default)(O[2], 2), g = L[1], V = (0, r.default)(O[3], 2), X = V[1], Z = Math.pow, c = 1 - _, y = C * Z(c, 3), h2 = 3 * G * _ * Z(c, 2), P = 3 * g * Z(_, 2) * c, q = X * Z(_, 3);
      return 1 - (y + h2 + P + q);
    }
    function v(f, _, O) {
      var m = "object";
      return typeof f == "number" && (m = "number"), f instanceof Array && (m = "array"), m === "number" ? R(f, _, O) : m === "array" ? N(f, _, O) : m === "object" ? I(f, _, O) : O.map(function(C) {
        return _;
      });
    }
    function R(f, _, O) {
      var m = _ - f;
      return O.map(function(C) {
        return f + m * C;
      });
    }
    function N(f, _, O) {
      var m = _.map(function(C, d) {
        return typeof C != "number" ? false : C - f[d];
      });
      return O.map(function(C) {
        return m.map(function(d, G) {
          return d === false ? _[G] : f[G] + d * C;
        });
      });
    }
    function I(f, _, O) {
      var m = Object.keys(_), C = m.map(function(L) {
        return f[L];
      }), d = m.map(function(L) {
        return _[L];
      }), G = N(C, d, O);
      return G.map(function(L) {
        var g = {};
        return L.forEach(function(V, X) {
          return g[m[X]] = V;
        }), g;
      });
    }
    function E(f, _, O) {
      var m = v(f, _, O), C = function(g) {
        var V = f[g], X = _[g];
        if ((0, n.default)(X) !== "object")
          return "continue";
        var Z = E(V, X, O);
        m.forEach(function(c, y) {
          return c[g] = Z[y];
        });
      };
      for (var d in _)
        var G = C(d);
      return m;
    }
    function b(f, _) {
      if (!f || !_) {
        console.error("InjectNewCurve Missing Parameters!");
        return;
      }
      a.default.set(f, _);
    }
    var $ = l;
    e.default = $;
  }(Rr)), Rr;
}
var fa;
function Dl() {
  return fa || (fa = 1, function(e) {
    var t = we;
    Object.defineProperty(e, "__esModule", {
      value: true
    }), e.default = void 0;
    var r = t(Ol()), n = t(Gl()), a = t(ze()), o = t(Be()), l = t(Lt()), s = t(Ml()), D = t(Tl()), W = Fe(), M = function F(A, v) {
      (0, l.default)(this, F), v = (0, W.deepClone)(v, true);
      var R = {
        /**
         * @description Weather to render graph
         * @type {Boolean}
         * @default visible = true
         */
        visible: true,
        /**
         * @description Whether to enable drag
         * @type {Boolean}
         * @default drag = false
         */
        drag: false,
        /**
         * @description Whether to enable hover
         * @type {Boolean}
         * @default hover = false
         */
        hover: false,
        /**
         * @description Graph rendering index
         *  Give priority to index high graph in rendering
         * @type {Number}
         * @example index = 1
         */
        index: 1,
        /**
         * @description Animation delay time(ms)
         * @type {Number}
         * @default animationDelay = 0
         */
        animationDelay: 0,
        /**
         * @description Number of animation frames
         * @type {Number}
         * @default animationFrame = 30
         */
        animationFrame: 30,
        /**
         * @description Animation dynamic curve (Supported by transition)
         * @type {String}
         * @default animationCurve = 'linear'
         * @link https://github.com/jiaming743/Transition
         */
        animationCurve: "linear",
        /**
         * @description Weather to pause graph animation
         * @type {Boolean}
         * @default animationPause = false
         */
        animationPause: false,
        /**
         * @description Rectangular hover detection zone
         *  Use this method for hover detection first
         * @type {Null|Array}
         * @default hoverRect = null
         * @example hoverRect = [0, 0, 100, 100] // [Rect start x, y, Rect width, height]
         */
        hoverRect: null,
        /**
         * @description Mouse enter event handler
         * @type {Function|Null}
         * @default mouseEnter = null
         */
        mouseEnter: null,
        /**
         * @description Mouse outer event handler
         * @type {Function|Null}
         * @default mouseOuter = null
         */
        mouseOuter: null,
        /**
         * @description Mouse click event handler
         * @type {Function|Null}
         * @default click = null
         */
        click: null
      }, N = {
        status: "static",
        animationRoot: [],
        animationKeys: [],
        animationFrameState: [],
        cache: {}
      };
      v.shape || (v.shape = {}), v.style || (v.style = {});
      var I = Object.assign({}, A.shape, v.shape);
      Object.assign(R, v, N), Object.assign(this, A, R), this.shape = I, this.style = new s.default(v.style), this.addedProcessor();
    };
    e.default = M, M.prototype.addedProcessor = function() {
      typeof this.setGraphCenter == "function" && this.setGraphCenter(null, this), typeof this.added == "function" && this.added(this);
    }, M.prototype.drawProcessor = function(F, A) {
      var v = F.ctx;
      A.style.initStyle(v), typeof this.beforeDraw == "function" && this.beforeDraw(this, F), A.draw(F, A), typeof this.drawed == "function" && this.drawed(this, F), A.style.restoreTransform(v);
    }, M.prototype.hoverCheckProcessor = function(F, A) {
      var v = A.hoverRect, R = A.style, N = A.hoverCheck, I = R.graphCenter, E = R.rotate, b = R.scale, $ = R.translate;
      return I && (E && (F = (0, W.getRotatePointPos)(-E, F, I)), b && (F = (0, W.getScalePointPos)(b.map(function(f) {
        return 1 / f;
      }), F, I)), $ && (F = (0, W.getTranslatePointPos)($.map(function(f) {
        return f * -1;
      }), F))), v ? W.checkPointIsInRect.apply(void 0, [F].concat((0, o.default)(v))) : N(F, this);
    }, M.prototype.moveProcessor = function(F) {
      this.move(F, this), typeof this.beforeMove == "function" && this.beforeMove(F, this), typeof this.setGraphCenter == "function" && this.setGraphCenter(F, this), typeof this.moved == "function" && this.moved(F, this);
    }, M.prototype.attr = function(F) {
      var A = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : void 0;
      if (!F || A === void 0)
        return false;
      var v = (0, a.default)(this[F]) === "object";
      v && (A = (0, W.deepClone)(A, true));
      var R = this.render;
      F === "style" ? this.style.update(A) : v ? Object.assign(this[F], A) : this[F] = A, F === "index" && R.sortGraphsByIndex(), R.drawAllGraph();
    }, M.prototype.animation = function() {
      var F = (0, n.default)(
        r.default.mark(function A(v, R) {
          var N, I, E, b, $, f, _, O, m, C = arguments;
          return r.default.wrap(function(G) {
            for (; ; )
              switch (G.prev = G.next) {
                case 0:
                  if (N = C.length > 2 && C[2] !== void 0 ? C[2] : false, !(v !== "shape" && v !== "style")) {
                    G.next = 4;
                    break;
                  }
                  return console.error("Only supported shape and style animation!"), G.abrupt("return");
                case 4:
                  if (R = (0, W.deepClone)(R, true), v === "style" && this.style.colorProcessor(R), I = this[v], E = Object.keys(R), b = {}, E.forEach(function(L) {
                    return b[L] = I[L];
                  }), $ = this.animationFrame, f = this.animationCurve, _ = this.animationDelay, O = (0, D.default)(f, b, R, $, true), this.animationRoot.push(I), this.animationKeys.push(E), this.animationFrameState.push(O), !N) {
                    G.next = 17;
                    break;
                  }
                  return G.abrupt("return");
                case 17:
                  if (!(_ > 0)) {
                    G.next = 20;
                    break;
                  }
                  return G.next = 20, U(_);
                case 20:
                  return m = this.render, G.abrupt("return", new Promise(
                    function() {
                      var L = (0, n.default)(
                        r.default.mark(function g(V) {
                          return r.default.wrap(function(Z) {
                            for (; ; )
                              switch (Z.prev = Z.next) {
                                case 0:
                                  return Z.next = 2, m.launchAnimation();
                                case 2:
                                  V();
                                case 3:
                                case "end":
                                  return Z.stop();
                              }
                          }, g);
                        })
                      );
                      return function(g) {
                        return L.apply(this, arguments);
                      };
                    }()
                  ));
                case 22:
                case "end":
                  return G.stop();
              }
          }, A, this);
        })
      );
      return function(A, v) {
        return F.apply(this, arguments);
      };
    }(), M.prototype.turnNextAnimationFrame = function(F) {
      var A = this.animationDelay, v = this.animationRoot, R = this.animationKeys, N = this.animationFrameState, I = this.animationPause;
      I || Date.now() - F < A || (v.forEach(function(E, b) {
        R[b].forEach(function($) {
          E[$] = N[b][0][$];
        });
      }), N.forEach(function(E, b) {
        E.shift();
        var $ = E.length === 0;
        $ && (v[b] = null), $ && (R[b] = null);
      }), this.animationFrameState = N.filter(function(E) {
        return E.length;
      }), this.animationRoot = v.filter(function(E) {
        return E;
      }), this.animationKeys = R.filter(function(E) {
        return E;
      }));
    }, M.prototype.animationEnd = function() {
      var F = this.animationFrameState, A = this.animationKeys, v = this.animationRoot, R = this.render;
      return v.forEach(function(N, I) {
        var E = A[I], b = F[I].pop();
        E.forEach(function($) {
          return N[$] = b[$];
        });
      }), this.animationFrameState = [], this.animationKeys = [], this.animationRoot = [], R.drawAllGraph();
    }, M.prototype.pauseAnimation = function() {
      this.attr("animationPause", true);
    }, M.prototype.playAnimation = function() {
      var F = this.render;
      return this.attr("animationPause", false), new Promise(
        function() {
          var A = (0, n.default)(
            r.default.mark(function v(R) {
              return r.default.wrap(function(I) {
                for (; ; )
                  switch (I.prev = I.next) {
                    case 0:
                      return I.next = 2, F.launchAnimation();
                    case 2:
                      R();
                    case 3:
                    case "end":
                      return I.stop();
                  }
              }, v);
            })
          );
          return function(v) {
            return A.apply(this, arguments);
          };
        }()
      );
    }, M.prototype.delProcessor = function(F) {
      var A = this, v = F.graphs, R = v.findIndex(function(N) {
        return N === A;
      });
      R !== -1 && (typeof this.beforeDelete == "function" && this.beforeDelete(this), v.splice(R, 1, null), typeof this.deleted == "function" && this.deleted(this));
    };
    function U(F) {
      return new Promise(function(A) {
        setTimeout(A, F);
      });
    }
  }(Lr)), Lr;
}
var da;
function Bl() {
  return da || (da = 1, function(e) {
    var t = we;
    Object.defineProperty(e, "__esModule", {
      value: true
    }), e.default = void 0;
    var r = t(Ue()), n = t(Be()), a = t(Lt()), o = t(St), l = t(Gn()), s = Fe(), D = t(Mn()), W = t(Dl());
    function M(E, b) {
      var $ = Object.keys(E);
      if (Object.getOwnPropertySymbols) {
        var f = Object.getOwnPropertySymbols(E);
        b && (f = f.filter(function(_) {
          return Object.getOwnPropertyDescriptor(E, _).enumerable;
        })), $.push.apply($, f);
      }
      return $;
    }
    function U(E) {
      for (var b = 1; b < arguments.length; b++) {
        var $ = arguments[b] != null ? arguments[b] : {};
        b % 2 ? M($, true).forEach(function(f) {
          (0, r.default)(E, f, $[f]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(E, Object.getOwnPropertyDescriptors($)) : M($).forEach(function(f) {
          Object.defineProperty(E, f, Object.getOwnPropertyDescriptor($, f));
        });
      }
      return E;
    }
    var F = function E(b) {
      if ((0, a.default)(this, E), !b) {
        console.error("CRender Missing parameters!");
        return;
      }
      var $ = b.getContext("2d"), f = b.clientWidth, _ = b.clientHeight, O = [f, _];
      b.setAttribute("width", f), b.setAttribute("height", _), this.ctx = $, this.area = O, this.animationStatus = false, this.graphs = [], this.color = o.default, this.bezierCurve = l.default, b.addEventListener("mousedown", R.bind(this)), b.addEventListener("mousemove", N.bind(this)), b.addEventListener("mouseup", I.bind(this));
    };
    e.default = F, F.prototype.clearArea = function() {
      var E, b = this.area;
      (E = this.ctx).clearRect.apply(E, [0, 0].concat((0, n.default)(b)));
    }, F.prototype.add = function() {
      var E = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, b = E.name;
      if (!b) {
        console.error("add Missing parameters!");
        return;
      }
      var $ = D.default.get(b);
      if (!$) {
        console.warn("No corresponding graph configuration found!");
        return;
      }
      var f = new W.default($, E);
      if (f.validator(f))
        return f.render = this, this.graphs.push(f), this.sortGraphsByIndex(), this.drawAllGraph(), f;
    }, F.prototype.sortGraphsByIndex = function() {
      var E = this.graphs;
      E.sort(function(b, $) {
        if (b.index > $.index)
          return 1;
        if (b.index === $.index)
          return 0;
        if (b.index < $.index)
          return -1;
      });
    }, F.prototype.delGraph = function(E) {
      typeof E.delProcessor == "function" && (E.delProcessor(this), this.graphs = this.graphs.filter(function(b) {
        return b;
      }), this.drawAllGraph());
    }, F.prototype.delAllGraph = function() {
      var E = this;
      this.graphs.forEach(function(b) {
        return b.delProcessor(E);
      }), this.graphs = this.graphs.filter(function(b) {
        return b;
      }), this.drawAllGraph();
    }, F.prototype.drawAllGraph = function() {
      var E = this;
      this.clearArea(), this.graphs.filter(function(b) {
        return b && b.visible;
      }).forEach(function(b) {
        return b.drawProcessor(E, b);
      });
    }, F.prototype.launchAnimation = function() {
      var E = this, b = this.animationStatus;
      if (!b)
        return this.animationStatus = true, new Promise(function($) {
          A.call(E, function() {
            E.animationStatus = false, $();
          }, Date.now());
        });
    };
    function A(E, b) {
      var $ = this.graphs;
      if (!v($)) {
        E();
        return;
      }
      $.forEach(function(f) {
        return f.turnNextAnimationFrame(b);
      }), this.drawAllGraph(), requestAnimationFrame(A.bind(this, E, b));
    }
    function v(E) {
      return E.find(function(b) {
        return !b.animationPause && b.animationFrameState.length;
      });
    }
    function R(E) {
      var b = this.graphs, $ = b.find(function(f) {
        return f.status === "hover";
      });
      $ && ($.status = "active");
    }
    function N(E) {
      var b = E.offsetX, $ = E.offsetY, f = [b, $], _ = this.graphs, O = _.find(function(g) {
        return g.status === "active" || g.status === "drag";
      });
      if (O) {
        if (!O.drag)
          return;
        if (typeof O.move != "function") {
          console.error("No move method is provided, cannot be dragged!");
          return;
        }
        O.moveProcessor(E), O.status = "drag";
        return;
      }
      var m = _.find(function(g) {
        return g.status === "hover";
      }), C = _.filter(function(g) {
        return g.hover && (typeof g.hoverCheck == "function" || g.hoverRect);
      }), d = C.find(function(g) {
        return g.hoverCheckProcessor(f, g);
      });
      d ? document.body.style.cursor = d.style.hoverCursor : document.body.style.cursor = "default";
      var G = false, L = false;
      if (m && (G = typeof m.mouseOuter == "function"), d && (L = typeof d.mouseEnter == "function"), !(!d && !m)) {
        if (!d && m) {
          G && m.mouseOuter(E, m), m.status = "static";
          return;
        }
        if (!(d && d === m)) {
          if (d && !m) {
            L && d.mouseEnter(E, d), d.status = "hover";
            return;
          }
          d && m && d !== m && (G && m.mouseOuter(E, m), m.status = "static", L && d.mouseEnter(E, d), d.status = "hover");
        }
      }
    }
    function I(E) {
      var b = this.graphs, $ = b.find(function(_) {
        return _.status === "active";
      }), f = b.find(function(_) {
        return _.status === "drag";
      });
      $ && typeof $.click == "function" && $.click(E, $), b.forEach(function(_) {
        return _ && (_.status = "static");
      }), $ && ($.status = "hover"), f && (f.status = "hover");
    }
    F.prototype.clone = function(E) {
      var b = E.style.getStyle(), $ = U({}, E, {
        style: b
      });
      return delete $.render, $ = (0, s.deepClone)($, true), this.add($);
    };
  }(sr)), sr;
}
(function(e) {
  var t = we;
  Object.defineProperty(e, "__esModule", {
    value: true
  }), Object.defineProperty(e, "CRender", {
    enumerable: true,
    get: function() {
      return r.default;
    }
  }), Object.defineProperty(e, "extendNewGraph", {
    enumerable: true,
    get: function() {
      return n.extendNewGraph;
    }
  }), e.default = void 0;
  var r = t(Bl()), n = Mn(), a = r.default;
  e.default = a;
})(Zt);
var vi = ci(Zt);
var Fl = { class: "dv-water-pond-level" };
var Nl = { key: 0 };
var jl = ["id"];
var El = ["offset", "stop-color"];
var Wl = ["stroke", "fill", "x", "y"];
var zl = ["cx", "cy", "rx", "ry", "stroke"];
var ql = ["rx", "ry", "width", "height", "stroke"];
var Il = {
  __name: "index",
  props: {
    config: Object,
    default: () => ({})
  },
  setup(e) {
    const t = e, r = Ve(), n = ref(null), a = reactive({
      gradientId: `water-level-pond-${r}`,
      defaultConfig: {
        /**
             * @description Data
             * @type {Array<Number>}
             * @default data = []
             * @example data = [60, 40]
             */
        data: [],
        /**
             * @description Shape of wanter level pond
             * @type {String}
             * @default shape = 'rect'
             * @example shape = 'rect' | 'roundRect' | 'round'
             */
        shape: "rect",
        /**
             * @description Water wave number
             * @type {Number}
             * @default waveNum = 3
             */
        waveNum: 3,
        /**
             * @description Water wave height (px)
             * @type {Number}
             * @default waveHeight = 40
             */
        waveHeight: 40,
        /**
             * @description Wave opacity
             * @type {Number}
             * @default waveOpacity = 0.4
             */
        waveOpacity: 0.4,
        /**
             * @description Colors (hex|rgb|rgba|color keywords)
             * @type {Array<String>}
             * @default colors = ['#00BAFF', '#3DE7C9']
             * @example colors = ['#000', 'rgb(0, 0, 0)', 'rgba(0, 0, 0, 1)', 'red']
             */
        colors: ["#3DE7C9", "#00BAFF"],
        /**
             * @description Formatter
             * @type {String}
             * @default formatter = '{value}%'
             */
        formatter: "{value}%"
      },
      mergedConfig: {},
      renderer: null,
      svgBorderGradient: [],
      details: "",
      waves: [],
      animation: false
    }), o = computed(() => {
      const { shape: b } = a.mergedConfig;
      return b === "round" ? "50%" : b === "rect" ? "0" : b === "roundRect" ? "10px" : "0";
    }), l = computed(() => {
      const { shape: b } = a.mergedConfig;
      return b || "rect";
    });
    watch(() => t.config, () => {
      a.renderer.delAllGraph(), a.waves = [], setTimeout(W, 0);
    }, {
      deep: true
    }), onMounted(() => {
      s();
    }), onBeforeUnmount(() => {
      a.renderer.delAllGraph(), a.waves = [];
    });
    function s() {
      D(), t.config && W();
    }
    function D() {
      a.renderer = new vi(n.value);
    }
    function W() {
      M(), U(), F(), A(), E();
    }
    function M() {
      a.mergedConfig = Ce($e(a.defaultConfig, true), t.config);
    }
    function U() {
      const { colors: b } = a.mergedConfig, f = 100 / (b.length - 1);
      a.svgBorderGradient = b.map((_, O) => [f * O, _]);
    }
    function F() {
      const { data: b, formatter: $ } = a.mergedConfig;
      if (!b.length) {
        a.details = "";
        return;
      }
      const f = Math.max(...b);
      a.details = $.replace("{value}", f);
    }
    function A() {
      const b = v(), $ = N();
      a.waves = b.map((f) => a.renderer.add({
        name: "smoothline",
        animationFrame: 300,
        shape: f,
        style: $,
        drawed: I
      }));
    }
    function v() {
      const { waveNum: b, waveHeight: $, data: f } = a.mergedConfig, [_, O] = a.renderer.area, m = b * 4 + 4, C = _ / b / 2;
      return f.map((d) => {
        let G = new Array(m).fill(0).map((L, g) => {
          const V = _ - C * g, X = (1 - d / 100) * O, Z = g % 2 === 0 ? X : X - $;
          return [V, Z];
        });
        return G = G.map((L) => R(L, [C * 2, 0])), { points: G };
      });
    }
    function R([b, $], [f, _]) {
      return [b + f, $ + _];
    }
    function N() {
      const b = a.renderer.area[1];
      return {
        gradientColor: a.mergedConfig.colors,
        gradientType: "linear",
        gradientParams: [0, 0, 0, b],
        gradientWith: "fill",
        opacity: a.mergedConfig.waveOpacity,
        translate: [0, 0]
      };
    }
    function I({ shape: { points: b } }, { ctx: $, area: f }) {
      const _ = b[0], O = b.slice(-1)[0], m = f[1];
      $.lineTo(O[0], m), $.lineTo(_[0], m), $.closePath(), $.fill();
    }
    async function E(b = 1) {
      if (a.animation)
        return;
      a.animation = true;
      const $ = a.renderer.area[0];
      a.waves.forEach((f) => {
        f.attr("style", { translate: [0, 0] }), f.animation("style", {
          translate: [$, 0]
        }, true);
      }), await a.renderer.launchAnimation(), a.animation = false, a.renderer.graphs.length && E(b + 1);
    }
    return (b, $) => (openBlock(), createElementBlock("div", Fl, [
      unref(a).renderer ? (openBlock(), createElementBlock("svg", Nl, [
        createBaseVNode("defs", null, [
          createBaseVNode("linearGradient", {
            id: unref(a).gradientId,
            x1: "0%",
            y1: "0%",
            x2: "0%",
            y2: "100%"
          }, [
            (openBlock(true), createElementBlock(Fragment, null, renderList(unref(a).svgBorderGradient, (f) => (openBlock(), createElementBlock("stop", {
              key: f[0],
              offset: f[0],
              "stop-color": f[1]
            }, null, 8, El))), 128))
          ], 8, jl)
        ]),
        unref(a).renderer ? (openBlock(), createElementBlock("text", {
          key: 0,
          stroke: `url(#${unref(a).gradientId})`,
          fill: `url(#${unref(a).gradientId})`,
          x: unref(a).renderer.area[0] / 2 + 8,
          y: unref(a).renderer.area[1] / 2 + 8
        }, toDisplayString(unref(a).details), 9, Wl)) : createCommentVNode("", true),
        !unref(l) || unref(l) === "round" ? (openBlock(), createElementBlock("ellipse", {
          key: 1,
          cx: unref(a).renderer.area[0] / 2 + 8,
          cy: unref(a).renderer.area[1] / 2 + 8,
          rx: unref(a).renderer.area[0] / 2 + 5,
          ry: unref(a).renderer.area[1] / 2 + 5,
          stroke: `url(#${unref(a).gradientId})`
        }, null, 8, zl)) : (openBlock(), createElementBlock("rect", {
          key: 2,
          x: "2",
          y: "2",
          rx: unref(l) === "roundRect" ? 10 : 0,
          ry: unref(l) === "roundRect" ? 10 : 0,
          width: unref(a).renderer.area[0] + 12,
          height: unref(a).renderer.area[1] + 12,
          stroke: `url(#${unref(a).gradientId})`
        }, null, 8, ql))
      ])) : createCommentVNode("", true),
      createBaseVNode("canvas", {
        ref_key: "waterPondLevel",
        ref: n,
        style: normalizeStyle(`border-radius: ${unref(o)};`)
      }, null, 4)
    ]));
  }
};
var Dr = {
  install(e) {
    e.component("DvWaterLevelPond", Il);
  }
};
var Hl = {};
var Vl = { class: "dv-loading" };
var Ul = createStaticVNode('<svg width="50px" height="50px"><circle cx="25" cy="25" r="20" fill="transparent" stroke-width="3" stroke-dasharray="31.415, 31.415" stroke="#02bcfe" stroke-linecap="round"><animateTransform attributeName="transform" type="rotate" values="0, 25 25;360, 25 25" dur="1.5s" repeatCount="indefinite"></animateTransform><animate attributeName="stroke" values="#02bcfe;#3be6cb;#02bcfe" dur="3s" repeatCount="indefinite"></animate></circle><circle cx="25" cy="25" r="10" fill="transparent" stroke-width="3" stroke-dasharray="15.7, 15.7" stroke="#3be6cb" stroke-linecap="round"><animateTransform attributeName="transform" type="rotate" values="360, 25 25;0, 25 25" dur="1.5s" repeatCount="indefinite"></animateTransform><animate attributeName="stroke" values="#3be6cb;#02bcfe;#3be6cb" dur="3s" repeatCount="indefinite"></animate></circle></svg>', 1);
var Xl = { class: "loading-tip" };
function Ql(e, t) {
  return openBlock(), createElementBlock("div", Vl, [
    Ul,
    createBaseVNode("div", Xl, [
      renderSlot(e.$slots, "default")
    ])
  ]);
}
var Yl = Ze(Hl, [["render", Ql]]);
var Br = {
  install(e) {
    e.component("DvLoading", Yl);
  }
};
var Kl = ["width", "height"];
var Jl = ["id"];
var Zl = createBaseVNode("stop", {
  offset: "0%",
  "stop-color": "#fff",
  "stop-opacity": "1"
}, null, -1);
var es = createBaseVNode("stop", {
  offset: "100%",
  "stop-color": "#fff",
  "stop-opacity": "0"
}, null, -1);
var ts = [
  Zl,
  es
];
var rs = ["id"];
var ns = createBaseVNode("stop", {
  offset: "0%",
  "stop-color": "#fff",
  "stop-opacity": "0"
}, null, -1);
var as = createBaseVNode("stop", {
  offset: "100%",
  "stop-color": "#fff",
  "stop-opacity": "1"
}, null, -1);
var is = [
  ns,
  as
];
var os = ["id", "cx", "cy"];
var ls = ["values", "dur"];
var ss = ["dur"];
var us = ["id"];
var cs = ["xlink:href", "fill"];
var fs = ["xlink:href", "fill", "mask"];
var ds = ["xlink:href", "width", "height", "x", "y"];
var hs = ["fill", "x", "y"];
var vs = ["id", "d"];
var ps = ["xlink:href", "stroke-width", "stroke"];
var gs = ["id"];
var ms = ["r", "fill"];
var ys = ["dur", "path"];
var bs = ["xlink:href", "stroke-width", "stroke", "mask"];
var xs = ["from", "to", "dur"];
var Cs = {
  __name: "index",
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    dev: {
      type: Boolean,
      default: false
    }
  },
  setup(e) {
    const t = e, r = Ve(), n = ref(null), { width: a, height: o } = xe(n, W, D), l = reactive({
      unique: Math.random(),
      flylineGradientId: `flyline-gradient-id-${r}`,
      haloGradientId: `halo-gradient-id-${r}`,
      /**
           * @description Type Declaration
           *
           * interface Halo {
           *    show?: boolean
           *    duration?: [number, number]
           *    color?: string
           *    radius?: number
           * }
           *
           * interface Text {
           *    show?: boolean
           *    offset?: [number, number]
           *    color?: string
           *    fontSize?: number
           * }
           *
           * interface Icon {
           *    show?: boolean
           *    src?: string
           *    width?: number
           *    height?: number
           * }
           *
           * interface Point {
           *    name: string
           *    coordinate: [number, number]
           *    halo?: Halo
           *    text?: Text
           *    icon?: Icon
           * }
           *
           * interface Line {
           *    width?: number
           *    color?: string
           *    orbitColor?: string
           *    duration?: [number, number]
           *    radius?: string
           * }
           *
           * interface Flyline extends Line {
           *    source: string
           *    target: string
           * }
           *
           * interface FlylineWithPath extends Flyline {
           *    d: string
           *    path: [[number, number], [number, number], [number, number]]
           *    key: string
           * }
           */
      defaultConfig: {
        /**
             * @description Flyline chart points
             * @type {FlylineChartPoint[]}
             * @default points = []
             */
        points: [],
        /**
             * @description Lines
             * @type {Flyline[]}
             * @default lines = []
             */
        lines: [],
        /**
             * @description Global halo configuration
             * @type {Halo}
             */
        halo: {
          /**
               * @description Whether to show halo
               * @type {Boolean}
               * @default show = false
               */
          show: false,
          /**
               * @description Halo animation duration (1s = 10)
               * @type {[number, number]}
               */
          duration: [20, 30],
          /**
               * @description Halo color
               * @type {String}
               * @default color = '#fb7293'
               */
          color: "#fb7293",
          /**
               * @description Halo radius
               * @type {Number}
               * @default radius = 120
               */
          radius: 120
        },
        /**
             * @description Global text configuration
             * @type {Text}
             */
        text: {
          /**
               * @description Whether to show text
               * @type {Boolean}
               * @default show = false
               */
          show: false,
          /**
               * @description Text offset
               * @type {[number, number]}
               * @default offset = [0, 15]
               */
          offset: [0, 15],
          /**
               * @description Text color
               * @type {String}
               * @default color = '#ffdb5c'
               */
          color: "#ffdb5c",
          /**
               * @description Text font size
               * @type {Number}
               * @default fontSize = 12
               */
          fontSize: 12
        },
        /**
             * @description Global icon configuration
             * @type {Icon}
             */
        icon: {
          /**
               * @description Whether to show icon
               * @type {Boolean}
               * @default show = false
               */
          show: false,
          /**
               * @description Icon src
               * @type {String}
               * @default src = ''
               */
          src: "",
          /**
               * @description Icon width
               * @type {Number}
               * @default width = 15
               */
          width: 15,
          /**
               * @description Icon height
               * @type {Number}
               * @default width = 15
               */
          height: 15
        },
        /**
             * @description Global line configuration
             * @type {Line}
             */
        line: {
          /**
               * @description Line width
               * @type {Number}
               * @default width = 1
               */
          width: 1,
          /**
               * @description Flyline color
               * @type {String}
               * @default color = '#ffde93'
               */
          color: "#ffde93",
          /**
               * @description Orbit color
               * @type {String}
               * @default orbitColor = 'rgba(103, 224, 227, .2)'
               */
          orbitColor: "rgba(103, 224, 227, .2)",
          /**
               * @description Flyline animation duration
               * @type {[number, number]}
               * @default duration = [20, 30]
               */
          duration: [20, 30],
          /**
               * @description Flyline radius
               * @type {Number}
               * @default radius = 100
               */
          radius: 100
        },
        /**
             * @description Back ground image url
             * @type {String}
             * @default bgImgSrc = ''
             */
        bgImgSrc: "",
        /**
             * @description K value
             * @type {Number}
             * @default k = -0.5
             * @example k = -1 ~ 1
             */
        k: -0.5,
        /**
             * @description Flyline curvature
             * @type {Number}
             * @default curvature = 5
             */
        curvature: 5,
        /**
             * @description Relative points position
             * @type {Boolean}
             * @default relative = true
             */
        relative: true
      },
      /**
           * @description Fly line data
           * @type {FlylineWithPath[]}
           * @default flylines = []
           */
      flylines: [],
      /**
           * @description Fly line lengths
           * @type {Number[]}
           * @default flylineLengths = []
           */
      flylineLengths: [],
      /**
           * @description Fly line points
           * @default flylinePoints = []
           */
      flylinePoints: [],
      mergedConfig: null
    });
    let s;
    onMounted(() => {
      s = getCurrentInstance();
    }), watch(() => t.config, () => {
      M();
    }, {
      deep: true
    });
    function D() {
      M();
    }
    function W() {
      M();
    }
    async function M() {
      U(), F(), A(), await I();
    }
    function U() {
      const b = Ce($e(l.defaultConfig, true), t.config || {}), { points: $, lines: f, halo: _, text: O, icon: m, line: C } = b;
      b.points = $.map((d) => (d.halo = Ce($e(_, true), d.halo || {}), d.text = Ce($e(O, true), d.text || {}), d.icon = Ce($e(m, true), d.icon || {}), d)), b.lines = f.map((d) => Ce($e(C, true), d)), l.mergedConfig = b;
    }
    function F() {
      const { relative: b, points: $ } = l.mergedConfig;
      l.flylinePoints = $.map((f, _) => {
        const { coordinate: [O, m], halo: C, icon: d, text: G } = f;
        b && (f.coordinate = [O * a.value, m * o.value]), f.halo.time = _t(...C.duration) / 10;
        const { width: L, height: g } = d;
        f.icon.x = f.coordinate[0] - L / 2, f.icon.y = f.coordinate[1] - g / 2;
        const [V, X] = G.offset;
        return f.text.x = f.coordinate[0] + V, f.text.y = f.coordinate[1] + X, f.key = `${f.coordinate.toString()}${_}`, f;
      });
    }
    function A() {
      const { points: b, lines: $ } = l.mergedConfig;
      l.flylines = $.map((f) => {
        const { source: _, target: O, duration: m } = f, C = b.find(({ name: X }) => X === _).coordinate, d = b.find(({ name: X }) => X === O).coordinate, G = v(C, d).map((X) => X.map((Z) => parseFloat(Z.toFixed(10)))), L = `M${G[0].toString()} Q${G[1].toString()} ${G[2].toString()}`, g = `path${G.toString()}`, V = _t(...m) / 10;
        return { ...f, path: G, key: g, d: L, time: V };
      });
    }
    function v(b, $) {
      const f = R(b, $);
      return [b, f, $];
    }
    function R([b, $], [f, _]) {
      const { curvature: O, k: m } = l.mergedConfig, [C, d] = [(b + f) / 2, ($ + _) / 2], L = Vt([b, $], [f, _]) / O, g = L / 2;
      let [V, X] = [C, d];
      do
        V += g, X = N(m, [C, d], V)[1];
      while (Vt([C, d], [V, X]) < L);
      return [V, X];
    }
    function N(b, [$, f], _) {
      const O = f - b * $ + b * _;
      return [_, O];
    }
    async function I() {
      await nextTick(), l.flylineLengths = l.flylines.map(({ key: b }) => s.proxy.$refs[b][0].getTotalLength());
    }
    function E({ offsetX: b, offsetY: $ }) {
      if (!t.dev)
        return;
      const f = (b / a.value).toFixed(2), _ = ($ / o.value).toFixed(2);
      console.warn(`dv-flyline-chart-enhanced DEV: 
 Click Position is [${b}, ${$}] 
 Relative Position is [${f}, ${_}]`);
    }
    return (b, $) => (openBlock(), createElementBlock("div", {
      ref_key: "flylineChartEnhanced",
      ref: n,
      class: "dv-flyline-chart-enhanced",
      style: normalizeStyle(`background-image: url(${unref(l).mergedConfig ? unref(l).mergedConfig.bgImgSrc : ""})`),
      onClick: E
    }, [
      unref(l).flylines.length ? (openBlock(), createElementBlock("svg", {
        key: 0,
        width: unref(a),
        height: unref(o)
      }, [
        createBaseVNode("defs", null, [
          createBaseVNode("radialGradient", {
            id: unref(l).flylineGradientId,
            cx: "50%",
            cy: "50%",
            r: "50%"
          }, ts, 8, Jl),
          createBaseVNode("radialGradient", {
            id: unref(l).haloGradientId,
            cx: "50%",
            cy: "50%",
            r: "50%"
          }, is, 8, rs)
        ]),
        (openBlock(true), createElementBlock(Fragment, null, renderList(unref(l).flylinePoints, (f) => (openBlock(), createElementBlock("g", {
          key: f.key + Math.random()
        }, [
          createBaseVNode("defs", null, [
            f.halo.show ? (openBlock(), createElementBlock("circle", {
              key: 0,
              id: `halo${unref(l).unique}${f.key}`,
              cx: f.coordinate[0],
              cy: f.coordinate[1]
            }, [
              createBaseVNode("animate", {
                attributeName: "r",
                values: `1;${f.halo.radius}`,
                dur: `${f.halo.time}s`,
                repeatCount: "indefinite"
              }, null, 8, ls),
              createBaseVNode("animate", {
                attributeName: "opacity",
                values: "1;0",
                dur: `${f.halo.time}s`,
                repeatCount: "indefinite"
              }, null, 8, ss)
            ], 8, os)) : createCommentVNode("", true)
          ]),
          createBaseVNode("mask", {
            id: `mask${unref(l).unique}${f.key}`
          }, [
            f.halo.show ? (openBlock(), createElementBlock("use", {
              key: 0,
              "xlink:href": `#halo${unref(l).unique}${f.key}`,
              fill: `url(#${unref(l).haloGradientId})`
            }, null, 8, cs)) : createCommentVNode("", true)
          ], 8, us),
          f.halo.show ? (openBlock(), createElementBlock("use", {
            key: 0,
            "xlink:href": `#halo${unref(l).unique}${f.key}`,
            fill: f.halo.color,
            mask: `url(#mask${unref(l).unique}${f.key})`
          }, null, 8, fs)) : createCommentVNode("", true),
          f.icon.show ? (openBlock(), createElementBlock("image", {
            key: 1,
            "xlink:href": f.icon.src,
            width: f.icon.width,
            height: f.icon.height,
            x: f.icon.x,
            y: f.icon.y
          }, null, 8, ds)) : createCommentVNode("", true),
          f.text.show ? (openBlock(), createElementBlock("text", {
            key: 2,
            style: normalizeStyle(`fontSize:${f.text.fontSize}px;color:${f.text.color}`),
            fill: f.text.color,
            x: f.text.x,
            y: f.text.y
          }, toDisplayString(f.name), 13, hs)) : createCommentVNode("", true)
        ]))), 128)),
        (openBlock(true), createElementBlock(Fragment, null, renderList(unref(l).flylines, (f, _) => (openBlock(), createElementBlock("g", {
          key: f.key + Math.random()
        }, [
          createBaseVNode("defs", null, [
            createBaseVNode("path", {
              id: f.key,
              ref_for: true,
              ref: f.key,
              d: f.d,
              fill: "transparent"
            }, null, 8, vs)
          ]),
          createBaseVNode("use", {
            "xlink:href": `#${f.key}`,
            "stroke-width": f.width,
            stroke: f.orbitColor
          }, null, 8, ps),
          createBaseVNode("mask", {
            id: `mask${unref(l).unique}${f.key}`
          }, [
            createBaseVNode("circle", {
              cx: "0",
              cy: "0",
              r: f.radius,
              fill: `url(#${unref(l).flylineGradientId})`
            }, [
              createBaseVNode("animateMotion", {
                dur: f.time,
                path: f.d,
                rotate: "auto",
                repeatCount: "indefinite"
              }, null, 8, ys)
            ], 8, ms)
          ], 8, gs),
          unref(l).flylineLengths[_] ? (openBlock(), createElementBlock("use", {
            key: 0,
            "xlink:href": `#${f.key}`,
            "stroke-width": f.width,
            stroke: f.color,
            mask: `url(#mask${unref(l).unique}${f.key})`
          }, [
            createBaseVNode("animate", {
              attributeName: "stroke-dasharray",
              from: `0, ${unref(l).flylineLengths[_]}`,
              to: `${unref(l).flylineLengths[_]}, 0`,
              dur: f.time,
              repeatCount: "indefinite"
            }, null, 8, xs)
          ], 8, bs)) : createCommentVNode("", true)
        ]))), 128))
      ], 8, Kl)) : createCommentVNode("", true)
    ], 4));
  }
};
var Fr = {
  install(e) {
    e.component("DvFlylineChartEnhanced", Cs);
  }
};
var _s = ["width", "height"];
var $s = ["id"];
var Ps = createBaseVNode("stop", {
  offset: "0%",
  "stop-color": "#fff",
  "stop-opacity": "1"
}, null, -1);
var ws = createBaseVNode("stop", {
  offset: "100%",
  "stop-color": "#fff",
  "stop-opacity": "0"
}, null, -1);
var ks = [
  Ps,
  ws
];
var As = ["id"];
var Ls = createBaseVNode("stop", {
  offset: "0%",
  "stop-color": "#fff",
  "stop-opacity": "0"
}, null, -1);
var Ss = createBaseVNode("stop", {
  offset: "100%",
  "stop-color": "#fff",
  "stop-opacity": "1"
}, null, -1);
var Os = [
  Ls,
  Ss
];
var Gs = ["id", "cx", "cy"];
var Ms = ["values", "dur"];
var Rs = ["dur"];
var Ts = ["xlink:href", "width", "height", "x", "y"];
var Ds = ["id"];
var Bs = ["xlink:href", "fill"];
var Fs = ["xlink:href", "fill", "mask"];
var Ns = ["id", "d"];
var js = ["xlink:href", "stroke-width", "stroke"];
var Es = ["xlink:href", "stroke-width", "stroke", "mask"];
var Ws = ["from", "to", "dur"];
var zs = ["id"];
var qs = ["r", "fill"];
var Is = ["dur", "path"];
var Hs = ["xlink:href", "width", "height", "x", "y"];
var Vs = ["fill", "x", "y"];
var Us = {
  __name: "index",
  props: {
    config: {
      type: Object,
      default: () => ({})
    },
    dev: {
      type: Boolean,
      default: false
    }
  },
  setup(e) {
    const t = e, r = Ve(), n = ref(null), { width: a, height: o } = xe(n, W, D), l = reactive({
      unique: Math.random(),
      maskId: `flyline-mask-id-${r}`,
      maskCircleId: `mask-circle-id-${r}`,
      gradientId: `gradient-id-${r}`,
      gradient2Id: `gradient2-id-${r}`,
      defaultConfig: {
        /**
             * @description Flyline chart center point
             * @type {Array<Number>}
             * @default centerPoint = [0, 0]
             */
        centerPoint: [0, 0],
        /**
             * @description Flyline start points
             * @type {Array<Array<Number>>}
             * @default points = []
             * @example points = [[10, 10], [100, 100]]
             */
        points: [],
        /**
             * @description Flyline width
             * @type {Number}
             * @default lineWidth = 1
             */
        lineWidth: 1,
        /**
             * @description Orbit color
             * @type {String}
             * @default orbitColor = 'rgba(103, 224, 227, .2)'
             */
        orbitColor: "rgba(103, 224, 227, .2)",
        /**
             * @description Flyline color
             * @type {String}
             * @default orbitColor = '#ffde93'
             */
        flylineColor: "#ffde93",
        /**
             * @description K value
             * @type {Number}
             * @default k = -0.5
             * @example k = -1 ~ 1
             */
        k: -0.5,
        /**
             * @description Flyline curvature
             * @type {Number}
             * @default curvature = 5
             */
        curvature: 5,
        /**
             * @description Flyline radius
             * @type {Number}
             * @default flylineRadius = 100
             */
        flylineRadius: 100,
        /**
             * @description Flyline animation duration
             * @type {Array<Number>}
             * @default duration = [20, 30]
             */
        duration: [20, 30],
        /**
             * @description Relative points position
             * @type {Boolean}
             * @default relative = true
             */
        relative: true,
        /**
             * @description Back ground image url
             * @type {String}
             * @default bgImgUrl = ''
             * @example bgImgUrl = './img/bg.jpg'
             */
        bgImgUrl: "",
        /**
             * @description Text configuration
             * @type {Object}
             */
        text: {
          /**
               * @description Text offset
               * @type {Array<Number>}
               * @default offset = [0, 15]
               */
          offset: [0, 15],
          /**
               * @description Text color
               * @type {String}
               * @default color = '#ffdb5c'
               */
          color: "#ffdb5c",
          /**
               * @description Text font size
               * @type {Number}
               * @default fontSize = 12
               */
          fontSize: 12
        },
        /**
             * @description Halo configuration
             * @type {Object}
             */
        halo: {
          /**
               * @description Weather to show halo
               * @type {Boolean}
               * @default show = true
               * @example show = true | false
               */
          show: true,
          /**
               * @description Halo animation duration (10 = 1s)
               * @type {Number}
               * @default duration = 30
               */
          duration: 30,
          /**
               * @description Halo color
               * @type {String}
               * @default color = '#fb7293'
               */
          color: "#fb7293",
          /**
               * @description Halo max radius
               * @type {Number}
               * @default radius = 120
               */
          radius: 120
        },
        /**
             * @description Center point img configuration
             * @type {Object}
             */
        centerPointImg: {
          /**
               * @description Center point img width
               * @type {Number}
               * @default width = 40
               */
          width: 40,
          /**
               * @description Center point img height
               * @type {Number}
               * @default height = 40
               */
          height: 40,
          /**
               * @description Center point img url
               * @type {String}
               * @default url = ''
               */
          url: ""
        },
        /**
             * @description Points img configuration
             * @type {Object}
             * @default radius = 120
             */
        pointsImg: {
          /**
               * @description Points img width
               * @type {Number}
               * @default width = 15
               */
          width: 15,
          /**
               * @description Points img height
               * @type {Number}
               * @default height = 15
               */
          height: 15,
          /**
               * @description Points img url
               * @type {String}
               * @default url = ''
               */
          url: ""
        }
      },
      mergedConfig: null,
      paths: [],
      lengths: [],
      times: [],
      texts: []
    });
    let s;
    onMounted(() => {
      s = getCurrentInstance();
    }), watch(() => t.config, () => {
      M();
    }, {
      deep: true
    });
    function D() {
      M();
    }
    function W() {
      M();
    }
    async function M() {
      U(), F(), await N(), I(), E();
    }
    function U() {
      const $ = Ce($e(l.defaultConfig, true), t.config || {}), { points: f } = $;
      $.points = f.map((_) => _ instanceof Array ? { position: _, text: "" } : _), l.mergedConfig = $;
    }
    function F() {
      let { centerPoint: $, points: f } = l.mergedConfig;
      const { relative: _ } = l.mergedConfig;
      f = f.map(({ position: O }) => O), _ && ($ = [a.value * $[0], o.value * $[1]], f = f.map(([O, m]) => [a.value * O, o.value * m])), l.paths = f.map((O) => A($, O));
    }
    function A($, f) {
      const _ = v($, f);
      return [f, _, $];
    }
    function v([$, f], [_, O]) {
      const { curvature: m, k: C } = l.mergedConfig, [d, G] = [($ + _) / 2, (f + O) / 2], g = Vt([$, f], [_, O]) / m, V = g / 2;
      let [X, Z] = [d, G];
      do
        X += V, Z = R(C, [d, G], X)[1];
      while (Vt([d, G], [X, Z]) < g);
      return [X, Z];
    }
    function R($, [f, _], O) {
      const m = _ - $ * f + $ * O;
      return [O, m];
    }
    async function N() {
      await nextTick(), l.lengths = l.paths.map(($, f) => s.proxy.$refs[`path${f}`][0].getTotalLength());
    }
    function I() {
      const { duration: $, points: f } = l.mergedConfig;
      l.times = f.map((_) => _t(...$) / 10);
    }
    function E() {
      const { points: $ } = l.mergedConfig;
      l.texts = $.map(({ text: f }) => f);
    }
    function b({ offsetX: $, offsetY: f }) {
      if (!t.dev)
        return;
      const _ = ($ / a.value).toFixed(2), O = (f / o.value).toFixed(2);
      console.warn(`dv-flyline-chart DEV: 
 Click Position is [${$}, ${f}] 
 Relative Position is [${_}, ${O}]`);
    }
    return ($, f) => (openBlock(), createElementBlock("div", {
      ref_key: "flylineChart",
      ref: n,
      class: "dv-flyline-chart",
      style: normalizeStyle(`background-image: url(${unref(l).mergedConfig ? unref(l).mergedConfig.bgImgUrl : ""})`),
      onClick: b
    }, [
      unref(l).mergedConfig ? (openBlock(), createElementBlock("svg", {
        key: 0,
        width: unref(a),
        height: unref(o)
      }, [
        createBaseVNode("defs", null, [
          createBaseVNode("radialGradient", {
            id: unref(l).gradientId,
            cx: "50%",
            cy: "50%",
            r: "50%"
          }, ks, 8, $s),
          createBaseVNode("radialGradient", {
            id: unref(l).gradient2Id,
            cx: "50%",
            cy: "50%",
            r: "50%"
          }, Os, 8, As),
          unref(l).paths[0] ? (openBlock(), createElementBlock("circle", {
            key: 0,
            id: `circle${unref(l).paths[0].toString()}`,
            cx: unref(l).paths[0][2][0],
            cy: unref(l).paths[0][2][1]
          }, [
            createBaseVNode("animate", {
              attributeName: "r",
              values: `1;${unref(l).mergedConfig.halo.radius}`,
              dur: unref(l).mergedConfig.halo.duration / 10 + "s",
              repeatCount: "indefinite"
            }, null, 8, Ms),
            createBaseVNode("animate", {
              attributeName: "opacity",
              values: "1;0",
              dur: unref(l).mergedConfig.halo.duration / 10 + "s",
              repeatCount: "indefinite"
            }, null, 8, Rs)
          ], 8, Gs)) : createCommentVNode("", true)
        ]),
        unref(l).paths[0] ? (openBlock(), createElementBlock("image", {
          key: 0,
          "xlink:href": unref(l).mergedConfig.centerPointImg.url,
          width: unref(l).mergedConfig.centerPointImg.width,
          height: unref(l).mergedConfig.centerPointImg.height,
          x: unref(l).paths[0][2][0] - unref(l).mergedConfig.centerPointImg.width / 2,
          y: unref(l).paths[0][2][1] - unref(l).mergedConfig.centerPointImg.height / 2
        }, null, 8, Ts)) : createCommentVNode("", true),
        createBaseVNode("mask", {
          id: `maskhalo${unref(l).paths[0].toString()}`
        }, [
          unref(l).paths[0] ? (openBlock(), createElementBlock("use", {
            key: 0,
            "xlink:href": `#circle${unref(l).paths[0].toString()}`,
            fill: `url(#${unref(l).gradient2Id})`
          }, null, 8, Bs)) : createCommentVNode("", true)
        ], 8, Ds),
        unref(l).paths[0] && unref(l).mergedConfig.halo.show ? (openBlock(), createElementBlock("use", {
          key: 1,
          "xlink:href": `#circle${unref(l).paths[0].toString()}`,
          fill: unref(l).mergedConfig.halo.color,
          mask: `url(#maskhalo${unref(l).paths[0].toString()})`
        }, null, 8, Fs)) : createCommentVNode("", true),
        (openBlock(true), createElementBlock(Fragment, null, renderList(unref(l).paths, (_, O) => (openBlock(), createElementBlock("g", { key: O }, [
          createBaseVNode("defs", null, [
            createBaseVNode("path", {
              id: `path${_.toString()}`,
              ref_for: true,
              ref: `path${O}`,
              d: `M${_[0].toString()} Q${_[1].toString()} ${_[2].toString()}`,
              fill: "transparent"
            }, null, 8, Ns)
          ]),
          createBaseVNode("use", {
            "xlink:href": `#path${_.toString()}`,
            "stroke-width": unref(l).mergedConfig.lineWidth,
            stroke: unref(l).mergedConfig.orbitColor
          }, null, 8, js),
          unref(l).lengths[O] ? (openBlock(), createElementBlock("use", {
            key: 0,
            "xlink:href": `#path${_.toString()}`,
            "stroke-width": unref(l).mergedConfig.lineWidth,
            stroke: unref(l).mergedConfig.flylineColor,
            mask: `url(#mask${unref(l).unique}${_.toString()})`
          }, [
            createBaseVNode("animate", {
              attributeName: "stroke-dasharray",
              from: `0, ${unref(l).lengths[O]}`,
              to: `${unref(l).lengths[O]}, 0`,
              dur: unref(l).times[O] || 0,
              repeatCount: "indefinite"
            }, null, 8, Ws)
          ], 8, Es)) : createCommentVNode("", true),
          createBaseVNode("mask", {
            id: `mask${unref(l).unique}${_.toString()}`
          }, [
            createBaseVNode("circle", {
              cx: "0",
              cy: "0",
              r: unref(l).mergedConfig.flylineRadius,
              fill: `url(#${unref(l).gradientId})`
            }, [
              createBaseVNode("animateMotion", {
                dur: unref(l).times[O] || 0,
                path: `M${_[0].toString()} Q${_[1].toString()} ${_[2].toString()}`,
                rotate: "auto",
                repeatCount: "indefinite"
              }, null, 8, Is)
            ], 8, qs)
          ], 8, zs),
          createBaseVNode("image", {
            "xlink:href": unref(l).mergedConfig.pointsImg.url,
            width: unref(l).mergedConfig.pointsImg.width,
            height: unref(l).mergedConfig.pointsImg.height,
            x: _[0][0] - unref(l).mergedConfig.pointsImg.width / 2,
            y: _[0][1] - unref(l).mergedConfig.pointsImg.height / 2
          }, null, 8, Hs),
          createBaseVNode("text", {
            style: normalizeStyle(`fontSize:${unref(l).mergedConfig.text.fontSize}px;`),
            fill: unref(l).mergedConfig.text.color,
            x: _[0][0] + unref(l).mergedConfig.text.offset[0],
            y: _[0][1] + unref(l).mergedConfig.text.offset[1]
          }, toDisplayString(unref(l).texts[O]), 13, Vs)
        ]))), 128))
      ], 8, _s)) : createCommentVNode("", true)
    ], 4));
  }
};
var Nr = {
  install(e) {
    e.component("DvFlylineChart", Us);
  }
};
var Xs = (e) => (pushScopeId("data-v-282cb432"), e = e(), popScopeId(), e);
var Qs = { class: "ranking-info" };
var Ys = { class: "rank" };
var Ks = ["innerHTML"];
var Js = { class: "ranking-value" };
var Zs = { class: "ranking-column" };
var eu = Xs(() => createBaseVNode("div", { class: "shine" }, null, -1));
var tu = [
  eu
];
var ru = {
  __name: "index",
  props: {
    config: {
      type: Object,
      default: () => ({})
    }
  },
  setup(e) {
    const t = e;
    useCssVars((b) => ({
      "2801d2f6": unref(s),
      "5c86b458": unref(l),
      "6524ce8e": unref(W),
      "65ae9c69": unref(D)
    }));
    const r = ref(null), { width: n, height: a } = xe(r, U, M), o = reactive({
      defaultConfig: {
        /**
             * @description Board data
             * @type {Array<Object>}
             * @default data = []
             */
        data: [],
        /**
             * @description Row num
             * @type {Number}
             * @default rowNum = 5
             */
        rowNum: 5,
        /**
             * @description Scroll wait time
             * @type {Number}
             * @default waitTime = 2000
             */
        waitTime: 2e3,
        /**
             * @description Carousel type
             * @type {String}
             * @default carousel = 'single'
             * @example carousel = 'single' | 'page'
             */
        carousel: "single",
        /**
             * @description Value unit
             * @type {String}
             * @default unit = ''
             * @example unit = 'ton'
             */
        unit: "",
        /**
             * @description Auto sort by value
             * @type {Boolean}
             * @default sort = true
             */
        sort: true,
        /**
             * @description Value formatter
             * @type {Function}
             * @default valueFormatter = null
             */
        valueFormatter: null,
        /**
             * @description Text color
             * @type {String}
             * @default textColor = '#fff'
             */
        textColor: "#fff",
        /**
             * @description Main theme color
             * @type {String}
             * @default color = '#1370fb'
             */
        color: "#1370fb",
        /**
             * @description Font size
             * @type {Number}
             * @default fontSize = 13
             */
        fontSize: 13
      },
      mergedConfig: null,
      rowsData: [],
      rows: [],
      heights: [],
      avgHeight: 0,
      animationIndex: 0,
      animationHandler: "",
      updater: 0
    });
    watch(() => t.config, () => {
      E(), F();
    }, {
      deep: true
    });
    const l = computed(() => t.config.textColor ? t.config.textColor : o.defaultConfig.textColor), s = computed(() => t.config.color ? t.config.color : o.defaultConfig.color), D = computed(() => De(s.value, 50)), W = computed(() => `${t.config.fontSize ? t.config.fontSize : o.defaultConfig.fontSize}px`);
    onUnmounted(() => {
      E();
    });
    function M() {
      F();
    }
    function U() {
      o.mergedConfig && R(true);
    }
    function F() {
      A(), v(), R(), I(true);
    }
    function A() {
      o.mergedConfig = Ce($e(o.defaultConfig, true), t.config || {});
    }
    function v() {
      let { data: b } = o.mergedConfig;
      const { rowNum: $, sort: f } = o.mergedConfig;
      f && b.sort(({ value: L }, { value: g }) => L > g ? -1 : L < g ? 1 : 0);
      const _ = b.map(({ value: L }) => L), O = Math.min(..._) || 0, m = Math.abs(O), d = (Math.max(..._) || 0) + m;
      b = b.map((L, g) => ({ ...L, ranking: g + 1, percent: (L.value + m) / d * 100 }));
      const G = b.length;
      G > $ && G < 2 * $ && (b = [...b, ...b]), b = b.map((L, g) => ({ ...L, scroll: g })), o.rowsData = b, o.rows = b;
    }
    function R(b = false) {
      const { rowNum: $, data: f } = o.mergedConfig, _ = a.value / $;
      o.avgHeight = _, b || (o.heights = new Array(f.length).fill(_));
    }
    const N = computed(() => o.mergedConfig.carousel === "single");
    async function I(b = false) {
      const { waitTime: $, rowNum: f } = o.mergedConfig, _ = o.rowsData.length;
      if (f >= _)
        return;
      const { updater: O } = o;
      if (b && (await new Promise((G) => setTimeout(G, $)), O !== o.updater))
        return;
      const m = N.value ? 1 : f, C = o.rowsData.slice(o.animationIndex);
      if (C.push(...o.rowsData.slice(0, o.animationIndex)), o.rows = C.slice(0, N.value ? f + 1 : f * 2), o.heights = new Array(_).fill(o.avgHeight), await new Promise((G) => setTimeout(G, 300)), O !== o.updater)
        return;
      o.heights.fill(0, 0, m), o.animationIndex += m;
      const d = o.animationIndex - _;
      d >= 0 && (o.animationIndex = d), o.animationHandler = setTimeout(I, $ - 300);
    }
    function E() {
      o.updater = (o.updater + 1) % 999999, o.animationHandler && clearTimeout(o.animationHandler);
    }
    return (b, $) => (openBlock(), createElementBlock("div", {
      ref_key: "scrollRankingBoard",
      ref: r,
      class: "dv-scroll-ranking-board"
    }, [
      (openBlock(true), createElementBlock(Fragment, null, renderList(unref(o).rows, (f, _) => (openBlock(), createElementBlock("div", {
        key: f.toString() + f.scroll,
        class: "row-item",
        style: normalizeStyle(`height: ${unref(o).heights[_]}px;`)
      }, [
        createBaseVNode("div", Qs, [
          createBaseVNode("div", Ys, " No." + toDisplayString(f.ranking), 1),
          createBaseVNode("div", {
            class: "info-name",
            innerHTML: f.name
          }, null, 8, Ks),
          createBaseVNode("div", Js, toDisplayString(unref(o).mergedConfig.valueFormatter ? unref(o).mergedConfig.valueFormatter(f) : f.value + unref(o).mergedConfig.unit), 1)
        ]),
        createBaseVNode("div", Zs, [
          createBaseVNode("div", {
            class: "inside-column",
            style: normalizeStyle(`width: ${f.percent}%;`)
          }, tu, 4)
        ])
      ], 4))), 128))
    ], 512));
  }
};
var nu = Ze(ru, [["__scopeId", "data-v-282cb432"]]);
var jr = {
  install(e) {
    e.component("DvScrollRankingBoard", nu);
  }
};
var au = ["align", "innerHTML"];
var iu = ["align", "onClick", "onMouseenter", "innerHTML"];
var ou = {
  __name: "index",
  props: {
    config: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ["mouseover", "click", "getFirstRow"],
  setup(e, { expose: t, emit: r }) {
    const n = e, a = ref(null), { width: o, height: l } = xe(a, U, M), s = reactive({
      defaultConfig: {
        /**
             * @description Board header
             * @type {Array<String>}
             * @default header = []
             * @example header = ['column1', 'column2', 'column3']
             */
        header: [],
        /**
             * @description Board data
             * @type {Array<Array>}
             * @default data = []
             */
        data: [],
        /**
             * @description Row num
             * @type {Number}
             * @default rowNum = 5
             */
        rowNum: 5,
        /**
             * @description Header background color
             * @type {String}
             * @default headerBGC = '#00BAFF'
             */
        headerBGC: "#00BAFF",
        /**
             * @description Odd row background color
             * @type {String}
             * @default oddRowBGC = '#003B51'
             */
        oddRowBGC: "#003B51",
        /**
             * @description Even row background color
             * @type {String}
             * @default evenRowBGC = '#003B51'
             */
        evenRowBGC: "#0A2732",
        /**
             * @description Scroll wait time
             * @type {Number}
             * @default waitTime = 2000
             */
        waitTime: 2e3,
        /**
             * @description Header height
             * @type {Number}
             * @default headerHeight = 35
             */
        headerHeight: 35,
        /**
             * @description Column width
             * @type {Array<Number>}
             * @default columnWidth = []
             */
        columnWidth: [],
        /**
             * @description Column align
             * @type {Array<String>}
             * @default align = []
             * @example align = ['left', 'center', 'right']
             */
        align: [],
        /**
             * @description Show index
             * @type {Boolean}
             * @default index = false
             */
        index: false,
        /**
             * @description index Header
             * @type {String}
             * @default indexHeader = '#'
             */
        indexHeader: "#",
        /**
             * @description Carousel type
             * @type {String}
             * @default carousel = 'single'
             * @example carousel = 'single' | 'page'
             */
        carousel: "single",
        /**
             * @description Pause scroll when mouse hovered
             * @type {Boolean}
             * @default hoverPause = true
             * @example hoverPause = true | false
             */
        hoverPause: true
      },
      mergedConfig: null,
      header: [],
      rowsData: [],
      rows: [],
      widths: [],
      heights: [],
      avgHeight: 0,
      aligns: [],
      animationIndex: 0,
      animationHandler: "",
      updater: 0,
      needCalc: false
    });
    watch(() => n.config, (_) => {
      $(), F();
    }, { deep: true }), onUnmounted(() => {
      $();
    }), t({
      updateRows: f
    });
    function D(_, O, m, C) {
      const { ceils: d, rowIndex: G } = m;
      r("click", {
        row: d,
        ceil: C,
        rowIndex: G,
        columnIndex: O
      });
    }
    function W(_, O, m, C, d) {
      if (_) {
        const { ceils: G, rowIndex: L } = C;
        r("mouseover", {
          row: G,
          ceil: d,
          rowIndex: L,
          columnIndex: m
        });
      }
      s.mergedConfig.hoverPause && (_ ? $() : b(true));
    }
    function M() {
      F();
    }
    function U() {
      s.mergedConfig && (N(), I());
    }
    function F() {
      A(), v(), R(), N(), I(), E(), b(true);
    }
    function A() {
      s.mergedConfig = Ce($e(s.defaultConfig, true), n.config || {});
    }
    function v() {
      let { header: _ } = s.mergedConfig;
      const { index: O, indexHeader: m } = s.mergedConfig;
      if (!_.length) {
        _ = [];
        return;
      }
      _ = [..._], O && _.unshift(m), s.header = _;
    }
    function R() {
      let { data: _ } = s.mergedConfig;
      const { index: O, headerBGC: m, rowNum: C } = s.mergedConfig;
      O && (_ = _.map((G, L) => {
        G = [...G];
        const g = `<span class="index" style="background-color: ${m};">${L + 1}</span>`;
        return G.unshift(g), G;
      })), _ = _.map((G, L) => ({ ceils: G, rowIndex: L }));
      const d = _.length;
      d > C && d < 2 * C && (_ = [..._, ..._]), _ = _.map((G, L) => ({ ...G, scroll: L })), s.rowsData = _, s.rows = _;
    }
    function N() {
      const { columnWidth: _, header: O } = s.mergedConfig, m = _.reduce((L, g) => L + g, 0);
      let C = 0;
      s.rowsData[0] ? C = s.rowsData[0].ceils.length : O.length && (C = O.length);
      const d = (o.value - m) / (C - _.length), G = new Array(C).fill(d);
      s.widths = Ce(G, _);
    }
    function I(_ = false) {
      const { headerHeight: O, rowNum: m, data: C } = s.mergedConfig;
      let d = l.value;
      s.header.length && (d -= O);
      const G = d / m;
      s.avgHeight = G, _ || (s.heights = new Array(C.length).fill(G));
    }
    function E() {
      const _ = s.header.length, O = new Array(_).fill("left"), { align: m } = s.mergedConfig;
      s.aligns = Ce(O, m);
    }
    async function b(_ = false) {
      s.needCalc && (R(), I(), s.needCalc = false);
      const { waitTime: O, carousel: m, rowNum: C } = s.mergedConfig, { updater: d } = s, G = s.rowsData.length;
      if (C >= G || (_ && await new Promise((X) => setTimeout(X, O)), d !== s.updater))
        return;
      const L = m === "single" ? 1 : C, g = s.rowsData.slice(s.animationIndex);
      if (g.push(...s.rowsData.slice(0, s.animationIndex)), s.rows = g.slice(0, m === "page" ? C * 2 : C + 1), s.heights = new Array(G).fill(s.avgHeight), await new Promise((X) => setTimeout(X, 300)), d !== s.updater)
        return;
      s.heights.splice(0, L, ...new Array(L).fill(0)), s.animationIndex += L;
      const V = s.animationIndex - G;
      V >= 0 && (s.animationIndex = V), s.animationHandler = setTimeout(b, O - 300), r("getFirstRow", g[1]);
    }
    function $() {
      s.updater = (s.updater + 1) % 999999, s.animationHandler && clearTimeout(s.animationHandler);
    }
    function f(_, O) {
      s.mergedConfig = {
        ...s.mergedConfig,
        data: [..._]
      }, s.needCalc = true, typeof O == "number" && (s.animationIndex = O), s.animationHandler || b(true);
    }
    return (_, O) => (openBlock(), createElementBlock("div", {
      ref_key: "scrollBoard",
      ref: a,
      class: "dv-scroll-board"
    }, [
      unref(s).header.length && unref(s).mergedConfig ? (openBlock(), createElementBlock("div", {
        key: 0,
        class: "header",
        style: normalizeStyle(`background-color: ${unref(s).mergedConfig.headerBGC};`)
      }, [
        (openBlock(true), createElementBlock(Fragment, null, renderList(unref(s).header, (m, C) => (openBlock(), createElementBlock("div", {
          key: `${m}${C}`,
          class: "header-item",
          style: normalizeStyle(`
          height: ${unref(s).mergedConfig.headerHeight}px;
          line-height: ${unref(s).mergedConfig.headerHeight}px;
          width: ${unref(s).widths[C]}px;
        `),
          align: unref(s).aligns[C],
          innerHTML: m
        }, null, 12, au))), 128))
      ], 4)) : createCommentVNode("", true),
      unref(s).mergedConfig ? (openBlock(), createElementBlock("div", {
        key: 1,
        class: "rows",
        style: normalizeStyle(`height: ${unref(l) - (unref(s).header.length ? unref(s).mergedConfig.headerHeight : 0)}px;`)
      }, [
        (openBlock(true), createElementBlock(Fragment, null, renderList(unref(s).rows, (m, C) => (openBlock(), createElementBlock("div", {
          key: `${m.toString()}${m.scroll}`,
          class: "row-item",
          style: normalizeStyle(`
          height: ${unref(s).heights[C]}px;
          line-height: ${unref(s).heights[C]}px;
          background-color: ${unref(s).mergedConfig[m.rowIndex % 2 === 0 ? "evenRowBGC" : "oddRowBGC"]};
        `)
        }, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(m.ceils, (d, G) => (openBlock(), createElementBlock("div", {
            key: `${d}${C}${G}`,
            class: "ceil",
            style: normalizeStyle(`width: ${unref(s).widths[G]}px;`),
            align: unref(s).aligns[G],
            onClick: (L) => D(C, G, m, d),
            onMouseenter: (L) => W(true, C, G, m, d),
            onMouseleave: O[0] || (O[0] = (L) => W(false)),
            innerHTML: d
          }, null, 44, iu))), 128))
        ], 4))), 128))
      ], 4)) : createCommentVNode("", true)
    ], 512));
  }
};
var Er = {
  install(e) {
    e.component("DvScrollBoard", ou);
  }
};
var pi = {};
var Wr = {};
var je = {};
var ha;
function qe() {
  if (ha)
    return je;
  ha = 1;
  var e = we;
  Object.defineProperty(je, "__esModule", {
    value: true
  }), je.filterNonNumber = a, je.deepMerge = o, je.mulAdd = l, je.mergeSameStackData = s, je.getTwoPointDistance = D, je.getLinearGradientColor = W, je.getPolylineLength = M, je.getPointToLineDistance = U, je.initNeedSeries = F, je.radianToAngle = A;
  var t = e(Be()), r = e(ze()), n = Fe();
  function a(v) {
    return v.filter(function(R) {
      return typeof R == "number";
    });
  }
  function o(v, R) {
    for (var N in R) {
      if (v[N] && (0, r.default)(v[N]) === "object") {
        o(v[N], R[N]);
        continue;
      }
      if ((0, r.default)(R[N]) === "object") {
        v[N] = (0, n.deepClone)(R[N], true);
        continue;
      }
      v[N] = R[N];
    }
    return v;
  }
  function l(v) {
    return v = a(v), v.reduce(function(R, N) {
      return R + N;
    }, 0);
  }
  function s(v, R) {
    var N = v.stack;
    if (!N)
      return (0, t.default)(v.data);
    var I = R.filter(function(f) {
      var _ = f.stack;
      return _ === N;
    }), E = I.findIndex(function(f) {
      var _ = f.data;
      return _ === v.data;
    }), b = I.splice(0, E + 1).map(function(f) {
      var _ = f.data;
      return _;
    }), $ = b[0].length;
    return new Array($).fill(0).map(function(f, _) {
      return l(b.map(function(O) {
        return O[_];
      }));
    });
  }
  function D(v, R) {
    var N = Math.abs(v[0] - R[0]), I = Math.abs(v[1] - R[1]);
    return Math.sqrt(N * N + I * I);
  }
  function W(v, R, N, I) {
    if (!(!v || !R || !N || !I.length)) {
      var E = I;
      typeof E == "string" && (E = [I, I]);
      var b = v.createLinearGradient.apply(v, (0, t.default)(R).concat((0, t.default)(N))), $ = 1 / (E.length - 1);
      return E.forEach(function(f, _) {
        return b.addColorStop($ * _, f);
      }), b;
    }
  }
  function M(v) {
    var R = new Array(v.length - 1).fill(0).map(function(I, E) {
      return [v[E], v[E + 1]];
    }), N = R.map(function(I) {
      return D.apply(void 0, (0, t.default)(I));
    });
    return l(N);
  }
  function U(v, R, N) {
    var I = D(v, R), E = D(v, N), b = D(R, N);
    return 0.5 * Math.sqrt((I + E + b) * (I + E - b) * (I + b - E) * (E + b - I)) / b;
  }
  function F(v, R, N) {
    return v = v.filter(function(I) {
      var E = I.type;
      return E === N;
    }), v = v.map(function(I) {
      return o((0, n.deepClone)(R, true), I);
    }), v.filter(function(I) {
      var E = I.show;
      return E;
    });
  }
  function A(v) {
    return v / Math.PI * 180;
  }
  return je;
}
var gi = we;
var lu = gi(Ue());
var va = gi(Be());
var er = Zt;
var su = Mn();
var $t = Fe();
var uu = St;
var cu = qe();
function pa(e, t) {
  var r = Object.keys(e);
  if (Object.getOwnPropertySymbols) {
    var n = Object.getOwnPropertySymbols(e);
    t && (n = n.filter(function(a) {
      return Object.getOwnPropertyDescriptor(e, a).enumerable;
    })), r.push.apply(r, n);
  }
  return r;
}
function ga(e) {
  for (var t = 1; t < arguments.length; t++) {
    var r = arguments[t] != null ? arguments[t] : {};
    t % 2 ? pa(Object(r), true).forEach(function(n) {
      (0, lu.default)(e, n, r[n]);
    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(r)) : pa(Object(r)).forEach(function(n) {
      Object.defineProperty(e, n, Object.getOwnPropertyDescriptor(r, n));
    });
  }
  return e;
}
var fu = {
  shape: {
    rx: 0,
    ry: 0,
    ir: 0,
    or: 0,
    startAngle: 0,
    endAngle: 0,
    clockWise: true
  },
  validator: function(t) {
    var r = t.shape, n = ["rx", "ry", "ir", "or", "startAngle", "endAngle"];
    return n.find(function(a) {
      return typeof r[a] != "number";
    }) ? (console.error("Pie shape configuration is abnormal!"), false) : true;
  },
  draw: function(t, r) {
    var n = t.ctx, a = r.shape;
    n.beginPath();
    var o = a.rx, l = a.ry, s = a.ir, D = a.or, W = a.startAngle, M = a.endAngle, U = a.clockWise;
    o = parseInt(o) + 0.5, l = parseInt(l) + 0.5, n.arc(o, l, s > 0 ? s : 0, W, M, !U);
    var F = (0, $t.getCircleRadianPoint)(o, l, D, M).map(function(v) {
      return parseInt(v) + 0.5;
    }), A = (0, $t.getCircleRadianPoint)(o, l, s, W).map(function(v) {
      return parseInt(v) + 0.5;
    });
    n.lineTo.apply(n, (0, va.default)(F)), n.arc(o, l, D > 0 ? D : 0, M, W, U), n.lineTo.apply(n, (0, va.default)(A)), n.closePath(), n.stroke(), n.fill();
  }
};
var du = {
  shape: {
    rx: 0,
    ry: 0,
    r: 0,
    startAngle: 0,
    endAngle: 0,
    gradientStartAngle: null,
    gradientEndAngle: null
  },
  validator: function(t) {
    var r = t.shape, n = ["rx", "ry", "r", "startAngle", "endAngle"];
    return n.find(function(a) {
      return typeof r[a] != "number";
    }) ? (console.error("AgArc shape configuration is abnormal!"), false) : true;
  },
  draw: function(t, r) {
    var n = t.ctx, a = r.shape, o = r.style, l = o.gradient;
    l = l.map(function(O) {
      return (0, uu.getColorFromRgbValue)(O);
    }), l.length === 1 && (l = [l[0], l[0]]);
    var s = l.length - 1, D = a.gradientStartAngle, W = a.gradientEndAngle, M = a.startAngle, U = a.endAngle, F = a.r, A = a.rx, v = a.ry;
    D === null && (D = M), W === null && (W = U);
    var R = (W - D) / s;
    R === Math.PI * 2 && (R = Math.PI * 2 - 1e-3);
    for (var N = 0; N < s; N++) {
      n.beginPath();
      var I = (0, $t.getCircleRadianPoint)(A, v, F, M + R * N), E = (0, $t.getCircleRadianPoint)(A, v, F, M + R * (N + 1)), b = (0, cu.getLinearGradientColor)(n, I, E, [l[N], l[N + 1]]), $ = M + R * N, f = M + R * (N + 1), _ = false;
      if (f > U && (f = U, _ = true), n.arc(A, v, F, $, f), n.strokeStyle = b, n.stroke(), _)
        break;
    }
  }
};
var hu = {
  shape: {
    number: [],
    content: "",
    position: [0, 0],
    toFixed: 0,
    rowGap: 0,
    formatter: null
  },
  validator: function(t) {
    var r = t.shape, n = r.number, a = r.content, o = r.position;
    return !(n instanceof Array) || typeof a != "string" || !(o instanceof Array) ? (console.error("NumberText shape configuration is abnormal!"), false) : true;
  },
  draw: function(t, r) {
    var n = t.ctx, a = r.shape, o = a.number, l = a.content, s = a.toFixed, D = a.rowGap, W = a.formatter, M = l.split("{nt}"), U = "";
    M.forEach(function(F, A) {
      var v = o[A];
      typeof v != "number" && (v = ""), typeof v == "number" && (v = v.toFixed(s), typeof W == "function" && (v = W(v))), U += F + (v || "");
    }), su.text.draw({
      ctx: n
    }, {
      shape: ga(ga({}, a), {}, {
        content: U,
        rowGap: D
      })
    });
  }
};
var vu = {
  shape: {
    x: 0,
    y: 0,
    w: 0,
    h: 0
  },
  validator: function(t) {
    var r = t.shape, n = r.x, a = r.y, o = r.w, l = r.h;
    return typeof n != "number" || typeof a != "number" || typeof o != "number" || typeof l != "number" ? (console.error("lineIcon shape configuration is abnormal!"), false) : true;
  },
  draw: function(t, r) {
    var n = t.ctx, a = r.shape;
    n.beginPath();
    var o = a.x, l = a.y, s = a.w, D = a.h, W = D / 2;
    n.strokeStyle = n.fillStyle, n.moveTo(o, l + W), n.lineTo(o + s, l + W), n.lineWidth = 1, n.stroke(), n.beginPath();
    var M = W - 5 * 2;
    M <= 0 && (M = 3), n.arc(o + s / 2, l + W, M, 0, Math.PI * 2), n.lineWidth = 5, n.stroke(), n.fillStyle = "#fff", n.fill();
  },
  hoverCheck: function(t, r) {
    var n = r.shape, a = n.x, o = n.y, l = n.w, s = n.h;
    return (0, $t.checkPointIsInRect)(t, a, o, l, s);
  },
  setGraphCenter: function(t, r) {
    var n = r.shape, a = r.style, o = n.x, l = n.y, s = n.w, D = n.h;
    a.graphCenter = [o + s / 2, l + D / 2];
  }
};
(0, er.extendNewGraph)("pie", fu);
(0, er.extendNewGraph)("agArc", du);
(0, er.extendNewGraph)("numberText", hu);
(0, er.extendNewGraph)("lineIcon", vu);
var zr = {};
var Rt = {};
var qr = {};
var ut = {};
var ma;
function pu() {
  if (ma)
    return ut;
  ma = 1, Object.defineProperty(ut, "__esModule", {
    value: true
  }), ut.colorConfig = void 0;
  var e = ["#37a2da", "#32c5e9", "#67e0e3", "#9fe6b8", "#ffdb5c", "#ff9f7f", "#fb7293", "#e062ae", "#e690d1", "#e7bcf3", "#9d96f5", "#8378ea", "#96bfff"];
  return ut.colorConfig = e, ut;
}
var ct = {};
var ya;
function gu() {
  if (ya)
    return ct;
  ya = 1, Object.defineProperty(ct, "__esModule", {
    value: true
  }), ct.gridConfig = void 0;
  var e = {
    /**
     * @description Grid left margin
     * @type {String|Number}
     * @default left = '10%'
     * @example left = '10%' | 10
     */
    left: "10%",
    /**
     * @description Grid right margin
     * @type {String|Number}
     * @default right = '10%'
     * @example right = '10%' | 10
     */
    right: "10%",
    /**
     * @description Grid top margin
     * @type {String|Number}
     * @default top = 60
     * @example top = '10%' | 60
     */
    top: 60,
    /**
     * @description Grid bottom margin
     * @type {String|Number}
     * @default bottom = 60
     * @example bottom = '10%' | 60
     */
    bottom: 60,
    /**
     * @description Grid default style configuration
     * @type {Object}
     * @default style = {Configuration Of Class Style}
     */
    style: {
      fill: "rgba(0, 0, 0, 0)"
    },
    /**
     * @description Grid render level
     * Priority rendering high level
     * @type {Number}
     * @default rLevel = -30
     */
    rLevel: -30,
    /**
     * @description Grid animation curve
     * @type {String}
     * @default animationCurve = 'easeOutCubic'
     */
    animationCurve: "easeOutCubic",
    /**
     * @description Grid animation frame
     * @type {Number}
     * @default animationFrame = 50
     */
    animationFrame: 30
  };
  return ct.gridConfig = e, ct;
}
var tt = {};
var ba;
function mu() {
  if (ba)
    return tt;
  ba = 1, Object.defineProperty(tt, "__esModule", {
    value: true
  }), tt.yAxisConfig = tt.xAxisConfig = void 0;
  var e = {
    /**
     * @description Axis name
     * @type {String}
     * @default name = ''
     */
    name: "",
    /**
     * @description Whether to display this axis
     * @type {Boolean}
     * @default show = true
     */
    show: true,
    /**
     * @description Axis position
     * @type {String}
     * @default position = 'bottom'
     * @example position = 'bottom' | 'top'
     */
    position: "bottom",
    /**
     * @description Name gap
     * @type {Number}
     * @default nameGap = 15
     */
    nameGap: 15,
    /**
     * @description Name location
     * @type {String}
     * @default nameLocation = 'end'
     * @example nameLocation = 'end' | 'center' | 'start'
     */
    nameLocation: "end",
    /**
     * @description Name default style configuration
     * @type {Object}
     * @default style = {Configuration Of Class Style}
     */
    nameTextStyle: {
      fill: "#333",
      fontSize: 10
    },
    /**
     * @description Axis min value
     * @type {String|Number}
     * @default min = '20%'
     * @example min = '20%' | 0
     */
    min: "20%",
    /**
     * @description Axis max value
     * @type {String|Number}
     * @default max = '20%'
     * @example max = '20%' | 0
     */
    max: "20%",
    /**
     * @description Axis value interval
     * @type {Number}
     * @default interval = null
     * @example interval = 100
     */
    interval: null,
    /**
     * @description Min interval
     * @type {Number}
     * @default minInterval = null
     * @example minInterval = 1
     */
    minInterval: null,
    /**
     * @description Max interval
     * @type {Number}
     * @default maxInterval = null
     * @example maxInterval = 100
     */
    maxInterval: null,
    /**
     * @description Boundary gap
     * @type {Boolean}
     * @default boundaryGap = null
     * @example boundaryGap = true
     */
    boundaryGap: null,
    /**
     * @description Axis split number
     * @type {Number}
     * @default splitNumber = 5
     */
    splitNumber: 5,
    /**
     * @description Axis line configuration
     * @type {Object}
     */
    axisLine: {
      /**
       * @description Whether to display axis line
       * @type {Boolean}
       * @default show = true
       */
      show: true,
      /**
       * @description Axis line default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        stroke: "#333",
        lineWidth: 1
      }
    },
    /**
     * @description Axis tick configuration
     * @type {Object}
     */
    axisTick: {
      /**
       * @description Whether to display axis tick
       * @type {Boolean}
       * @default show = true
       */
      show: true,
      /**
       * @description Axis tick default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        stroke: "#333",
        lineWidth: 1
      }
    },
    /**
     * @description Axis label configuration
     * @type {Object}
     */
    axisLabel: {
      /**
       * @description Whether to display axis label
       * @type {Boolean}
       * @default show = true
       */
      show: true,
      /**
       * @description Axis label formatter
       * @type {String|Function}
       * @default formatter = null
       * @example formatter = '{value}件'
       * @example formatter = (dataItem) => (dataItem.value)
       */
      formatter: null,
      /**
       * @description Axis label default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        fill: "#333",
        fontSize: 10,
        rotate: 0
      }
    },
    /**
     * @description Axis split line configuration
     * @type {Object}
     */
    splitLine: {
      /**
       * @description Whether to display axis split line
       * @type {Boolean}
       * @default show = false
       */
      show: false,
      /**
       * @description Axis split line default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        stroke: "#d4d4d4",
        lineWidth: 1
      }
    },
    /**
     * @description X axis render level
     * Priority rendering high level
     * @type {Number}
     * @default rLevel = -20
     */
    rLevel: -20,
    /**
     * @description X axis animation curve
     * @type {String}
     * @default animationCurve = 'easeOutCubic'
     */
    animationCurve: "easeOutCubic",
    /**
     * @description X axis animation frame
     * @type {Number}
     * @default animationFrame = 50
     */
    animationFrame: 50
  };
  tt.xAxisConfig = e;
  var t = {
    /**
     * @description Axis name
     * @type {String}
     * @default name = ''
     */
    name: "",
    /**
     * @description Whether to display this axis
     * @type {Boolean}
     * @default show = true
     */
    show: true,
    /**
     * @description Axis position
     * @type {String}
     * @default position = 'left'
     * @example position = 'left' | 'right'
     */
    position: "left",
    /**
     * @description Name gap
     * @type {Number}
     * @default nameGap = 15
     */
    nameGap: 15,
    /**
     * @description Name location
     * @type {String}
     * @default nameLocation = 'end'
     * @example nameLocation = 'end' | 'center' | 'start'
     */
    nameLocation: "end",
    /**
     * @description name default style configuration
     * @type {Object}
     * @default style = {Configuration Of Class Style}
     */
    nameTextStyle: {
      fill: "#333",
      fontSize: 10
    },
    /**
     * @description Axis min value
     * @type {String|Number}
     * @default min = '20%'
     * @example min = '20%' | 0
     */
    min: "20%",
    /**
     * @description Axis max value
     * @type {String|Number}
     * @default max = '20%'
     * @example max = '20%' | 0
     */
    max: "20%",
    /**
     * @description Axis value interval
     * @type {Number}
     * @default interval = null
     * @example interval = 100
     */
    interval: null,
    /**
     * @description Min interval
     * @type {Number}
     * @default minInterval = null
     * @example minInterval = 1
     */
    minInterval: null,
    /**
     * @description Max interval
     * @type {Number}
     * @default maxInterval = null
     * @example maxInterval = 100
     */
    maxInterval: null,
    /**
     * @description Boundary gap
     * @type {Boolean}
     * @default boundaryGap = null
     * @example boundaryGap = true
     */
    boundaryGap: null,
    /**
     * @description Axis split number
     * @type {Number}
     * @default splitNumber = 5
     */
    splitNumber: 5,
    /**
     * @description Axis line configuration
     * @type {Object}
     */
    axisLine: {
      /**
       * @description Whether to display axis line
       * @type {Boolean}
       * @default show = true
       */
      show: true,
      /**
       * @description Axis line default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        stroke: "#333",
        lineWidth: 1
      }
    },
    /**
     * @description Axis tick configuration
     * @type {Object}
     */
    axisTick: {
      /**
       * @description Whether to display axis tick
       * @type {Boolean}
       * @default show = true
       */
      show: true,
      /**
       * @description Axis tick default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        stroke: "#333",
        lineWidth: 1
      }
    },
    /**
     * @description Axis label configuration
     * @type {Object}
     */
    axisLabel: {
      /**
       * @description Whether to display axis label
       * @type {Boolean}
       * @default show = true
       */
      show: true,
      /**
       * @description Axis label formatter
       * @type {String|Function}
       * @default formatter = null
       * @example formatter = '{value}件'
       * @example formatter = (dataItem) => (dataItem.value)
       */
      formatter: null,
      /**
       * @description Axis label default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        fill: "#333",
        fontSize: 10,
        rotate: 0
      }
    },
    /**
     * @description Axis split line configuration
     * @type {Object}
     */
    splitLine: {
      /**
       * @description Whether to display axis split line
       * @type {Boolean}
       * @default show = true
       */
      show: true,
      /**
       * @description Axis split line default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        stroke: "#d4d4d4",
        lineWidth: 1
      }
    },
    /**
     * @description Y axis render level
     * Priority rendering high level
     * @type {Number}
     * @default rLevel = -20
     */
    rLevel: -20,
    /**
     * @description Y axis animation curve
     * @type {String}
     * @default animationCurve = 'easeOutCubic'
     */
    animationCurve: "easeOutCubic",
    /**
     * @description Y axis animation frame
     * @type {Number}
     * @default animationFrame = 50
     */
    animationFrame: 50
  };
  return tt.yAxisConfig = t, tt;
}
var ft = {};
var xa;
function yu() {
  if (xa)
    return ft;
  xa = 1, Object.defineProperty(ft, "__esModule", {
    value: true
  }), ft.titleConfig = void 0;
  var e = {
    /**
     * @description Whether to display title
     * @type {Boolean}
     * @default show = true
     */
    show: true,
    /**
     * @description Title text
     * @type {String}
     * @default text = ''
     */
    text: "",
    /**
     * @description Title offset
     * @type {Array}
     * @default offset = [0, -20]
     */
    offset: [0, -20],
    /**
     * @description Title default style configuration
     * @type {Object}
     * @default style = {Configuration Of Class Style}
     */
    style: {
      fill: "#333",
      fontSize: 17,
      fontWeight: "bold",
      textAlign: "center",
      textBaseline: "bottom"
    },
    /**
     * @description Title render level
     * Priority rendering high level
     * @type {Number}
     * @default rLevel = 20
     */
    rLevel: 20,
    /**
     * @description Title animation curve
     * @type {String}
     * @default animationCurve = 'easeOutCubic'
     */
    animationCurve: "easeOutCubic",
    /**
     * @description Title animation frame
     * @type {Number}
     * @default animationFrame = 50
     */
    animationFrame: 50
  };
  return ft.titleConfig = e, ft;
}
var dt = {};
var Ca;
function bu() {
  if (Ca)
    return dt;
  Ca = 1, Object.defineProperty(dt, "__esModule", {
    value: true
  }), dt.lineConfig = void 0;
  var e = {
    /**
     * @description Whether to display this line chart
     * @type {Boolean}
     * @default show = true
     */
    show: true,
    /**
     * @description Legend name
     * @type {String}
     * @default name = ''
     */
    name: "",
    /**
     * @description Data stacking
     * The data value of the series element of the same stack
     * will be superimposed (the latter value will be superimposed on the previous value)
     * @type {String}
     * @default stack = ''
     */
    stack: "",
    /**
     * @description Smooth line
     * @type {Boolean}
     * @default smooth = false
     */
    smooth: false,
    /**
     * @description Line x axis index
     * @type {Number}
     * @default xAxisIndex = 0
     * @example xAxisIndex = 0 | 1
     */
    xAxisIndex: 0,
    /**
     * @description Line y axis index
     * @type {Number}
     * @default yAxisIndex = 0
     * @example yAxisIndex = 0 | 1
     */
    yAxisIndex: 0,
    /**
     * @description Line chart data
     * @type {Array}
     * @default data = []
     * @example data = [100, 200, 300]
     */
    data: [],
    /**
     * @description Line default style configuration
     * @type {Object}
     * @default style = {Configuration Of Class Style}
     */
    lineStyle: {
      lineWidth: 1
    },
    /**
     * @description Line point configuration
     * @type {Object}
     */
    linePoint: {
      /**
       * @description Whether to display line point
       * @type {Boolean}
       * @default show = true
       */
      show: true,
      /**
       * @description Line point radius
       * @type {Number}
       * @default radius = 2
       */
      radius: 2,
      /**
       * @description Line point default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        fill: "#fff",
        lineWidth: 1
      }
    },
    /**
     * @description Line area configuration
     * @type {Object}
     */
    lineArea: {
      /**
       * @description Whether to display line area
       * @type {Boolean}
       * @default show = false
       */
      show: false,
      /**
       * @description Line area gradient color (Hex|rgb|rgba)
       * @type {Array}
       * @default gradient = []
       */
      gradient: [],
      /**
       * @description Line area style default configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        opacity: 0.5
      }
    },
    /**
     * @description Line label configuration
     * @type {Object}
     */
    label: {
      /**
       * @description Whether to display line label
       * @type {Boolean}
       * @default show = false
       */
      show: false,
      /**
       * @description Line label position
       * @type {String}
       * @default position = 'top'
       * @example position = 'top' | 'center' | 'bottom'
       */
      position: "top",
      /**
       * @description Line label offset
       * @type {Array}
       * @default offset = [0, -10]
       */
      offset: [0, -10],
      /**
       * @description Line label formatter
       * @type {String|Function}
       * @default formatter = null
       * @example formatter = '{value}件'
       * @example formatter = (dataItem) => (dataItem.value)
       */
      formatter: null,
      /**
       * @description Line label default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        fontSize: 10
      }
    },
    /**
     * @description Line chart render level
     * Priority rendering high level
     * @type {Number}
     * @default rLevel = 10
     */
    rLevel: 10,
    /**
     * @description Line animation curve
     * @type {String}
     * @default animationCurve = 'easeOutCubic'
     */
    animationCurve: "easeOutCubic",
    /**
     * @description Line animation frame
     * @type {Number}
     * @default animationFrame = 50
     */
    animationFrame: 50
  };
  return dt.lineConfig = e, dt;
}
var ht = {};
var _a;
function xu() {
  if (_a)
    return ht;
  _a = 1, Object.defineProperty(ht, "__esModule", {
    value: true
  }), ht.barConfig = void 0;
  var e = {
    /**
     * @description Whether to display this bar chart
     * @type {Boolean}
     * @default show = true
     */
    show: true,
    /**
     * @description Legend name
     * @type {String}
     * @default name = ''
     */
    name: "",
    /**
     * @description Data stacking
     * The data value of the series element of the same stack
     * will be superimposed (the latter value will be superimposed on the previous value)
     * @type {String}
     * @default stack = ''
     */
    stack: "",
    /**
     * @description Bar shape type
     * @type {String}
     * @default shapeType = 'normal'
     * @example shapeType = 'normal' | 'leftEchelon' | 'rightEchelon'
     */
    shapeType: "normal",
    /**
     * @description Echelon bar sharpness offset
     * @type {Number}
     * @default echelonOffset = 10
     */
    echelonOffset: 10,
    /**
     * @description Bar width
     * This property should be set on the last 'bar' series
     * in this coordinate system to take effect and will be in effect
     * for all 'bar' series in this coordinate system
     * @type {String|Number}
     * @default barWidth = 'auto'
     * @example barWidth = 'auto' | '10%' | 20
     */
    barWidth: "auto",
    /**
     * @description Bar gap
     * This property should be set on the last 'bar' series
     * in this coordinate system to take effect and will be in effect
     * for all 'bar' series in this coordinate system
     * @type {String|Number}
     * @default barGap = '30%'
     * @example barGap = '30%' | 30
     */
    barGap: "30%",
    /**
     * @description Bar category gap
     * This property should be set on the last 'bar' series
     * in this coordinate system to take effect and will be in effect
     * for all 'bar' series in this coordinate system
     * @type {String|Number}
     * @default barCategoryGap = '20%'
     * @example barCategoryGap = '20%' | 20
     */
    barCategoryGap: "20%",
    /**
     * @description Bar x axis index
     * @type {Number}
     * @default xAxisIndex = 0
     * @example xAxisIndex = 0 | 1
     */
    xAxisIndex: 0,
    /**
     * @description Bar y axis index
     * @type {Number}
     * @default yAxisIndex = 0
     * @example yAxisIndex = 0 | 1
     */
    yAxisIndex: 0,
    /**
     * @description Bar chart data
     * @type {Array}
     * @default data = []
     * @example data = [100, 200, 300]
     */
    data: [],
    /**
     * @description Background bar configuration
     * @type {Object}
     */
    backgroundBar: {
      /**
       * @description Whether to display background bar
       * @type {Boolean}
       * @default show = false
       */
      show: false,
      /**
       * @description Background bar width
       * @type {String|Number}
       * @default width = 'auto'
       * @example width = 'auto' | '30%' | 30
       */
      width: "auto",
      /**
       * @description Background bar default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        fill: "rgba(200, 200, 200, .4)"
      }
    },
    /**
     * @description Bar label configuration
     * @type {Object}
     */
    label: {
      /**
       * @description Whether to display bar label
       * @type {Boolean}
       * @default show = false
       */
      show: false,
      /**
       * @description Bar label position
       * @type {String}
       * @default position = 'top'
       * @example position = 'top' | 'center' | 'bottom'
       */
      position: "top",
      /**
       * @description Bar label offset
       * @type {Array}
       * @default offset = [0, -10]
       */
      offset: [0, -10],
      /**
       * @description Bar label formatter
       * @type {String|Function}
       * @default formatter = null
       * @example formatter = '{value}件'
       * @example formatter = (dataItem) => (dataItem.value)
       */
      formatter: null,
      /**
       * @description Bar label default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        fontSize: 10
      }
    },
    /**
     * @description Bar gradient configuration
     * @type {Object}
     */
    gradient: {
      /**
       * @description Gradient color (Hex|rgb|rgba)
       * @type {Array}
       * @default color = []
       */
      color: [],
      /**
       * @description Local gradient
       * @type {Boolean}
       * @default local = true
       */
      local: true
    },
    /**
     * @description Bar style default configuration
     * @type {Object}
     * @default style = {Configuration Of Class Style}
     */
    barStyle: {},
    /**
     * @description Independent color mode
     * When set to true, independent color mode is enabled
     * @type {Boolean}
     * @default independentColor = false
     */
    independentColor: false,
    /**
     * @description Independent colors
     * Only effective when independent color mode is enabled
     * Default value is the same as the color in the root configuration
     * Two-dimensional color array can produce gradient colors
     * @type {Array}
     * @example independentColor = ['#fff', '#000']
     * @example independentColor = [['#fff', '#000'], '#000']
     */
    independentColors: [],
    /**
     * @description Bar chart render level
     * Priority rendering high level
     * @type {Number}
     * @default rLevel = 0
     */
    rLevel: 0,
    /**
     * @description Bar animation curve
     * @type {String}
     * @default animationCurve = 'easeOutCubic'
     */
    animationCurve: "easeOutCubic",
    /**
     * @description Bar animation frame
     * @type {Number}
     * @default animationFrame = 50
     */
    animationFrame: 50
  };
  return ht.barConfig = e, ht;
}
var vt = {};
var $a;
function mi() {
  if ($a)
    return vt;
  $a = 1, Object.defineProperty(vt, "__esModule", {
    value: true
  }), vt.pieConfig = void 0;
  var e = {
    /**
     * @description Whether to display this pie chart
     * @type {Boolean}
     * @default show = true
     */
    show: true,
    /**
     * @description Legend name
     * @type {String}
     * @default name = ''
     */
    name: "",
    /**
     * @description Radius of pie
     * @type {String|Number}
     * @default radius = '50%'
     * @example radius = '50%' | 100
     */
    radius: "50%",
    /**
     * @description Center point of pie
     * @type {Array}
     * @default center = ['50%','50%']
     * @example center = ['50%','50%'] | [100, 100]
     */
    center: ["50%", "50%"],
    /**
     * @description Pie chart start angle
     * @type {Number}
     * @default startAngle = -Math.PI / 2
     * @example startAngle = -Math.PI
     */
    startAngle: -Math.PI / 2,
    /**
     * @description Whether to enable rose type
     * @type {Boolean}
     * @default roseType = false
     */
    roseType: false,
    /**
     * @description Automatic sorting in rose type
     * @type {Boolean}
     * @default roseSort = true
     */
    roseSort: true,
    /**
     * @description Rose radius increasing
     * @type {String|Number}
     * @default roseIncrement = 'auto'
     * @example roseIncrement = 'auto' | '10%' | 10
     */
    roseIncrement: "auto",
    /**
     * @description Pie chart data
     * @type {Array}
     * @default data = []
     */
    data: [],
    /**
     * @description Pie inside label configuration
     * @type {Object}
     */
    insideLabel: {
      /**
       * @description Whether to display inside label
       * @type {Boolean}
       * @default show = false
       */
      show: false,
      /**
       * @description Label formatter
       * @type {String|Function}
       * @default formatter = '{percent}%'
       * @example formatter = '${name}-{value}-{percent}%'
       * @example formatter = (dataItem) => (dataItem.name)
       */
      formatter: "{percent}%",
      /**
       * @description Label default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        fontSize: 10,
        fill: "#fff",
        textAlign: "center",
        textBaseline: "middle"
      }
    },
    /**
     * @description Pie Outside label configuration
     * @type {Object}
     */
    outsideLabel: {
      /**
       * @description Whether to display outside label
       * @type {Boolean}
       * @default show = false
       */
      show: true,
      /**
       * @description Label formatter
       * @type {String|Function}
       * @default formatter = '{name}'
       * @example formatter = '${name}-{value}-{percent}%'
       * @example formatter = (dataItem) => (dataItem.name)
       */
      formatter: "{name}",
      /**
       * @description Label default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        fontSize: 11
      },
      /**
       * @description Gap beteen label line bended place and pie
       * @type {String|Number}
       * @default labelLineBendGap = '20%'
       * @example labelLineBendGap = '20%' | 20
       */
      labelLineBendGap: "20%",
      /**
       * @description Label line end length
       * @type {Number}
       * @default labelLineEndLength = 50
       */
      labelLineEndLength: 50,
      /**
       * @description Label line default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      labelLineStyle: {
        lineWidth: 1
      }
    },
    /**
     * @description Pie default style configuration
     * @type {Object}
     * @default style = {Configuration Of Class Style}
     */
    pieStyle: {},
    /**
     * @description Percentage fractional precision
     * @type {Number}
     * @default percentToFixed = 0
     */
    percentToFixed: 0,
    /**
     * @description Pie chart render level
     * Priority rendering high level
     * @type {Number}
     * @default rLevel = 10
     */
    rLevel: 10,
    /**
     * @description Animation delay gap
     * @type {Number}
     * @default animationDelayGap = 60
     */
    animationDelayGap: 60,
    /**
     * @description Pie animation curve
     * @type {String}
     * @default animationCurve = 'easeOutCubic'
     */
    animationCurve: "easeOutCubic",
    /**
     * @description Pie start animation curve
     * @type {String}
     * @default startAnimationCurve = 'easeOutBack'
     */
    startAnimationCurve: "easeOutBack",
    /**
     * @description Pie animation frame
     * @type {Number}
     * @default animationFrame = 50
     */
    animationFrame: 50
  };
  return vt.pieConfig = e, vt;
}
var pt = {};
var Pa;
function Cu() {
  if (Pa)
    return pt;
  Pa = 1, Object.defineProperty(pt, "__esModule", {
    value: true
  }), pt.radarAxisConfig = void 0;
  var e = {
    /**
     * @description Whether to display this radar axis
     * @type {Boolean}
     * @default show = true
     */
    show: true,
    /**
     * @description Center point of radar axis
     * @type {Array}
     * @default center = ['50%','50%']
     * @example center = ['50%','50%'] | [100, 100]
     */
    center: ["50%", "50%"],
    /**
     * @description Radius of radar axis
     * @type {String|Number}
     * @default radius = '65%'
     * @example radius = '65%' | 100
     */
    radius: "65%",
    /**
     * @description Radar axis start angle
     * @type {Number}
     * @default startAngle = -Math.PI / 2
     * @example startAngle = -Math.PI
     */
    startAngle: -Math.PI / 2,
    /**
     * @description Radar axis split number
     * @type {Number}
     * @default splitNum = 5
     */
    splitNum: 5,
    /**
     * @description Whether to enable polygon radar axis
     * @type {Boolean}
     * @default polygon = false
     */
    polygon: false,
    /**
     * @description Axis label configuration
     * @type {Object}
     */
    axisLabel: {
      /**
       * @description Whether to display axis label
       * @type {Boolean}
       * @default show = true
       */
      show: true,
      /**
       * @description Label gap between label and radar axis
       * @type {Number}
       * @default labelGap = 15
       */
      labelGap: 15,
      /**
       * @description Label color (Hex|rgb|rgba), will cover style.fill
       * @type {Array}
       * @default color = []
       */
      color: [],
      /**
       * @description Axis label default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        fill: "#333"
      }
    },
    /**
     * @description Axis line configuration
     * @type {Object}
     */
    axisLine: {
      /**
       * @description Whether to display axis line
       * @type {Boolean}
       * @default show = true
       */
      show: true,
      /**
       * @description Line color (Hex|rgb|rgba), will cover style.stroke
       * @type {Array}
       * @default color = []
       */
      color: [],
      /**
       * @description Axis label default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        stroke: "#999",
        lineWidth: 1
      }
    },
    /**
     * @description Split line configuration
     * @type {Object}
     */
    splitLine: {
      /**
       * @description Whether to display split line
       * @type {Boolean}
       * @default show = true
       */
      show: true,
      /**
       * @description Line color (Hex|rgb|rgba), will cover style.stroke
       * @type {Array}
       * @default color = []
       */
      color: [],
      /**
       * @description Split line default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        stroke: "#d4d4d4",
        lineWidth: 1
      }
    },
    /**
     * @description Split area configuration
     * @type {Object}
     */
    splitArea: {
      /**
       * @description Whether to display split area
       * @type {Boolean}
       * @default show = false
       */
      show: false,
      /**
       * @description Area color (Hex|rgb|rgba), will cover style.stroke
       * @type {Array}
       * @default color = []
       */
      color: ["#f5f5f5", "#e6e6e6"],
      /**
       * @description Split area default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {}
    },
    /**
     * @description Bar chart render level
     * Priority rendering high level
     * @type {Number}
     * @default rLevel = -10
     */
    rLevel: -10,
    /**
     * @description Radar axis animation curve
     * @type {String}
     * @default animationCurve = 'easeOutCubic'
     */
    animationCurve: "easeOutCubic",
    /**
     * @description Radar axis animation frame
     * @type {Number}
     * @default animationFrame = 50
     */
    animationFrane: 50
  };
  return pt.radarAxisConfig = e, pt;
}
var gt = {};
var wa;
function _u() {
  if (wa)
    return gt;
  wa = 1, Object.defineProperty(gt, "__esModule", {
    value: true
  }), gt.radarConfig = void 0;
  var e = {
    /**
     * @description Whether to display this radar
     * @type {Boolean}
     * @default show = true
     */
    show: true,
    /**
     * @description Legend name
     * @type {String}
     * @default name = ''
     */
    name: "",
    /**
     * @description Radar chart data
     * @type {Array}
     * @default data = []
     * @example data = [100, 200, 300]
     */
    data: [],
    /**
     * @description Radar default style configuration
     * @type {Object}
     * @default style = {Configuration Of Class Style}
     */
    radarStyle: {
      lineWidth: 1
    },
    /**
     * @description Radar point configuration
     * @type {Object}
     */
    point: {
      /**
       * @description Whether to display radar point
       * @type {Boolean}
       * @default show = true
       */
      show: true,
      /**
       * @description Point radius
       * @type {Number}
       * @default radius = 2
       */
      radius: 2,
      /**
       * @description Radar point default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        fill: "#fff"
      }
    },
    /**
     * @description Radar label configuration
     * @type {Object}
     */
    label: {
      /**
       * @description Whether to display radar label
       * @type {Boolean}
       * @default show = true
       */
      show: true,
      /**
       * @description Label position offset
       * @type {Array}
       * @default offset = [0, 0]
       */
      offset: [0, 0],
      /**
       * @description Label gap between label and radar
       * @type {Number}
       * @default labelGap = 5
       */
      labelGap: 5,
      /**
       * @description Label formatter
       * @type {String|Function}
       * @default formatter = null
       * @example formatter = 'Score-{value}'
       * @example formatter = (label) => (label)
       */
      formatter: null,
      /**
       * @description Radar label default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        fontSize: 10
      }
    },
    /**
     * @description Radar chart render level
     * Priority rendering high level
     * @type {Number}
     * @default rLevel = 10
     */
    rLevel: 10,
    /**
     * @description Radar animation curve
     * @type {String}
     * @default animationCurve = 'easeOutCubic'
     */
    animationCurve: "easeOutCubic",
    /**
     * @description Radar animation frame
     * @type {Number}
     * @default animationFrame = 50
     */
    animationFrane: 50
  };
  return gt.radarConfig = e, gt;
}
var mt = {};
var ka;
function yi() {
  if (ka)
    return mt;
  ka = 1, Object.defineProperty(mt, "__esModule", {
    value: true
  }), mt.gaugeConfig = void 0;
  var e = {
    /**
     * @description Whether to display this gauge
     * @type {Boolean}
     * @default show = true
     */
    show: true,
    /**
     * @description Legend name
     * @type {String}
     * @default name = ''
     */
    name: "",
    /**
     * @description Radius of gauge
     * @type {String|Number}
     * @default radius = '60%'
     * @example radius = '60%' | 100
     */
    radius: "60%",
    /**
     * @description Center point of gauge
     * @type {Array}
     * @default center = ['50%','50%']
     * @example center = ['50%','50%'] | [100, 100]
     */
    center: ["50%", "50%"],
    /**
     * @description Gauge start angle
     * @type {Number}
     * @default startAngle = -(Math.PI / 4) * 5
     * @example startAngle = -Math.PI
     */
    startAngle: -(Math.PI / 4) * 5,
    /**
     * @description Gauge end angle
     * @type {Number}
     * @default endAngle = Math.PI / 4
     * @example endAngle = 0
     */
    endAngle: Math.PI / 4,
    /**
     * @description Gauge min value
     * @type {Number}
     * @default min = 0
     */
    min: 0,
    /**
     * @description Gauge max value
     * @type {Number}
     * @default max = 100
     */
    max: 100,
    /**
     * @description Gauge split number
     * @type {Number}
     * @default splitNum = 5
     */
    splitNum: 5,
    /**
     * @description Gauge arc line width
     * @type {Number}
     * @default arcLineWidth = 15
     */
    arcLineWidth: 15,
    /**
     * @description Gauge chart data
     * @type {Array}
     * @default data = []
     */
    data: [],
    /**
     * @description Data item arc default style configuration
     * @type {Object}
     * @default dataItemStyle = {Configuration Of Class Style}
     */
    dataItemStyle: {},
    /**
     * @description Axis tick configuration
     * @type {Object}
     */
    axisTick: {
      /**
       * @description Whether to display axis tick
       * @type {Boolean}
       * @default show = true
       */
      show: true,
      /**
       * @description Axis tick length
       * @type {Number}
       * @default tickLength = 6
       */
      tickLength: 6,
      /**
       * @description Axis tick default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        stroke: "#999",
        lineWidth: 1
      }
    },
    /**
     * @description Axis label configuration
     * @type {Object}
     */
    axisLabel: {
      /**
       * @description Whether to display axis label
       * @type {Boolean}
       * @default show = true
       */
      show: true,
      /**
       * @description Axis label data (Can be calculated automatically)
       * @type {Array}
       * @default data = [Number...]
       */
      data: [],
      /**
       * @description Axis label formatter
       * @type {String|Function}
       * @default formatter = null
       * @example formatter = '{value}%'
       * @example formatter = (labelItem) => (labelItem.value)
       */
      formatter: null,
      /**
       * @description Axis label gap between label and axis tick
       * @type {String|Function}
       * @default labelGap = 5
       */
      labelGap: 5,
      /**
       * @description Axis label default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {}
    },
    /**
     * @description Gauge pointer configuration
     * @type {Object}
     */
    pointer: {
      /**
       * @description Whether to display pointer
       * @type {Boolean}
       * @default show = true
       */
      show: true,
      /**
       * @description Pointer value index of data
       * @type {Number}
       * @default valueIndex = 0 (pointer.value = data[0].value)
       */
      valueIndex: 0,
      /**
       * @description Pointer default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        scale: [1, 1],
        fill: "#fb7293"
      }
    },
    /**
     * @description Data item arc detail configuration
     * @type {Object}
     */
    details: {
      /**
       * @description Whether to display details
       * @type {Boolean}
       * @default show = false
       */
      show: false,
      /**
       * @description Details formatter
       * @type {String|Function}
       * @default formatter = null
       * @example formatter = '{value}%'
       * @example formatter = '{name}%'
       * @example formatter = (dataItem) => (dataItem.value)
       */
      formatter: null,
      /**
       * @description Details position offset
       * @type {Array}
       * @default offset = [0, 0]
       * @example offset = [10, 10]
       */
      offset: [0, 0],
      /**
       * @description Value fractional precision
       * @type {Number}
       * @default valueToFixed = 0
       */
      valueToFixed: 0,
      /**
       * @description Details position
       * @type {String}
       * @default position = 'center'
       * @example position = 'start' | 'center' | 'end'
       */
      position: "center",
      /**
       * @description Details default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        fontSize: 20,
        fontWeight: "bold",
        textAlign: "center",
        textBaseline: "middle"
      }
    },
    /**
     * @description Gauge background arc configuration
     * @type {Object}
     */
    backgroundArc: {
      /**
       * @description Whether to display background arc
       * @type {Boolean}
       * @default show = true
       */
      show: true,
      /**
       * @description Background arc default style configuration
       * @type {Object}
       * @default style = {Configuration Of Class Style}
       */
      style: {
        stroke: "#e0e0e0"
      }
    },
    /**
     * @description Gauge chart render level
     * Priority rendering high level
     * @type {Number}
     * @default rLevel = 10
     */
    rLevel: 10,
    /**
     * @description Gauge animation curve
     * @type {String}
     * @default animationCurve = 'easeOutCubic'
     */
    animationCurve: "easeOutCubic",
    /**
     * @description Gauge animation frame
     * @type {Number}
     * @default animationFrame = 50
     */
    animationFrame: 50
  };
  return mt.gaugeConfig = e, mt;
}
var yt = {};
var Aa;
function $u() {
  if (Aa)
    return yt;
  Aa = 1, Object.defineProperty(yt, "__esModule", {
    value: true
  }), yt.legendConfig = void 0;
  var e = {
    /**
     * @description Whether to display legend
     * @type {Boolean}
     * @default show = true
     */
    show: true,
    /**
     * @description Legend orient
     * @type {String}
     * @default orient = 'horizontal'
     * @example orient = 'horizontal' | 'vertical'
     */
    orient: "horizontal",
    /**
     * @description Legend left
     * @type {String|Number}
     * @default left = 'auto'
     * @example left = 'auto' | '10%' | 10
     */
    left: "auto",
    /**
     * @description Legend right
     * @type {String|Number}
     * @default right = 'auto'
     * @example right = 'auto' | '10%' | 10
     */
    right: "auto",
    /**
     * @description Legend top
     * @type {String|Number}
     * @default top = 'auto'
     * @example top = 'auto' | '10%' | 10
     */
    top: "auto",
    /**
     * @description Legend bottom
     * @type {String|Number}
     * @default bottom = 'auto'
     * @example bottom = 'auto' | '10%' | 10
     */
    bottom: "auto",
    /**
     * @description Legend item gap
     * @type {Number}
     * @default itemGap = 10
     */
    itemGap: 10,
    /**
     * @description Icon width
     * @type {Number}
     * @default iconWidth = 25
     */
    iconWidth: 25,
    /**
     * @description Icon height
     * @type {Number}
     * @default iconHeight = 10
     */
    iconHeight: 10,
    /**
     * @description Whether legend is optional
     * @type {Boolean}
     * @default selectAble = true
     */
    selectAble: true,
    /**
     * @description Legend data
     * @type {Array}
     * @default data = []
     */
    data: [],
    /**
     * @description Legend text default style configuration
     * @type {Object}
     * @default style = {Configuration Of Class Style}
     */
    textStyle: {
      fontFamily: "Arial",
      fontSize: 13,
      fill: "#000"
    },
    /**
     * @description Legend icon default style configuration
     * @type {Object}
     * @default style = {Configuration Of Class Style}
     */
    iconStyle: {},
    /**
     * @description Legend text unselected default style configuration
     * @type {Object}
     * @default style = {Configuration Of Class Style}
     */
    textUnselectedStyle: {
      fontFamily: "Arial",
      fontSize: 13,
      fill: "#999"
    },
    /**
     * @description Legend icon unselected default style configuration
     * @type {Object}
     * @default style = {Configuration Of Class Style}
     */
    iconUnselectedStyle: {
      fill: "#999"
    },
    /**
     * @description Legend render level
     * Priority rendering high level
     * @type {Number}
     * @default rLevel = 20
     */
    rLevel: 20,
    /**
     * @description Legend animation curve
     * @type {String}
     * @default animationCurve = 'easeOutCubic'
     */
    animationCurve: "easeOutCubic",
    /**
     * @description Legend animation frame
     * @type {Number}
     * @default animationFrame = 50
     */
    animationFrame: 50
  };
  return yt.legendConfig = e, yt;
}
var La;
function Xe() {
  return La || (La = 1, function(e) {
    Object.defineProperty(e, "__esModule", {
      value: true
    }), e.changeDefaultConfig = v, Object.defineProperty(e, "colorConfig", {
      enumerable: true,
      get: function() {
        return t.colorConfig;
      }
    }), Object.defineProperty(e, "gridConfig", {
      enumerable: true,
      get: function() {
        return r.gridConfig;
      }
    }), Object.defineProperty(e, "xAxisConfig", {
      enumerable: true,
      get: function() {
        return n.xAxisConfig;
      }
    }), Object.defineProperty(e, "yAxisConfig", {
      enumerable: true,
      get: function() {
        return n.yAxisConfig;
      }
    }), Object.defineProperty(e, "titleConfig", {
      enumerable: true,
      get: function() {
        return a.titleConfig;
      }
    }), Object.defineProperty(e, "lineConfig", {
      enumerable: true,
      get: function() {
        return o.lineConfig;
      }
    }), Object.defineProperty(e, "barConfig", {
      enumerable: true,
      get: function() {
        return l.barConfig;
      }
    }), Object.defineProperty(e, "pieConfig", {
      enumerable: true,
      get: function() {
        return s.pieConfig;
      }
    }), Object.defineProperty(e, "radarAxisConfig", {
      enumerable: true,
      get: function() {
        return D.radarAxisConfig;
      }
    }), Object.defineProperty(e, "radarConfig", {
      enumerable: true,
      get: function() {
        return W.radarConfig;
      }
    }), Object.defineProperty(e, "gaugeConfig", {
      enumerable: true,
      get: function() {
        return M.gaugeConfig;
      }
    }), Object.defineProperty(e, "legendConfig", {
      enumerable: true,
      get: function() {
        return U.legendConfig;
      }
    }), e.keys = void 0;
    var t = pu(), r = gu(), n = mu(), a = yu(), o = bu(), l = xu(), s = mi(), D = Cu(), W = _u(), M = yi(), U = $u(), F = qe(), A = {
      colorConfig: t.colorConfig,
      gridConfig: r.gridConfig,
      xAxisConfig: n.xAxisConfig,
      yAxisConfig: n.yAxisConfig,
      titleConfig: a.titleConfig,
      lineConfig: o.lineConfig,
      barConfig: l.barConfig,
      pieConfig: s.pieConfig,
      radarAxisConfig: D.radarAxisConfig,
      radarConfig: W.radarConfig,
      gaugeConfig: M.gaugeConfig,
      legendConfig: U.legendConfig
    };
    function v(N, I) {
      if (!A["".concat(N, "Config")]) {
        console.warn("Change default config Error - Invalid key!");
        return;
      }
      (0, F.deepMerge)(A["".concat(N, "Config")], I);
    }
    var R = ["color", "title", "legend", "xAxis", "yAxis", "grid", "radarAxis", "line", "bar", "pie", "radar", "gauge"];
    e.keys = R;
  }(qr)), qr;
}
var Sa;
function Pu() {
  if (Sa)
    return Rt;
  Sa = 1, Object.defineProperty(Rt, "__esModule", {
    value: true
  }), Rt.mergeColor = n;
  var e = Xe(), t = Fe(), r = qe();
  function n(a) {
    var o = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, l = (0, t.deepClone)(e.colorConfig, true), s = o.color, D = o.series;
    if (D || (D = []), s || (s = []), o.color = s = (0, r.deepMerge)(l, s), !!D.length) {
      var W = s.length;
      D.forEach(function(A, v) {
        A.color || (A.color = s[v % W]);
      });
      var M = D.filter(function(A) {
        var v = A.type;
        return v === "pie";
      });
      M.forEach(function(A) {
        return A.data.forEach(function(v, R) {
          return v.color = s[R % W];
        });
      });
      var U = D.filter(function(A) {
        var v = A.type;
        return v === "gauge";
      });
      U.forEach(function(A) {
        return A.data.forEach(function(v, R) {
          return v.color = s[R % W];
        });
      });
      var F = D.filter(function(A) {
        var v = A.type, R = A.independentColor;
        return v === "bar" && R;
      });
      F.forEach(function(A) {
        A.independentColors || (A.independentColors = s);
      });
    }
  }
  return Rt;
}
var Tt = {};
var it = {};
var Oa;
function Qe() {
  if (Oa)
    return it;
  Oa = 1;
  var e = we;
  Object.defineProperty(it, "__esModule", {
    value: true
  }), it.doUpdate = M, it.Updater = void 0;
  var t = e(Be()), r = e(ze()), n = e(Lt()), a = function U(F, A) {
    (0, n.default)(this, U);
    var v = F.chart, R = F.key, N = F.getGraphConfig;
    if (typeof N != "function") {
      console.warn("Updater need function getGraphConfig!");
      return;
    }
    v[R] || (this.graphs = v[R] = []), Object.assign(this, F), this.update(A);
  };
  it.Updater = a, a.prototype.update = function(U) {
    var F = this, A = this.graphs, v = this.beforeUpdate;
    if (o(this, U), !!U.length) {
      var R = (0, r.default)(v);
      U.forEach(function(N, I) {
        R === "function" && v(A, N, I, F);
        var E = A[I];
        E ? l(E, N, I, F) : D(A, N, I, F);
      });
    }
  };
  function o(U, F) {
    var A = U.graphs, v = U.chart.render, R = A.length, N = F.length;
    if (R > N) {
      var I = A.splice(N);
      I.forEach(function(E) {
        return E.forEach(function(b) {
          return v.delGraph(b);
        });
      });
    }
  }
  function l(U, F, A, v) {
    var R = v.getGraphConfig, N = v.chart.render, I = v.beforeChange, E = R(F, v);
    s(U, E, N), U.forEach(function(b, $) {
      var f = E[$];
      typeof I == "function" && I(b, f), W(b, f);
    });
  }
  function s(U, F, A) {
    var v = U.length, R = F.length;
    if (R > v) {
      var N = U.slice(-1)[0], I = R - v, E = new Array(I).fill(0).map(function($) {
        return A.clone(N);
      });
      U.push.apply(U, (0, t.default)(E));
    } else if (R < v) {
      var b = U.splice(R);
      b.forEach(function($) {
        return A.delGraph($);
      });
    }
  }
  function D(U, F, A, v) {
    var R = v.getGraphConfig, N = v.getStartGraphConfig, I = v.chart, E = I.render, b = null;
    typeof N == "function" && (b = N(F, v));
    var $ = R(F, v);
    if ($.length) {
      b ? (U[A] = b.map(function(_) {
        return E.add(_);
      }), U[A].forEach(function(_, O) {
        var m = $[O];
        W(_, m);
      })) : U[A] = $.map(function(_) {
        return E.add(_);
      });
      var f = v.afterAddGraph;
      typeof f == "function" && f(U[A]);
    }
  }
  function W(U, F) {
    var A = Object.keys(F);
    A.forEach(function(v) {
      v === "shape" || v === "style" ? U.animation(v, F[v], true) : U[v] = F[v];
    });
  }
  function M() {
    var U = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {}, F = U.chart, A = U.series, v = U.key, R = U.getGraphConfig, N = U.getStartGraphConfig, I = U.beforeChange, E = U.beforeUpdate, b = U.afterAddGraph;
    F[v] ? F[v].update(A) : F[v] = new a({
      chart: F,
      key: v,
      getGraphConfig: R,
      getStartGraphConfig: N,
      beforeChange: I,
      beforeUpdate: E,
      afterAddGraph: b
    }, A);
  }
  return it;
}
var Ga;
function wu() {
  if (Ga)
    return Tt;
  Ga = 1;
  var e = we;
  Object.defineProperty(Tt, "__esModule", {
    value: true
  }), Tt.title = l;
  var t = e(Ne()), r = Qe(), n = Fe(), a = Xe(), o = qe();
  function l(M) {
    var U = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, F = [];
    U.title && (F[0] = (0, o.deepMerge)((0, n.deepClone)(a.titleConfig, true), U.title)), (0, r.doUpdate)({
      chart: M,
      series: F,
      key: "title",
      getGraphConfig: s
    });
  }
  function s(M, U) {
    var F = a.titleConfig.animationCurve, A = a.titleConfig.animationFrame, v = a.titleConfig.rLevel, R = D(M, U), N = W(M);
    return [{
      name: "text",
      index: v,
      visible: M.show,
      animationCurve: F,
      animationFrame: A,
      shape: R,
      style: N
    }];
  }
  function D(M, U) {
    var F = M.offset, A = M.text, v = U.chart.gridArea, R = v.x, N = v.y, I = v.w, E = (0, t.default)(F, 2), b = E[0], $ = E[1];
    return {
      content: A,
      position: [R + I / 2 + b, N + $]
    };
  }
  function W(M) {
    var U = M.style;
    return U;
  }
  return Tt;
}
var Dt = {};
var Ma;
function ku() {
  if (Ma)
    return Dt;
  Ma = 1;
  var e = we;
  Object.defineProperty(Dt, "__esModule", {
    value: true
  }), Dt.grid = W;
  var t = e(Ne()), r = e(Ue()), n = Qe(), a = Fe(), o = Xe(), l = qe();
  function s(v, R) {
    var N = Object.keys(v);
    if (Object.getOwnPropertySymbols) {
      var I = Object.getOwnPropertySymbols(v);
      R && (I = I.filter(function(E) {
        return Object.getOwnPropertyDescriptor(v, E).enumerable;
      })), N.push.apply(N, I);
    }
    return N;
  }
  function D(v) {
    for (var R = 1; R < arguments.length; R++) {
      var N = arguments[R] != null ? arguments[R] : {};
      R % 2 ? s(Object(N), true).forEach(function(I) {
        (0, r.default)(v, I, N[I]);
      }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(v, Object.getOwnPropertyDescriptors(N)) : s(Object(N)).forEach(function(I) {
        Object.defineProperty(v, I, Object.getOwnPropertyDescriptor(N, I));
      });
    }
    return v;
  }
  function W(v) {
    var R = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, N = R.grid;
    N = (0, l.deepMerge)((0, a.deepClone)(o.gridConfig, true), N || {}), (0, n.doUpdate)({
      chart: v,
      series: [N],
      key: "grid",
      getGraphConfig: M
    });
  }
  function M(v, R) {
    var N = v.animationCurve, I = v.animationFrame, E = v.rLevel, b = U(v, R), $ = A(v);
    return R.chart.gridArea = D({}, b), [{
      name: "rect",
      index: E,
      animationCurve: N,
      animationFrame: I,
      shape: b,
      style: $
    }];
  }
  function U(v, R) {
    var N = (0, t.default)(R.chart.render.area, 2), I = N[0], E = N[1], b = F(v.left, I), $ = F(v.right, I), f = F(v.top, E), _ = F(v.bottom, E), O = I - b - $, m = E - f - _;
    return {
      x: b,
      y: f,
      w: O,
      h: m
    };
  }
  function F(v, R) {
    return typeof v == "number" ? v : typeof v != "string" ? 0 : R * parseInt(v) / 100;
  }
  function A(v) {
    var R = v.style;
    return R;
  }
  return Dt;
}
var Bt = {};
var Ra;
function Au() {
  if (Ra)
    return Bt;
  Ra = 1;
  var e = we;
  Object.defineProperty(Bt, "__esModule", {
    value: true
  }), Bt.axis = v;
  var t = e(ze()), r = e(Ne()), n = e(Ue()), a = e(Be()), o = Qe(), l = Xe(), s = qe(), D = Fe();
  function W(u, x) {
    var w = Object.keys(u);
    if (Object.getOwnPropertySymbols) {
      var T = Object.getOwnPropertySymbols(u);
      x && (T = T.filter(function(Y) {
        return Object.getOwnPropertyDescriptor(u, Y).enumerable;
      })), w.push.apply(w, T);
    }
    return w;
  }
  function M(u) {
    for (var x = 1; x < arguments.length; x++) {
      var w = arguments[x] != null ? arguments[x] : {};
      x % 2 ? W(Object(w), true).forEach(function(T) {
        (0, n.default)(u, T, w[T]);
      }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(u, Object.getOwnPropertyDescriptors(w)) : W(Object(w)).forEach(function(T) {
        Object.defineProperty(u, T, Object.getOwnPropertyDescriptor(w, T));
      });
    }
    return u;
  }
  var U = {
    xAxisConfig: l.xAxisConfig,
    yAxisConfig: l.yAxisConfig
  }, F = Math.abs, A = Math.pow;
  function v(u) {
    var x = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, w = x.xAxis, T = x.yAxis, Y = x.series, re = [];
    w && T && Y && (re = R(w, T), re = N(re), re = re.filter(function(ue) {
      var de = ue.show;
      return de;
    }), re = I(re), re = E(re, Y), re = X(re), re = Z(re, u), re = c(re), re = h2(re), re = P(re, u)), (0, o.doUpdate)({
      chart: u,
      series: re,
      key: "axisLine",
      getGraphConfig: q
    }), (0, o.doUpdate)({
      chart: u,
      series: re,
      key: "axisTick",
      getGraphConfig: ae
    }), (0, o.doUpdate)({
      chart: u,
      series: re,
      key: "axisLabel",
      getGraphConfig: Q
    }), (0, o.doUpdate)({
      chart: u,
      series: re,
      key: "axisName",
      getGraphConfig: k
    }), (0, o.doUpdate)({
      chart: u,
      series: re,
      key: "splitLine",
      getGraphConfig: te
    }), u.axisData = re;
  }
  function R(u, x) {
    var w = [], T = [];
    if (u instanceof Array) {
      var Y;
      (Y = w).push.apply(Y, (0, a.default)(u));
    } else
      w.push(u);
    if (x instanceof Array) {
      var re;
      (re = T).push.apply(re, (0, a.default)(x));
    } else
      T.push(x);
    return w.splice(2), T.splice(2), w = w.map(function(ue, de) {
      return M(M({}, ue), {}, {
        index: de,
        axis: "x"
      });
    }), T = T.map(function(ue, de) {
      return M(M({}, ue), {}, {
        index: de,
        axis: "y"
      });
    }), [].concat((0, a.default)(w), (0, a.default)(T));
  }
  function N(u) {
    var x = u.filter(function(T) {
      var Y = T.axis;
      return Y === "x";
    }), w = u.filter(function(T) {
      var Y = T.axis;
      return Y === "y";
    });
    return x = x.map(function(T) {
      return (0, s.deepMerge)((0, D.deepClone)(l.xAxisConfig), T);
    }), w = w.map(function(T) {
      return (0, s.deepMerge)((0, D.deepClone)(l.yAxisConfig), T);
    }), [].concat((0, a.default)(x), (0, a.default)(w));
  }
  function I(u) {
    var x = u.filter(function(T) {
      var Y = T.data;
      return Y === "value";
    }), w = u.filter(function(T) {
      var Y = T.data;
      return Y !== "value";
    });
    return x.forEach(function(T) {
      typeof T.boundaryGap != "boolean" && (T.boundaryGap = false);
    }), w.forEach(function(T) {
      typeof T.boundaryGap != "boolean" && (T.boundaryGap = true);
    }), [].concat((0, a.default)(x), (0, a.default)(w));
  }
  function E(u, x) {
    var w = u.filter(function(Y) {
      var re = Y.data;
      return re === "value";
    }), T = u.filter(function(Y) {
      var re = Y.data;
      return re instanceof Array;
    });
    return w = b(w, x), T = g(T), [].concat((0, a.default)(w), (0, a.default)(T));
  }
  function b(u, x) {
    return u.map(function(w) {
      var T = $(w, x), Y = O(w, T), re = (0, r.default)(Y, 2), ue = re[0], de = re[1], he = V(ue, de, w), pe = w.axisLabel.formatter, ge = [];
      return ue < 0 && de > 0 ? ge = d(ue, de, he) : ge = G(ue, de, he), ge = ge.map(function(ye) {
        return parseFloat(ye.toFixed(2));
      }), M(M({}, w), {}, {
        maxValue: ge.slice(-1)[0],
        minValue: ge[0],
        label: L(ge, pe)
      });
    });
  }
  function $(u, x) {
    if (x = x.filter(function(ue) {
      var de = ue.show, he = ue.type;
      return !(de === false || he === "pie");
    }), x.length === 0)
      return [0, 0];
    var w = u.index, T = u.axis;
    x = _(x);
    var Y = T + "Axis", re = x.filter(function(ue) {
      return ue[Y] === w;
    });
    return re.length || (re = x), f(re);
  }
  function f(u) {
    if (u) {
      var x = Math.min.apply(Math, (0, a.default)(u.map(function(T) {
        var Y = T.data;
        return Math.min.apply(Math, (0, a.default)((0, s.filterNonNumber)(Y)));
      }))), w = Math.max.apply(Math, (0, a.default)(u.map(function(T) {
        var Y = T.data;
        return Math.max.apply(Math, (0, a.default)((0, s.filterNonNumber)(Y)));
      })));
      return [x, w];
    }
  }
  function _(u) {
    var x = (0, D.deepClone)(u, true);
    return u.forEach(function(w, T) {
      var Y = (0, s.mergeSameStackData)(w, u);
      x[T].data = Y;
    }), x;
  }
  function O(u, x) {
    var w = u.min, T = u.max, Y = u.axis, re = (0, r.default)(x, 2), ue = re[0], de = re[1], he = (0, t.default)(w), pe = (0, t.default)(T);
    if (C(w) || (w = U[Y + "AxisConfig"].min, he = "string"), C(T) || (T = U[Y + "AxisConfig"].max, pe = "string"), he === "string") {
      w = parseInt(ue - F(ue * parseFloat(w) / 100));
      var ge = m(w);
      w = parseFloat((w / ge - 0.1).toFixed(1)) * ge;
    }
    if (pe === "string") {
      T = parseInt(de + F(de * parseFloat(T) / 100));
      var ye = m(T);
      T = parseFloat((T / ye + 0.1).toFixed(1)) * ye;
    }
    return [w, T];
  }
  function m(u) {
    var x = F(u).toString(), w = x.length, T = x.replace(/0*$/g, "").indexOf("0"), Y = w - 1;
    return T !== -1 && (Y -= T), A(10, Y);
  }
  function C(u) {
    var x = (0, t.default)(u), w = x === "string" && /^\d+%$/.test(u), T = x === "number";
    return w || T;
  }
  function d(u, x, w) {
    var T = [], Y = [], re = 0, ue = 0;
    do
      T.push(re -= w);
    while (re > u);
    do
      Y.push(ue += w);
    while (ue < x);
    return [].concat((0, a.default)(T.reverse()), [0], (0, a.default)(Y));
  }
  function G(u, x, w) {
    var T = [u], Y = u;
    do
      T.push(Y += w);
    while (Y < x);
    return T;
  }
  function L(u, x) {
    return x && (typeof x == "string" && (u = u.map(function(w) {
      return x.replace("{value}", w);
    })), typeof x == "function" && (u = u.map(function(w, T) {
      return x({
        value: w,
        index: T
      });
    }))), u;
  }
  function g(u) {
    return u.map(function(x) {
      var w = x.data, T = x.axisLabel.formatter;
      return M(M({}, x), {}, {
        label: L(w, T)
      });
    });
  }
  function V(u, x, w) {
    var T = w.interval, Y = w.minInterval, re = w.maxInterval, ue = w.splitNumber, de = w.axis, he = U[de + "AxisConfig"];
    if (typeof T != "number" && (T = he.interval), typeof Y != "number" && (Y = he.minInterval), typeof re != "number" && (re = he.maxInterval), typeof ue != "number" && (ue = he.splitNumber), typeof T == "number")
      return T;
    var pe = parseInt((x - u) / (ue - 1));
    return pe.toString().length > 1 && (pe = parseInt(pe.toString().replace(/\d$/, "0"))), pe === 0 && (pe = 1), typeof Y == "number" && pe < Y ? Y : typeof re == "number" && pe > re ? re : pe;
  }
  function X(u) {
    var x = u.filter(function(T) {
      var Y = T.axis;
      return Y === "x";
    }), w = u.filter(function(T) {
      var Y = T.axis;
      return Y === "y";
    });
    return x[0] && !x[0].position && (x[0].position = l.xAxisConfig.position), x[1] && !x[1].position && (x[1].position = x[0].position === "bottom" ? "top" : "bottom"), w[0] && !w[0].position && (w[0].position = l.yAxisConfig.position), w[1] && !w[1].position && (w[1].position = w[0].position === "left" ? "right" : "left"), [].concat((0, a.default)(x), (0, a.default)(w));
  }
  function Z(u, x) {
    var w = x.gridArea, T = w.x, Y = w.y, re = w.w, ue = w.h;
    return u = u.map(function(de) {
      var he = de.position, pe = [];
      return he === "left" ? pe = [[T, Y], [T, Y + ue]].reverse() : he === "right" ? pe = [[T + re, Y], [T + re, Y + ue]].reverse() : he === "top" ? pe = [[T, Y], [T + re, Y]] : he === "bottom" && (pe = [[T, Y + ue], [T + re, Y + ue]]), M(M({}, de), {}, {
        linePosition: pe
      });
    }), u;
  }
  function c(u, x) {
    return u.map(function(w) {
      var T = w.axis, Y = w.linePosition, re = w.position, ue = w.label, de = w.boundaryGap;
      typeof de != "boolean" && (de = U[T + "AxisConfig"].boundaryGap);
      var he = ue.length, pe = (0, r.default)(Y, 2), ge = (0, r.default)(pe[0], 2), ye = ge[0], Ge = ge[1], Ke = (0, r.default)(pe[1], 2), et = Ke[0], Je = Ke[1], Ni = T === "x" ? et - ye : Je - Ge, Gt = Ni / (de ? he : he - 1), Fn = new Array(he).fill(0).map(function(wp, Mt) {
        return T === "x" ? [ye + Gt * (de ? Mt + 0.5 : Mt), Ge] : [ye, Ge + Gt * (de ? Mt + 0.5 : Mt)];
      }), ji = y(T, de, re, Fn, Gt);
      return M(M({}, w), {}, {
        tickPosition: Fn,
        tickLinePosition: ji,
        tickGap: Gt
      });
    });
  }
  function y(u, x, w, T, Y) {
    var re = u === "x" ? 1 : 0, ue = 5;
    u === "x" && w === "top" && (ue = -5), u === "y" && w === "left" && (ue = -5);
    var de = T.map(function(he) {
      var pe = (0, D.deepClone)(he);
      return pe[re] += ue, [(0, D.deepClone)(he), pe];
    });
    return x && (re = u === "x" ? 0 : 1, ue = Y / 2, de.forEach(function(he) {
      var pe = (0, r.default)(he, 2), ge = pe[0], ye = pe[1];
      ge[re] += ue, ye[re] += ue;
    })), de;
  }
  function h2(u, x) {
    return u.map(function(w) {
      var T = w.nameGap, Y = w.nameLocation, re = w.position, ue = w.linePosition, de = (0, r.default)(ue, 2), he = de[0], pe = de[1], ge = (0, a.default)(he);
      Y === "end" && (ge = (0, a.default)(pe)), Y === "center" && (ge[0] = (he[0] + pe[0]) / 2, ge[1] = (he[1] + pe[1]) / 2);
      var ye = 0;
      re === "top" && Y === "center" && (ye = 1), re === "bottom" && Y === "center" && (ye = 1), re === "left" && Y !== "center" && (ye = 1), re === "right" && Y !== "center" && (ye = 1);
      var Ge = T;
      return re === "top" && Y !== "end" && (Ge *= -1), re === "left" && Y !== "start" && (Ge *= -1), re === "bottom" && Y === "start" && (Ge *= -1), re === "right" && Y === "end" && (Ge *= -1), ge[ye] += Ge, M(M({}, w), {}, {
        namePosition: ge
      });
    });
  }
  function P(u, x) {
    var w = x.gridArea, T = w.w, Y = w.h;
    return u.map(function(re) {
      var ue = re.tickLinePosition, de = re.position, he = re.boundaryGap, pe = 0, ge = T;
      (de === "top" || de === "bottom") && (pe = 1), (de === "top" || de === "bottom") && (ge = Y), (de === "right" || de === "bottom") && (ge *= -1);
      var ye = ue.map(function(Ge) {
        var Ke = (0, r.default)(Ge, 1), et = Ke[0], Je = (0, a.default)(et);
        return Je[pe] += ge, [(0, a.default)(et), Je];
      });
      return he || ye.shift(), M(M({}, re), {}, {
        splitLinePosition: ye
      });
    });
  }
  function q(u) {
    var x = u.animationCurve, w = u.animationFrame, T = u.rLevel;
    return [{
      name: "polyline",
      index: T,
      visible: u.axisLine.show,
      animationCurve: x,
      animationFrame: w,
      shape: K(u),
      style: ee(u)
    }];
  }
  function K(u) {
    var x = u.linePosition;
    return {
      points: x
    };
  }
  function ee(u) {
    return u.axisLine.style;
  }
  function ae(u) {
    var x = u.animationCurve, w = u.animationFrame, T = u.rLevel, Y = oe(u), re = ve(u);
    return Y.map(function(ue) {
      return {
        name: "polyline",
        index: T,
        visible: u.axisTick.show,
        animationCurve: x,
        animationFrame: w,
        shape: ue,
        style: re
      };
    });
  }
  function oe(u) {
    var x = u.tickLinePosition;
    return x.map(function(w) {
      return {
        points: w
      };
    });
  }
  function ve(u) {
    return u.axisTick.style;
  }
  function Q(u) {
    var x = u.animationCurve, w = u.animationFrame, T = u.rLevel, Y = ie(u), re = fe(u, Y);
    return Y.map(function(ue, de) {
      return {
        name: "text",
        index: T,
        visible: u.axisLabel.show,
        animationCurve: x,
        animationFrame: w,
        shape: ue,
        style: re[de],
        setGraphCenter: function() {
        }
      };
    });
  }
  function ie(u) {
    var x = u.label, w = u.tickPosition, T = u.position;
    return w.map(function(Y, re) {
      return {
        position: ce(Y, T),
        content: x[re].toString()
      };
    });
  }
  function ce(u, x) {
    var w = 0, T = 10;
    return (x === "top" || x === "bottom") && (w = 1), (x === "top" || x === "left") && (T = -10), u = (0, D.deepClone)(u), u[w] += T, u;
  }
  function fe(u, x) {
    var w = u.position, T = u.axisLabel.style, Y = j(w);
    T = (0, s.deepMerge)(Y, T);
    var re = x.map(function(ue) {
      var de = ue.position;
      return M(M({}, T), {}, {
        graphCenter: de
      });
    });
    return re;
  }
  function j(u) {
    if (u === "left")
      return {
        textAlign: "right",
        textBaseline: "middle"
      };
    if (u === "right")
      return {
        textAlign: "left",
        textBaseline: "middle"
      };
    if (u === "top")
      return {
        textAlign: "center",
        textBaseline: "bottom"
      };
    if (u === "bottom")
      return {
        textAlign: "center",
        textBaseline: "top"
      };
  }
  function k(u) {
    var x = u.animationCurve, w = u.animationFrame, T = u.rLevel;
    return [{
      name: "text",
      index: T,
      animationCurve: x,
      animationFrame: w,
      shape: S(u),
      style: B(u)
    }];
  }
  function S(u) {
    var x = u.name, w = u.namePosition;
    return {
      content: x,
      position: w
    };
  }
  function B(u) {
    var x = u.nameLocation, w = u.position, T = u.nameTextStyle, Y = H(w, x);
    return (0, s.deepMerge)(Y, T);
  }
  function H(u, x) {
    if (u === "top" && x === "start" || u === "bottom" && x === "start" || u === "left" && x === "center")
      return {
        textAlign: "right",
        textBaseline: "middle"
      };
    if (u === "top" && x === "end" || u === "bottom" && x === "end" || u === "right" && x === "center")
      return {
        textAlign: "left",
        textBaseline: "middle"
      };
    if (u === "top" && x === "center" || u === "left" && x === "end" || u === "right" && x === "end")
      return {
        textAlign: "center",
        textBaseline: "bottom"
      };
    if (u === "bottom" && x === "center" || u === "left" && x === "start" || u === "right" && x === "start")
      return {
        textAlign: "center",
        textBaseline: "top"
      };
  }
  function te(u) {
    var x = u.animationCurve, w = u.animationFrame, T = u.rLevel, Y = p(u), re = z(u);
    return Y.map(function(ue) {
      return {
        name: "polyline",
        index: T,
        visible: u.splitLine.show,
        animationCurve: x,
        animationFrame: w,
        shape: ue,
        style: re
      };
    });
  }
  function p(u) {
    var x = u.splitLinePosition;
    return x.map(function(w) {
      return {
        points: w
      };
    });
  }
  function z(u) {
    return u.splitLine.style;
  }
  return Bt;
}
var Ft = {};
var Ta;
function Lu() {
  if (Ta)
    return Ft;
  Ta = 1;
  var e = we;
  Object.defineProperty(Ft, "__esModule", {
    value: true
  }), Ft.line = A;
  var t = e(ze()), r = e(Ne()), n = e(Be()), a = e(Ue()), o = Qe(), l = Xe(), s = e(Gn()), D = qe();
  function W(Q, ie) {
    var ce = Object.keys(Q);
    if (Object.getOwnPropertySymbols) {
      var fe = Object.getOwnPropertySymbols(Q);
      ie && (fe = fe.filter(function(j) {
        return Object.getOwnPropertyDescriptor(Q, j).enumerable;
      })), ce.push.apply(ce, fe);
    }
    return ce;
  }
  function M(Q) {
    for (var ie = 1; ie < arguments.length; ie++) {
      var ce = arguments[ie] != null ? arguments[ie] : {};
      ie % 2 ? W(Object(ce), true).forEach(function(fe) {
        (0, a.default)(Q, fe, ce[fe]);
      }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(Q, Object.getOwnPropertyDescriptors(ce)) : W(Object(ce)).forEach(function(fe) {
        Object.defineProperty(Q, fe, Object.getOwnPropertyDescriptor(ce, fe));
      });
    }
    return Q;
  }
  var U = s.default.polylineToBezierCurve, F = s.default.getBezierCurveLength;
  function A(Q) {
    var ie = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, ce = ie.xAxis, fe = ie.yAxis, j = ie.series, k = [];
    ce && fe && j && (k = (0, D.initNeedSeries)(j, l.lineConfig, "line"), k = v(k, Q)), (0, o.doUpdate)({
      chart: Q,
      series: k,
      key: "lineArea",
      getGraphConfig: b,
      getStartGraphConfig: m,
      beforeUpdate: C,
      beforeChange: d
    }), (0, o.doUpdate)({
      chart: Q,
      series: k,
      key: "line",
      getGraphConfig: G,
      getStartGraphConfig: X,
      beforeUpdate: C,
      beforeChange: d
    }), (0, o.doUpdate)({
      chart: Q,
      series: k,
      key: "linePoint",
      getGraphConfig: Z,
      getStartGraphConfig: h2
    }), (0, o.doUpdate)({
      chart: Q,
      series: k,
      key: "lineLabel",
      getGraphConfig: P
    });
  }
  function v(Q, ie) {
    var ce = ie.axisData;
    return Q.map(function(fe) {
      var j = (0, D.mergeSameStackData)(fe, Q);
      j = R(fe, j);
      var k = N(fe, ce), S = I(j, k), B = E(k);
      return M(M({}, fe), {}, {
        linePosition: S.filter(function(H) {
          return H;
        }),
        lineFillBottomPos: B
      });
    });
  }
  function R(Q, ie) {
    var ce = Q.data;
    return ie.map(function(fe, j) {
      return typeof ce[j] == "number" ? fe : null;
    });
  }
  function N(Q, ie) {
    var ce = Q.xAxisIndex, fe = Q.yAxisIndex, j = ie.find(function(S) {
      var B = S.axis, H = S.index;
      return B === "x" && H === ce;
    }), k = ie.find(function(S) {
      var B = S.axis, H = S.index;
      return B === "y" && H === fe;
    });
    return [j, k];
  }
  function I(Q, ie) {
    var ce = ie.findIndex(function(re) {
      var ue = re.data;
      return ue === "value";
    }), fe = ie[ce], j = ie[1 - ce], k = fe.linePosition, S = fe.axis, B = j.tickPosition, H = B.length, te = S === "x" ? 0 : 1, p = k[0][te], z = k[1][te], u = z - p, x = fe.maxValue, w = fe.minValue, T = x - w, Y = new Array(H).fill(0).map(function(re, ue) {
      var de = Q[ue];
      if (typeof de != "number")
        return null;
      var he = (de - w) / T;
      return T === 0 && (he = 0), he * u + p;
    });
    return Y.map(function(re, ue) {
      if (ue >= H || typeof re != "number")
        return null;
      var de = [re, B[ue][1 - te]];
      return te === 0 || de.reverse(), de;
    });
  }
  function E(Q) {
    var ie = Q.find(function(z) {
      var u = z.data;
      return u === "value";
    }), ce = ie.axis, fe = ie.linePosition, j = ie.minValue, k = ie.maxValue, S = ce === "x" ? 0 : 1, B = fe[0][S];
    if (j < 0 && k > 0) {
      var H = k - j, te = Math.abs(fe[0][S] - fe[1][S]), p = Math.abs(j) / H * te;
      ce === "y" && (p *= -1), B += p;
    }
    return {
      changeIndex: S,
      changeValue: B
    };
  }
  function b(Q) {
    var ie = Q.animationCurve, ce = Q.animationFrame, fe = Q.lineFillBottomPos, j = Q.rLevel;
    return [{
      name: L(Q),
      index: j,
      animationCurve: ie,
      animationFrame: ce,
      visible: Q.lineArea.show,
      lineFillBottomPos: fe,
      shape: $(Q),
      style: f(Q),
      drawed: O
    }];
  }
  function $(Q) {
    var ie = Q.linePosition;
    return {
      points: ie
    };
  }
  function f(Q) {
    var ie = Q.lineArea, ce = Q.color, fe = ie.gradient, j = ie.style, k = [j.fill || ce], S = (0, D.deepMerge)(k, fe);
    S.length === 1 && S.push(S[0]);
    var B = _(Q);
    return j = M(M({}, j), {}, {
      stroke: "rgba(0, 0, 0, 0)"
    }), (0, D.deepMerge)({
      gradientColor: S,
      gradientParams: B,
      gradientType: "linear",
      gradientWith: "fill"
    }, j);
  }
  function _(Q) {
    var ie = Q.lineFillBottomPos, ce = Q.linePosition, fe = ie.changeIndex, j = ie.changeValue, k = ce.map(function(te) {
      return te[fe];
    }), S = Math.max.apply(Math, (0, n.default)(k)), B = Math.min.apply(Math, (0, n.default)(k)), H = S;
    return fe === 1 && (H = B), fe === 1 ? [0, H, 0, j] : [H, 0, j, 0];
  }
  function O(Q, ie) {
    var ce = Q.lineFillBottomPos, fe = Q.shape, j = ie.ctx, k = fe.points, S = ce.changeIndex, B = ce.changeValue, H = (0, n.default)(k[k.length - 1]), te = (0, n.default)(k[0]);
    H[S] = B, te[S] = B, j.lineTo.apply(j, (0, n.default)(H)), j.lineTo.apply(j, (0, n.default)(te)), j.closePath(), j.fill();
  }
  function m(Q) {
    var ie = b(Q)[0], ce = M({}, ie.style);
    return ce.opacity = 0, ie.style = ce, [ie];
  }
  function C(Q, ie, ce, fe) {
    var j = Q[ce];
    if (j) {
      var k = L(ie), S = fe.chart.render, B = j[0].name, H = k !== B;
      H && (j.forEach(function(te) {
        return S.delGraph(te);
      }), Q[ce] = null);
    }
  }
  function d(Q, ie) {
    var ce = ie.shape.points, fe = Q.shape.points, j = fe.length, k = ce.length;
    if (k > j) {
      var S = fe.slice(-1)[0], B = new Array(k - j).fill(0).map(function(H) {
        return (0, n.default)(S);
      });
      fe.push.apply(fe, (0, n.default)(B));
    } else
      k < j && fe.splice(k);
  }
  function G(Q) {
    var ie = Q.animationCurve, ce = Q.animationFrame, fe = Q.rLevel;
    return [{
      name: L(Q),
      index: fe + 1,
      animationCurve: ie,
      animationFrame: ce,
      shape: $(Q),
      style: g(Q)
    }];
  }
  function L(Q) {
    var ie = Q.smooth;
    return ie ? "smoothline" : "polyline";
  }
  function g(Q) {
    var ie = Q.lineStyle, ce = Q.color, fe = Q.smooth, j = Q.linePosition, k = V(j, fe);
    return (0, D.deepMerge)({
      stroke: ce,
      lineDash: [k, 0]
    }, ie);
  }
  function V(Q) {
    var ie = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;
    if (!ie)
      return (0, D.getPolylineLength)(Q);
    var ce = U(Q);
    return F(ce);
  }
  function X(Q) {
    var ie = Q.lineStyle.lineDash, ce = G(Q)[0], fe = ce.style.lineDash;
    return ie ? fe = [0, 0] : fe = (0, n.default)(fe).reverse(), ce.style.lineDash = fe, [ce];
  }
  function Z(Q) {
    var ie = Q.animationCurve, ce = Q.animationFrame, fe = Q.rLevel, j = c(Q), k = y(Q);
    return j.map(function(S) {
      return {
        name: "circle",
        index: fe + 2,
        visible: Q.linePoint.show,
        animationCurve: ie,
        animationFrame: ce,
        shape: S,
        style: k
      };
    });
  }
  function c(Q) {
    var ie = Q.linePosition, ce = Q.linePoint.radius;
    return ie.map(function(fe) {
      var j = (0, r.default)(fe, 2), k = j[0], S = j[1];
      return {
        r: ce,
        rx: k,
        ry: S
      };
    });
  }
  function y(Q) {
    var ie = Q.color, ce = Q.linePoint.style;
    return (0, D.deepMerge)({
      stroke: ie
    }, ce);
  }
  function h2(Q) {
    var ie = Z(Q);
    return ie.forEach(function(ce) {
      ce.shape.r = 0.1;
    }), ie;
  }
  function P(Q) {
    var ie = Q.animationCurve, ce = Q.animationFrame, fe = Q.rLevel, j = q(Q), k = ve(Q);
    return j.map(function(S, B) {
      return {
        name: "text",
        index: fe + 3,
        visible: Q.label.show,
        animationCurve: ie,
        animationFrame: ce,
        shape: S,
        style: k
      };
    });
  }
  function q(Q) {
    var ie = oe(Q), ce = K(Q);
    return ie.map(function(fe, j) {
      return {
        content: fe,
        position: ce[j]
      };
    });
  }
  function K(Q) {
    var ie = Q.linePosition, ce = Q.lineFillBottomPos, fe = Q.label, j = fe.position, k = fe.offset, S = ce.changeIndex, B = ce.changeValue;
    return ie.map(function(H) {
      if (j === "bottom" && (H = (0, n.default)(H), H[S] = B), j === "center") {
        var te = (0, n.default)(H);
        te[S] = B, H = ae(H, te);
      }
      return ee(H, k);
    });
  }
  function ee(Q, ie) {
    var ce = (0, r.default)(Q, 2), fe = ce[0], j = ce[1], k = (0, r.default)(ie, 2), S = k[0], B = k[1];
    return [fe + S, j + B];
  }
  function ae(Q, ie) {
    var ce = (0, r.default)(Q, 2), fe = ce[0], j = ce[1], k = (0, r.default)(ie, 2), S = k[0], B = k[1];
    return [(fe + S) / 2, (j + B) / 2];
  }
  function oe(Q) {
    var ie = Q.data, ce = Q.label.formatter;
    if (ie = ie.filter(function(j) {
      return typeof j == "number";
    }).map(function(j) {
      return j.toString();
    }), !ce)
      return ie;
    var fe = (0, t.default)(ce);
    return fe === "string" ? ie.map(function(j) {
      return ce.replace("{value}", j);
    }) : fe === "function" ? ie.map(function(j, k) {
      return ce({
        value: j,
        index: k
      });
    }) : ie;
  }
  function ve(Q) {
    var ie = Q.color, ce = Q.label.style;
    return (0, D.deepMerge)({
      fill: ie
    }, ce);
  }
  return Ft;
}
var Nt = {};
var Da;
function Su() {
  if (Da)
    return Nt;
  Da = 1;
  var e = we;
  Object.defineProperty(Nt, "__esModule", {
    value: true
  }), Nt.bar = U;
  var t = e(ze()), r = e(Ue()), n = e(Ne()), a = e(Be()), o = Qe(), l = Xe(), s = Fe(), D = qe();
  function W(p, z) {
    var u = Object.keys(p);
    if (Object.getOwnPropertySymbols) {
      var x = Object.getOwnPropertySymbols(p);
      z && (x = x.filter(function(w) {
        return Object.getOwnPropertyDescriptor(p, w).enumerable;
      })), u.push.apply(u, x);
    }
    return u;
  }
  function M(p) {
    for (var z = 1; z < arguments.length; z++) {
      var u = arguments[z] != null ? arguments[z] : {};
      z % 2 ? W(Object(u), true).forEach(function(x) {
        (0, r.default)(p, x, u[x]);
      }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(p, Object.getOwnPropertyDescriptors(u)) : W(Object(u)).forEach(function(x) {
        Object.defineProperty(p, x, Object.getOwnPropertyDescriptor(u, x));
      });
    }
    return p;
  }
  function U(p) {
    var z = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, u = z.xAxis, x = z.yAxis, w = z.series, T = [];
    u && x && w && (T = (0, D.initNeedSeries)(w, l.barConfig, "bar"), T = F(T, p), T = A(T), T = O(T)), (0, o.doUpdate)({
      chart: p,
      series: T.slice(-1),
      key: "backgroundBar",
      getGraphConfig: V
    }), T.reverse(), (0, o.doUpdate)({
      chart: p,
      series: T,
      key: "bar",
      getGraphConfig: y,
      getStartGraphConfig: ve,
      beforeUpdate: fe
    }), (0, o.doUpdate)({
      chart: p,
      series: T,
      key: "barLabel",
      getGraphConfig: j
    });
  }
  function F(p, z) {
    var u = z.axisData;
    return p.forEach(function(x) {
      var w = x.xAxisIndex, T = x.yAxisIndex;
      typeof w != "number" && (w = 0), typeof T != "number" && (T = 0);
      var Y = u.find(function(he) {
        var pe = he.axis, ge = he.index;
        return "".concat(pe).concat(ge) === "x".concat(w);
      }), re = u.find(function(he) {
        var pe = he.axis, ge = he.index;
        return "".concat(pe).concat(ge) === "y".concat(T);
      }), ue = [Y, re], de = ue.findIndex(function(he) {
        var pe = he.data;
        return pe === "value";
      });
      x.valueAxis = ue[de], x.labelAxis = ue[1 - de];
    }), p;
  }
  function A(p, z) {
    var u = R(p);
    return u.forEach(function(x) {
      v(x), I(x), E(x), b(x), _(x);
    }), p;
  }
  function v(p) {
    var z = N(p);
    z = z.map(function(x) {
      return {
        stack: x,
        index: -1
      };
    });
    var u = 0;
    p.forEach(function(x) {
      var w = x.stack;
      if (!w)
        x.barIndex = u, u++;
      else {
        var T = z.find(function(Y) {
          var re = Y.stack;
          return re === w;
        });
        T.index === -1 && (T.index = u, u++), x.barIndex = T.index;
      }
    });
  }
  function R(p) {
    var z = p.map(function(u) {
      var x = u.labelAxis, w = x.axis, T = x.index;
      return w + T;
    });
    return z = (0, a.default)(new Set(z)), z.map(function(u) {
      return p.filter(function(x) {
        var w = x.labelAxis, T = w.axis, Y = w.index;
        return T + Y === u;
      });
    });
  }
  function N(p) {
    var z = [];
    return p.forEach(function(u) {
      var x = u.stack;
      x && z.push(x);
    }), (0, a.default)(new Set(z));
  }
  function I(p) {
    var z = (0, a.default)(new Set(p.map(function(u) {
      var x = u.barIndex;
      return x;
    }))).length;
    p.forEach(function(u) {
      return u.barNum = z;
    });
  }
  function E(p) {
    var z = p.slice(-1)[0], u = z.barCategoryGap, x = z.labelAxis.tickGap, w = 0;
    typeof u == "number" ? w = u : w = (1 - parseInt(u) / 100) * x, p.forEach(function(T) {
      return T.barCategoryWidth = w;
    });
  }
  function b(p) {
    var z = p.slice(-1)[0], u = z.barCategoryWidth, x = z.barWidth, w = z.barGap, T = z.barNum, Y = [];
    typeof x == "number" || x !== "auto" ? Y = $(u, x, w) : x === "auto" && (Y = f(u, x, w, T));
    var re = Y, ue = (0, n.default)(re, 2), de = ue[0], he = ue[1];
    p.forEach(function(pe) {
      pe.barWidth = de, pe.barGap = he;
    });
  }
  function $(p, z, u) {
    var x = 0, w = 0;
    return typeof z == "number" ? x = z : x = parseInt(z) / 100 * p, typeof u == "number" ? w = u : w = parseInt(u) / 100 * x, [x, w];
  }
  function f(p, z, u, x) {
    var w = 0, T = 0, Y = p / x;
    if (typeof u == "number")
      T = u, w = Y - T;
    else {
      var re = 10 + parseInt(u) / 10;
      re === 0 ? (w = Y * 2, T = -w) : (w = Y / re * 10, T = Y - w);
    }
    return [w, T];
  }
  function _(p) {
    var z = p.slice(-1)[0], u = z.barGap, x = z.barWidth, w = z.barNum, T = (u + x) * w - u;
    p.forEach(function(Y) {
      return Y.barAllWidthAndGap = T;
    });
  }
  function O(p, z) {
    return p = C(p), p = m(p), p = G(p), p = L(p), p;
  }
  function m(p) {
    return p.map(function(z) {
      var u = z.labelAxis, x = z.barAllWidthAndGap, w = z.barGap, T = z.barWidth, Y = z.barIndex, re = u.tickGap, ue = u.tickPosition, de = u.axis, he = de === "x" ? 0 : 1, pe = ue.map(function(ge, ye) {
        var Ge = ue[ye][he] - re / 2, Ke = Ge + (re - x) / 2;
        return Ke + (Y + 0.5) * T + Y * w;
      });
      return M(M({}, z), {}, {
        barLabelAxisPos: pe
      });
    });
  }
  function C(p) {
    return p.map(function(z) {
      var u = (0, D.mergeSameStackData)(z, p);
      u = d(z, u);
      var x = z.valueAxis, w = x.axis, T = x.minValue, Y = x.maxValue, re = x.linePosition, ue = g(T, Y, T < 0 ? 0 : T, re, w), de = u.map(function(pe) {
        return g(T, Y, pe, re, w);
      }), he = de.map(function(pe) {
        return [ue, pe];
      });
      return M(M({}, z), {}, {
        barValueAxisPos: he
      });
    });
  }
  function d(p, z) {
    var u = p.data;
    return z.map(function(x, w) {
      return typeof u[w] == "number" ? x : null;
    }).filter(function(x) {
      return x !== null;
    });
  }
  function G(p) {
    return p.map(function(z) {
      var u = z.barLabelAxisPos, x = z.data;
      return x.forEach(function(w, T) {
        typeof w != "number" && (u[T] = null);
      }), M(M({}, z), {}, {
        barLabelAxisPos: u.filter(function(w) {
          return w !== null;
        })
      });
    });
  }
  function L(p) {
    return p.forEach(function(z) {
      var u = z.data, x = z.barLabelAxisPos, w = z.barValueAxisPos, T = u.filter(function(re) {
        return typeof re == "number";
      }).length, Y = x.length;
      Y > T && (x.splice(T), w.splice(T));
    }), p;
  }
  function g(p, z, u, x, w) {
    if (typeof u != "number")
      return null;
    var T = z - p, Y = w === "x" ? 0 : 1, re = x[1][Y] - x[0][Y], ue = (u - p) / T;
    T === 0 && (ue = 0);
    var de = ue * re;
    return de + x[0][Y];
  }
  function V(p) {
    var z = p.animationCurve, u = p.animationFrame, x = p.rLevel, w = X(p), T = c(p);
    return w.map(function(Y) {
      return {
        name: "rect",
        index: x,
        visible: p.backgroundBar.show,
        animationCurve: z,
        animationFrame: u,
        shape: Y,
        style: T
      };
    });
  }
  function X(p) {
    var z = p.labelAxis, u = p.valueAxis, x = z.tickPosition, w = u.axis, T = u.linePosition, Y = Z(p), re = Y / 2, ue = w === "x" ? 0 : 1, de = x.map(function(ye) {
      return ye[1 - ue];
    }), he = [T[0][ue], T[1][ue]], pe = he[0], ge = he[1];
    return de.map(function(ye) {
      return w === "x" ? {
        x: pe,
        y: ye - re,
        w: ge - pe,
        h: Y
      } : {
        x: ye - re,
        y: ge,
        w: Y,
        h: pe - ge
      };
    });
  }
  function Z(p) {
    var z = p.barAllWidthAndGap, u = p.barCategoryWidth, x = p.backgroundBar, w = x.width;
    return typeof w == "number" ? w : w === "auto" ? z : parseInt(w) / 100 * u;
  }
  function c(p) {
    return p.backgroundBar.style;
  }
  function y(p) {
    var z = p.barLabelAxisPos, u = p.animationCurve, x = p.animationFrame, w = p.rLevel, T = h2(p);
    return z.map(function(Y, re) {
      return {
        name: T,
        index: w,
        animationCurve: u,
        animationFrame: x,
        shape: P(p, re),
        style: ae(p, re)
      };
    });
  }
  function h2(p) {
    var z = p.shapeType;
    return z === "leftEchelon" || z === "rightEchelon" ? "polyline" : "rect";
  }
  function P(p, z) {
    var u = p.shapeType;
    return u === "leftEchelon" ? q(p, z) : u === "rightEchelon" ? K(p, z) : ee(p, z);
  }
  function q(p, z) {
    var u = p.barValueAxisPos, x = p.barLabelAxisPos, w = p.barWidth, T = p.echelonOffset, Y = (0, n.default)(u[z], 2), re = Y[0], ue = Y[1], de = x[z], he = w / 2, pe = p.valueAxis.axis, ge = [];
    return pe === "x" ? (ge[0] = [ue, de - he], ge[1] = [ue, de + he], ge[2] = [re, de + he], ge[3] = [re + T, de - he], ue - re < T && ge.splice(3, 1)) : (ge[0] = [de - he, ue], ge[1] = [de + he, ue], ge[2] = [de + he, re], ge[3] = [de - he, re - T], re - ue < T && ge.splice(3, 1)), {
      points: ge,
      close: true
    };
  }
  function K(p, z) {
    var u = p.barValueAxisPos, x = p.barLabelAxisPos, w = p.barWidth, T = p.echelonOffset, Y = (0, n.default)(u[z], 2), re = Y[0], ue = Y[1], de = x[z], he = w / 2, pe = p.valueAxis.axis, ge = [];
    return pe === "x" ? (ge[0] = [ue, de + he], ge[1] = [ue, de - he], ge[2] = [re, de - he], ge[3] = [re + T, de + he], ue - re < T && ge.splice(2, 1)) : (ge[0] = [de + he, ue], ge[1] = [de - he, ue], ge[2] = [de - he, re], ge[3] = [de + he, re - T], re - ue < T && ge.splice(2, 1)), {
      points: ge,
      close: true
    };
  }
  function ee(p, z) {
    var u = p.barValueAxisPos, x = p.barLabelAxisPos, w = p.barWidth, T = (0, n.default)(u[z], 2), Y = T[0], re = T[1], ue = x[z], de = p.valueAxis.axis, he = {};
    return de === "x" ? (he.x = Y, he.y = ue - w / 2, he.w = re - Y, he.h = w) : (he.x = ue - w / 2, he.y = re, he.w = w, he.h = Y - re), he;
  }
  function ae(p, z) {
    var u = p.barStyle, x = p.gradient, w = p.color, T = p.independentColor, Y = p.independentColors, re = [u.fill || w], ue = (0, D.deepMerge)(re, x.color);
    if (T) {
      var de = Y[z % Y.length];
      ue = de instanceof Array ? de : [de];
    }
    ue.length === 1 && ue.push(ue[0]);
    var he = oe(p, z);
    return (0, D.deepMerge)({
      gradientColor: ue,
      gradientParams: he,
      gradientType: "linear",
      gradientWith: "fill"
    }, u);
  }
  function oe(p, z) {
    var u = p.barValueAxisPos, x = p.barLabelAxisPos, w = p.data, T = p.valueAxis, Y = T.linePosition, re = T.axis, ue = (0, n.default)(u[z], 2), de = ue[0], he = ue[1], pe = x[z], ge = w[z], ye = (0, n.default)(Y, 2), Ge = ye[0], Ke = ye[1], et = re === "x" ? 0 : 1, Je = he;
    return p.gradient.local || (Je = ge < 0 ? Ge[et] : Ke[et]), re === "y" ? [pe, Je, pe, de] : [Je, pe, de, pe];
  }
  function ve(p) {
    var z = y(p), u = p.shapeType;
    return z.forEach(function(x) {
      var w = x.shape;
      u === "leftEchelon" ? w = Q(w, p) : u === "rightEchelon" ? w = ie(w, p) : w = ce(w, p), x.shape = w;
    }), z;
  }
  function Q(p, z) {
    var u = z.valueAxis.axis;
    p = (0, s.deepClone)(p);
    var x = p, w = x.points, T = u === "x" ? 0 : 1, Y = w[2][T];
    return w.forEach(function(re) {
      return re[T] = Y;
    }), p;
  }
  function ie(p, z) {
    var u = z.valueAxis.axis;
    p = (0, s.deepClone)(p);
    var x = p, w = x.points, T = u === "x" ? 0 : 1, Y = w[2][T];
    return w.forEach(function(re) {
      return re[T] = Y;
    }), p;
  }
  function ce(p, z) {
    var u = z.valueAxis.axis, x = p.x, w = p.y, T = p.w, Y = p.h;
    return u === "x" ? T = 0 : (w = w + Y, Y = 0), {
      x,
      y: w,
      w: T,
      h: Y
    };
  }
  function fe(p, z, u, x) {
    var w = x.chart.render, T = h2(z);
    p[u] && p[u][0].name !== T && (p[u].forEach(function(Y) {
      return w.delGraph(Y);
    }), p[u] = null);
  }
  function j(p) {
    var z = p.animationCurve, u = p.animationFrame, x = p.rLevel, w = k(p), T = te(p);
    return w.map(function(Y) {
      return {
        name: "text",
        index: x,
        visible: p.label.show,
        animationCurve: z,
        animationFrame: u,
        shape: Y,
        style: T
      };
    });
  }
  function k(p) {
    var z = S(p), u = B(p);
    return u.map(function(x, w) {
      return {
        position: x,
        content: z[w]
      };
    });
  }
  function S(p) {
    var z = p.data, u = p.label, x = u.formatter;
    if (z = z.filter(function(T) {
      return typeof T == "number";
    }).map(function(T) {
      return T.toString();
    }), !x)
      return z;
    var w = (0, t.default)(x);
    return w === "string" ? z.map(function(T) {
      return x.replace("{value}", T);
    }) : w === "function" ? z.map(function(T, Y) {
      return x({
        value: T,
        index: Y
      });
    }) : z;
  }
  function B(p) {
    var z = p.label, u = p.barValueAxisPos, x = p.barLabelAxisPos, w = z.position, T = z.offset, Y = p.valueAxis.axis;
    return u.map(function(re, ue) {
      var de = (0, n.default)(re, 2), he = de[0], pe = de[1], ge = x[ue], ye = [pe, ge];
      return w === "bottom" && (ye = [he, ge]), w === "center" && (ye = [(he + pe) / 2, ge]), Y === "y" && ye.reverse(), H(ye, T);
    });
  }
  function H(p, z) {
    var u = (0, n.default)(p, 2), x = u[0], w = u[1], T = (0, n.default)(z, 2), Y = T[0], re = T[1];
    return [x + Y, w + re];
  }
  function te(p) {
    var z = p.color, u = p.label.style, x = p.gradient.color;
    return x.length && (z = x[0]), u = (0, D.deepMerge)({
      fill: z
    }, u), u;
  }
  return Nt;
}
var jt = {};
var Ba;
function Ou() {
  if (Ba)
    return jt;
  Ba = 1;
  var e = we;
  Object.defineProperty(jt, "__esModule", {
    value: true
  }), jt.pie = U;
  var t = e(Ue()), r = e(ze()), n = e(Ne()), a = e(Be()), o = Qe(), l = mi(), s = Fe(), D = qe();
  function W(k, S) {
    var B = Object.keys(k);
    if (Object.getOwnPropertySymbols) {
      var H = Object.getOwnPropertySymbols(k);
      S && (H = H.filter(function(te) {
        return Object.getOwnPropertyDescriptor(k, te).enumerable;
      })), B.push.apply(B, H);
    }
    return B;
  }
  function M(k) {
    for (var S = 1; S < arguments.length; S++) {
      var B = arguments[S] != null ? arguments[S] : {};
      S % 2 ? W(Object(B), true).forEach(function(H) {
        (0, t.default)(k, H, B[H]);
      }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(k, Object.getOwnPropertyDescriptors(B)) : W(Object(B)).forEach(function(H) {
        Object.defineProperty(k, H, Object.getOwnPropertyDescriptor(B, H));
      });
    }
    return k;
  }
  function U(k) {
    var S = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, B = S.series;
    B || (B = []);
    var H = (0, D.initNeedSeries)(B, l.pieConfig, "pie");
    H = F(H, k), H = A(H, k), H = R(H), H = E(H), H = f(H), H = O(H), H = C(H), H = d(H), (0, o.doUpdate)({
      chart: k,
      series: H,
      key: "pie",
      getGraphConfig: Z,
      getStartGraphConfig: c,
      beforeChange: y
    }), (0, o.doUpdate)({
      chart: k,
      series: H,
      key: "pieInsideLabel",
      getGraphConfig: q
    }), (0, o.doUpdate)({
      chart: k,
      series: H,
      key: "pieOutsideLabelLine",
      getGraphConfig: ae,
      getStartGraphConfig: oe
    }), (0, o.doUpdate)({
      chart: k,
      series: H,
      key: "pieOutsideLabel",
      getGraphConfig: ie,
      getStartGraphConfig: ce
    });
  }
  function F(k, S) {
    var B = S.render.area;
    return k.forEach(function(H) {
      var te = H.center;
      te = te.map(function(p, z) {
        return typeof p == "number" ? p : parseInt(p) / 100 * B[z];
      }), H.center = te;
    }), k;
  }
  function A(k, S) {
    var B = Math.min.apply(Math, (0, a.default)(S.render.area)) / 2;
    return k.forEach(function(H) {
      var te = H.radius, p = H.data;
      te = v(te, B), p.forEach(function(z) {
        var u = z.radius;
        u || (u = te), u = v(u, B), z.radius = u;
      }), H.radius = te;
    }), k;
  }
  function v(k, S) {
    return k instanceof Array || (k = [0, k]), k = k.map(function(B) {
      return typeof B == "number" ? B : parseInt(B) / 100 * S;
    }), k;
  }
  function R(k, S) {
    var B = k.filter(function(H) {
      var te = H.roseType;
      return te;
    });
    return B.forEach(function(H) {
      var te = H.radius, p = H.data, z = H.roseSort, u = I(H), x = (0, a.default)(p);
      p = N(p), p.forEach(function(w, T) {
        w.radius[1] = te[1] - u * T;
      }), z ? p.reverse() : H.data = x, H.roseIncrement = u;
    }), k;
  }
  function N(k) {
    return k.sort(function(S, B) {
      var H = S.value, te = B.value;
      if (H === te)
        return 0;
      if (H > te)
        return -1;
      if (H < te)
        return 1;
    });
  }
  function I(k) {
    var S = k.radius, B = k.roseIncrement;
    if (typeof B == "number")
      return B;
    if (B === "auto") {
      var H = k.data, te = H.reduce(function(u, x) {
        var w = x.radius;
        return [].concat((0, a.default)(u), (0, a.default)(w));
      }, []), p = Math.min.apply(Math, (0, a.default)(te)), z = Math.max.apply(Math, (0, a.default)(te));
      return (z - p) * 0.6 / (H.length - 1 || 1);
    }
    return parseInt(B) / 100 * S[1];
  }
  function E(k) {
    return k.forEach(function(S) {
      var B = S.data, H = S.percentToFixed, te = $(B);
      B.forEach(function(z) {
        var u = z.value;
        z.percent = u / te * 100, z.percentForLabel = b(u / te * 100, H);
      });
      var p = (0, D.mulAdd)(B.slice(0, -1).map(function(z) {
        var u = z.percent;
        return u;
      }));
      B.slice(-1)[0].percent = 100 - p, B.slice(-1)[0].percentForLabel = b(100 - p, H);
    }), k;
  }
  function b(k) {
    var S = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 0, B = k.toString(), H = B.split("."), te = H[1] || "0", p = te.slice(0, S);
    return H[1] = p, parseFloat(H.join("."));
  }
  function $(k) {
    return (0, D.mulAdd)(k.map(function(S) {
      var B = S.value;
      return B;
    }));
  }
  function f(k) {
    return k.forEach(function(S) {
      var B = S.startAngle, H = S.data;
      H.forEach(function(te, p) {
        var z = _(H, p), u = (0, n.default)(z, 2), x = u[0], w = u[1];
        te.startAngle = B + x, te.endAngle = B + w;
      });
    }), k;
  }
  function _(k, S) {
    var B = Math.PI * 2, H = k.slice(0, S + 1), te = (0, D.mulAdd)(H.map(function(u) {
      var x = u.percent;
      return x;
    })), p = k[S].percent, z = te - p;
    return [B * z / 100, B * te / 100];
  }
  function O(k) {
    return k.forEach(function(S) {
      var B = S.data;
      B.forEach(function(H) {
        H.insideLabelPos = m(S, H);
      });
    }), k;
  }
  function m(k, S) {
    var B = k.center, H = S.startAngle, te = S.endAngle, p = (0, n.default)(S.radius, 2), z = p[0], u = p[1], x = (z + u) / 2, w = (H + te) / 2;
    return s.getCircleRadianPoint.apply(void 0, (0, a.default)(B).concat([x, w]));
  }
  function C(k) {
    return k.forEach(function(S) {
      var B = S.data, H = S.center;
      B.forEach(function(te) {
        var p = te.startAngle, z = te.endAngle, u = te.radius, x = (p + z) / 2, w = s.getCircleRadianPoint.apply(void 0, (0, a.default)(H).concat([u[1], x]));
        te.edgeCenterPos = w;
      });
    }), k;
  }
  function d(k) {
    return k.forEach(function(S) {
      var B = g(S), H = g(S, false);
      B = V(B), H = V(H), X(B, S), X(H, S, false);
    }), k;
  }
  function G(k) {
    var S = k.outsideLabel.labelLineBendGap, B = L(k);
    return typeof S != "number" && (S = parseInt(S) / 100 * B), S + B;
  }
  function L(k) {
    var S = k.data, B = S.map(function(H) {
      var te = (0, n.default)(H.radius, 2);
      te[0];
      var p = te[1];
      return p;
    });
    return Math.max.apply(Math, (0, a.default)(B));
  }
  function g(k) {
    var S = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true, B = k.data, H = k.center, te = H[0];
    return B.filter(function(p) {
      var z = p.edgeCenterPos, u = z[0];
      return S ? u <= te : u > te;
    });
  }
  function V(k) {
    return k.sort(function(S, B) {
      var H = (0, n.default)(S.edgeCenterPos, 2);
      H[0];
      var te = H[1], p = (0, n.default)(B.edgeCenterPos, 2);
      p[0];
      var z = p[1];
      if (te > z)
        return 1;
      if (te < z)
        return -1;
      if (te === z)
        return 0;
    }), k;
  }
  function X(k, S) {
    var B = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : true, H = S.center, te = S.outsideLabel, p = G(S);
    k.forEach(function(z) {
      var u = z.edgeCenterPos, x = z.startAngle, w = z.endAngle, T = te.labelLineEndLength, Y = (x + w) / 2, re = s.getCircleRadianPoint.apply(void 0, (0, a.default)(H).concat([p, Y])), ue = (0, a.default)(re);
      ue[0] += T * (B ? -1 : 1), z.labelLine = [u, re, ue], z.labelLineLength = (0, D.getPolylineLength)(z.labelLine), z.align = {
        textAlign: "left",
        textBaseline: "middle"
      }, B && (z.align.textAlign = "right");
    });
  }
  function Z(k) {
    var S = k.data, B = k.animationCurve, H = k.animationFrame, te = k.rLevel;
    return S.map(function(p, z) {
      return {
        name: "pie",
        index: te,
        animationCurve: B,
        animationFrame: H,
        shape: h2(k, z),
        style: P(k, z)
      };
    });
  }
  function c(k) {
    var S = k.animationDelayGap, B = k.startAnimationCurve, H = Z(k);
    return H.forEach(function(te, p) {
      te.animationCurve = B, te.animationDelay = p * S, te.shape.or = te.shape.ir;
    }), H;
  }
  function y(k) {
    k.animationDelay = 0;
  }
  function h2(k, S) {
    var B = k.center, H = k.data, te = H[S], p = te.radius, z = te.startAngle, u = te.endAngle;
    return {
      startAngle: z,
      endAngle: u,
      ir: p[0],
      or: p[1],
      rx: B[0],
      ry: B[1]
    };
  }
  function P(k, S) {
    var B = k.pieStyle, H = k.data, te = H[S], p = te.color;
    return (0, D.deepMerge)({
      fill: p
    }, B);
  }
  function q(k) {
    var S = k.animationCurve, B = k.animationFrame, H = k.data, te = k.rLevel;
    return H.map(function(p, z) {
      return {
        name: "text",
        index: te,
        visible: k.insideLabel.show,
        animationCurve: S,
        animationFrame: B,
        shape: K(k, z),
        style: ee(k)
      };
    });
  }
  function K(k, S) {
    var B = k.insideLabel, H = k.data, te = B.formatter, p = H[S], z = (0, r.default)(te), u = "";
    return z === "string" && (u = te.replace("{name}", p.name), u = u.replace("{percent}", p.percentForLabel), u = u.replace("{value}", p.value)), z === "function" && (u = te(p)), {
      content: u,
      position: p.insideLabelPos
    };
  }
  function ee(k, S) {
    var B = k.insideLabel.style;
    return B;
  }
  function ae(k) {
    var S = k.animationCurve, B = k.animationFrame, H = k.data, te = k.rLevel;
    return H.map(function(p, z) {
      return {
        name: "polyline",
        index: te,
        visible: k.outsideLabel.show,
        animationCurve: S,
        animationFrame: B,
        shape: ve(k, z),
        style: Q(k, z)
      };
    });
  }
  function oe(k) {
    var S = k.data, B = ae(k);
    return B.forEach(function(H, te) {
      H.style.lineDash = [0, S[te].labelLineLength];
    }), B;
  }
  function ve(k, S) {
    var B = k.data, H = B[S];
    return {
      points: H.labelLine
    };
  }
  function Q(k, S) {
    var B = k.outsideLabel, H = k.data, te = B.labelLineStyle, p = H[S].color;
    return (0, D.deepMerge)({
      stroke: p,
      lineDash: [H[S].labelLineLength, 0]
    }, te);
  }
  function ie(k) {
    var S = k.animationCurve, B = k.animationFrame, H = k.data, te = k.rLevel;
    return H.map(function(p, z) {
      return {
        name: "text",
        index: te,
        visible: k.outsideLabel.show,
        animationCurve: S,
        animationFrame: B,
        shape: fe(k, z),
        style: j(k, z)
      };
    });
  }
  function ce(k) {
    var S = k.data, B = ie(k);
    return B.forEach(function(H, te) {
      H.shape.position = S[te].labelLine[1];
    }), B;
  }
  function fe(k, S) {
    var B = k.outsideLabel, H = k.data, te = B.formatter, p = H[S], z = p.labelLine, u = p.name, x = p.percentForLabel, w = p.value, T = (0, r.default)(te), Y = "";
    return T === "string" && (Y = te.replace("{name}", u), Y = Y.replace("{percent}", x), Y = Y.replace("{value}", w)), T === "function" && (Y = te(H[S])), {
      content: Y,
      position: z[2]
    };
  }
  function j(k, S) {
    var B = k.outsideLabel, H = k.data, te = H[S], p = te.color, z = te.align, u = B.style;
    return (0, D.deepMerge)(M({
      fill: p
    }, z), u);
  }
  return jt;
}
var Et = {};
var Fa;
function Gu() {
  if (Fa)
    return Et;
  Fa = 1;
  var e = we;
  Object.defineProperty(Et, "__esModule", {
    value: true
  }), Et.radarAxis = M;
  var t = e(Ne()), r = e(Ue()), n = e(Be()), a = Qe(), o = Xe(), l = Fe(), s = qe();
  function D(c, y) {
    var h2 = Object.keys(c);
    if (Object.getOwnPropertySymbols) {
      var P = Object.getOwnPropertySymbols(c);
      y && (P = P.filter(function(q) {
        return Object.getOwnPropertyDescriptor(c, q).enumerable;
      })), h2.push.apply(h2, P);
    }
    return h2;
  }
  function W(c) {
    for (var y = 1; y < arguments.length; y++) {
      var h2 = arguments[y] != null ? arguments[y] : {};
      y % 2 ? D(Object(h2), true).forEach(function(P) {
        (0, r.default)(c, P, h2[P]);
      }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(c, Object.getOwnPropertyDescriptors(h2)) : D(Object(h2)).forEach(function(P) {
        Object.defineProperty(c, P, Object.getOwnPropertyDescriptor(h2, P));
      });
    }
    return c;
  }
  function M(c) {
    var y = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, h2 = y.radar, P = [];
    h2 && (P = U(h2), P = F(P, c), P = A(P, c), P = v(P), P = R(P), P = N(P), P = [P]);
    var q = P;
    P.length && !P[0].show && (q = []), (0, a.doUpdate)({
      chart: c,
      series: q,
      key: "radarAxisSplitArea",
      getGraphConfig: I,
      beforeUpdate: $,
      beforeChange: f
    }), (0, a.doUpdate)({
      chart: c,
      series: q,
      key: "radarAxisSplitLine",
      getGraphConfig: _,
      beforeUpdate: C,
      beforeChange: d
    }), (0, a.doUpdate)({
      chart: c,
      series: q,
      key: "radarAxisLine",
      getGraphConfig: G
    }), (0, a.doUpdate)({
      chart: c,
      series: q,
      key: "radarAxisLable",
      getGraphConfig: V
    }), c.radarAxis = P[0];
  }
  function U(c) {
    return (0, s.deepMerge)((0, l.deepClone)(o.radarAxisConfig), c);
  }
  function F(c, y) {
    var h2 = y.render.area, P = c.center;
    return c.centerPos = P.map(function(q, K) {
      return typeof q == "number" ? q : parseInt(q) / 100 * h2[K];
    }), c;
  }
  function A(c, y) {
    var h2 = y.render.area, P = c.splitNum, q = c.radius, K = Math.min.apply(Math, (0, n.default)(h2)) / 2;
    typeof q != "number" && (q = parseInt(q) / 100 * K);
    var ee = q / P;
    return c.ringRadius = new Array(P).fill(0).map(function(ae, oe) {
      return ee * (oe + 1);
    }), c.radius = q, c;
  }
  function v(c) {
    var y = c.indicator, h2 = c.centerPos, P = c.radius, q = c.startAngle, K = Math.PI * 2, ee = y.length, ae = K / ee, oe = new Array(ee).fill(0).map(function(ve, Q) {
      return ae * Q + q;
    });
    return c.axisLineAngles = oe, c.axisLinePosition = oe.map(function(ve) {
      return l.getCircleRadianPoint.apply(void 0, (0, n.default)(h2).concat([P, ve]));
    }), c;
  }
  function R(c) {
    var y = c.ringRadius, h2 = y[0] / 2;
    return c.areaRadius = y.map(function(P) {
      return P - h2;
    }), c;
  }
  function N(c) {
    var y = c.axisLineAngles, h2 = c.centerPos, P = c.radius, q = c.axisLabel;
    return P += q.labelGap, c.axisLabelPosition = y.map(function(K) {
      return l.getCircleRadianPoint.apply(void 0, (0, n.default)(h2).concat([P, K]));
    }), c;
  }
  function I(c) {
    var y = c.areaRadius, h2 = c.polygon, P = c.animationCurve, q = c.animationFrame, K = c.rLevel, ee = h2 ? "regPolygon" : "ring";
    return y.map(function(ae, oe) {
      return {
        name: ee,
        index: K,
        visible: c.splitArea.show,
        animationCurve: P,
        animationFrame: q,
        shape: E(c, oe),
        style: b(c, oe)
      };
    });
  }
  function E(c, y) {
    var h2 = c.polygon, P = c.areaRadius, q = c.indicator, K = c.centerPos, ee = q.length, ae = {
      rx: K[0],
      ry: K[1],
      r: P[y]
    };
    return h2 && (ae.side = ee), ae;
  }
  function b(c, y) {
    var h2 = c.splitArea, P = c.ringRadius, q = c.axisLineAngles, K = c.polygon, ee = c.centerPos, ae = h2.color, oe = h2.style;
    oe = W({
      fill: "rgba(0, 0, 0, 0)"
    }, oe);
    var ve = P[0] - 0;
    if (K) {
      var Q = l.getCircleRadianPoint.apply(void 0, (0, n.default)(ee).concat([P[0], q[0]])), ie = l.getCircleRadianPoint.apply(void 0, (0, n.default)(ee).concat([P[0], q[1]]));
      ve = (0, s.getPointToLineDistance)(ee, Q, ie);
    }
    if (oe = (0, s.deepMerge)((0, l.deepClone)(oe, true), {
      lineWidth: ve
    }), !ae.length)
      return oe;
    var ce = ae.length;
    return (0, s.deepMerge)(oe, {
      stroke: ae[y % ce]
    });
  }
  function $(c, y, h2, P) {
    var q = c[h2];
    if (q) {
      var K = P.chart.render, ee = y.polygon, ae = q[0].name, oe = ee ? "regPolygon" : "ring", ve = oe !== ae;
      ve && (q.forEach(function(Q) {
        return K.delGraph(Q);
      }), c[h2] = null);
    }
  }
  function f(c, y) {
    var h2 = y.shape.side;
    typeof h2 == "number" && (c.shape.side = h2);
  }
  function _(c) {
    var y = c.ringRadius, h2 = c.polygon, P = c.animationCurve, q = c.animationFrame, K = c.rLevel, ee = h2 ? "regPolygon" : "ring";
    return y.map(function(ae, oe) {
      return {
        name: ee,
        index: K,
        animationCurve: P,
        animationFrame: q,
        visible: c.splitLine.show,
        shape: O(c, oe),
        style: m(c, oe)
      };
    });
  }
  function O(c, y) {
    var h2 = c.ringRadius, P = c.centerPos, q = c.indicator, K = c.polygon, ee = {
      rx: P[0],
      ry: P[1],
      r: h2[y]
    }, ae = q.length;
    return K && (ee.side = ae), ee;
  }
  function m(c, y) {
    var h2 = c.splitLine, P = h2.color, q = h2.style;
    if (q = W({
      fill: "rgba(0, 0, 0, 0)"
    }, q), !P.length)
      return q;
    var K = P.length;
    return (0, s.deepMerge)(q, {
      stroke: P[y % K]
    });
  }
  function C(c, y, h2, P) {
    var q = c[h2];
    if (q) {
      var K = P.chart.render, ee = y.polygon, ae = q[0].name, oe = ee ? "regPolygon" : "ring", ve = oe !== ae;
      ve && (q.forEach(function(Q) {
        return K.delGraph(Q);
      }), c[h2] = null);
    }
  }
  function d(c, y) {
    var h2 = y.shape.side;
    typeof h2 == "number" && (c.shape.side = h2);
  }
  function G(c) {
    var y = c.axisLinePosition, h2 = c.animationCurve, P = c.animationFrame, q = c.rLevel;
    return y.map(function(K, ee) {
      return {
        name: "polyline",
        index: q,
        visible: c.axisLine.show,
        animationCurve: h2,
        animationFrame: P,
        shape: L(c, ee),
        style: g(c, ee)
      };
    });
  }
  function L(c, y) {
    var h2 = c.centerPos, P = c.axisLinePosition, q = [h2, P[y]];
    return {
      points: q
    };
  }
  function g(c, y) {
    var h2 = c.axisLine, P = h2.color, q = h2.style;
    if (!P.length)
      return q;
    var K = P.length;
    return (0, s.deepMerge)(q, {
      stroke: P[y % K]
    });
  }
  function V(c) {
    var y = c.axisLabelPosition, h2 = c.animationCurve, P = c.animationFrame, q = c.rLevel;
    return y.map(function(K, ee) {
      return {
        name: "text",
        index: q,
        visible: c.axisLabel.show,
        animationCurve: h2,
        animationFrame: P,
        shape: X(c, ee),
        style: Z(c, ee)
      };
    });
  }
  function X(c, y) {
    var h2 = c.axisLabelPosition, P = c.indicator;
    return {
      content: P[y].name,
      position: h2[y]
    };
  }
  function Z(c, y) {
    var h2 = c.axisLabel, P = (0, t.default)(c.centerPos, 2), q = P[0], K = P[1], ee = c.axisLabelPosition, ae = h2.color, oe = h2.style, ve = (0, t.default)(ee[y], 2), Q = ve[0], ie = ve[1], ce = Q > q ? "left" : "right", fe = ie > K ? "top" : "bottom";
    if (oe = (0, s.deepMerge)({
      textAlign: ce,
      textBaseline: fe
    }, oe), !ae.length)
      return oe;
    var j = ae.length;
    return (0, s.deepMerge)(oe, {
      fill: ae[y % j]
    });
  }
  return Et;
}
var Wt = {};
var Na;
function Mu() {
  if (Na)
    return Wt;
  Na = 1;
  var e = we;
  Object.defineProperty(Wt, "__esModule", {
    value: true
  }), Wt.radar = F;
  var t = e(Ue()), r = e(ze()), n = e(Ne()), a = e(Be()), o = Qe(), l = Xe(), s = Fe(), D = St, W = qe();
  function M(g, V) {
    var X = Object.keys(g);
    if (Object.getOwnPropertySymbols) {
      var Z = Object.getOwnPropertySymbols(g);
      V && (Z = Z.filter(function(c) {
        return Object.getOwnPropertyDescriptor(g, c).enumerable;
      })), X.push.apply(X, Z);
    }
    return X;
  }
  function U(g) {
    for (var V = 1; V < arguments.length; V++) {
      var X = arguments[V] != null ? arguments[V] : {};
      V % 2 ? M(Object(X), true).forEach(function(Z) {
        (0, t.default)(g, Z, X[Z]);
      }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(g, Object.getOwnPropertyDescriptors(X)) : M(Object(X)).forEach(function(Z) {
        Object.defineProperty(g, Z, Object.getOwnPropertyDescriptor(X, Z));
      });
    }
    return g;
  }
  function F(g) {
    var V = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, X = V.series;
    X || (X = []);
    var Z = (0, W.initNeedSeries)(X, l.radarConfig, "radar");
    Z = A(Z, g), Z = v(Z, g), Z = R(Z, g), (0, o.doUpdate)({
      chart: g,
      series: Z,
      key: "radar",
      getGraphConfig: N,
      getStartGraphConfig: I,
      beforeChange: $
    }), (0, o.doUpdate)({
      chart: g,
      series: Z,
      key: "radarPoint",
      getGraphConfig: f,
      getStartGraphConfig: _
    }), (0, o.doUpdate)({
      chart: g,
      series: Z,
      key: "radarLabel",
      getGraphConfig: C
    });
  }
  function A(g, V) {
    var X = V.radarAxis;
    if (!X)
      return [];
    var Z = X.indicator, c = X.axisLineAngles, y = X.radius, h2 = X.centerPos;
    return g.forEach(function(P) {
      var q = P.data;
      P.dataRadius = [], P.radarPosition = Z.map(function(K, ee) {
        var ae = K.max, oe = K.min, ve = q[ee];
        typeof ae != "number" && (ae = ve), typeof oe != "number" && (oe = 0), typeof ve != "number" && (ve = oe);
        var Q = (ve - oe) / (ae - oe) * y;
        return P.dataRadius[ee] = Q, s.getCircleRadianPoint.apply(void 0, (0, a.default)(h2).concat([Q, c[ee]]));
      });
    }), g;
  }
  function v(g, V) {
    var X = V.radarAxis;
    if (!X)
      return [];
    var Z = X.centerPos, c = X.axisLineAngles;
    return g.forEach(function(y) {
      var h2 = y.dataRadius, P = y.label, q = P.labelGap;
      y.labelPosition = h2.map(function(K, ee) {
        return s.getCircleRadianPoint.apply(void 0, (0, a.default)(Z).concat([K + q, c[ee]]));
      });
    }), g;
  }
  function R(g, V) {
    var X = V.radarAxis;
    if (!X)
      return [];
    var Z = (0, n.default)(X.centerPos, 2), c = Z[0], y = Z[1];
    return g.forEach(function(h2) {
      var P = h2.labelPosition, q = P.map(function(K) {
        var ee = (0, n.default)(K, 2), ae = ee[0], oe = ee[1], ve = ae > c ? "left" : "right", Q = oe > y ? "top" : "bottom";
        return {
          textAlign: ve,
          textBaseline: Q
        };
      });
      h2.labelAlign = q;
    }), g;
  }
  function N(g) {
    var V = g.animationCurve, X = g.animationFrame, Z = g.rLevel;
    return [{
      name: "polyline",
      index: Z,
      animationCurve: V,
      animationFrame: X,
      shape: E(g),
      style: b(g)
    }];
  }
  function I(g, V) {
    var X = V.chart.radarAxis.centerPos, Z = N(g)[0], c = Z.shape.points.length, y = new Array(c).fill(0).map(function(h2) {
      return (0, a.default)(X);
    });
    return Z.shape.points = y, [Z];
  }
  function E(g) {
    var V = g.radarPosition;
    return {
      points: V,
      close: true
    };
  }
  function b(g) {
    var V = g.radarStyle, X = g.color, Z = (0, D.getRgbaValue)(X);
    Z[3] = 0.5;
    var c = {
      stroke: X,
      fill: (0, D.getColorFromRgbValue)(Z)
    };
    return (0, W.deepMerge)(c, V);
  }
  function $(g, V) {
    var X = V.shape, Z = g.shape.points, c = Z.length, y = X.points.length;
    if (y > c) {
      var h2 = Z.slice(-1)[0], P = new Array(y - c).fill(0).map(function(q) {
        return (0, a.default)(h2);
      });
      Z.push.apply(Z, (0, a.default)(P));
    } else
      y < c && Z.splice(y);
  }
  function f(g) {
    var V = g.radarPosition, X = g.animationCurve, Z = g.animationFrame, c = g.rLevel;
    return V.map(function(y, h2) {
      return {
        name: "circle",
        index: c,
        animationCurve: X,
        animationFrame: Z,
        visible: g.point.show,
        shape: O(g, h2),
        style: m(g)
      };
    });
  }
  function _(g) {
    var V = f(g);
    return V.forEach(function(X) {
      return X.shape.r = 0.01;
    }), V;
  }
  function O(g, V) {
    var X = g.radarPosition, Z = g.point, c = Z.radius, y = X[V];
    return {
      rx: y[0],
      ry: y[1],
      r: c
    };
  }
  function m(g, V) {
    var X = g.point, Z = g.color, c = X.style;
    return (0, W.deepMerge)({
      stroke: Z
    }, c);
  }
  function C(g) {
    var V = g.labelPosition, X = g.animationCurve, Z = g.animationFrame, c = g.rLevel;
    return V.map(function(y, h2) {
      return {
        name: "text",
        index: c,
        visible: g.label.show,
        animationCurve: X,
        animationFrame: Z,
        shape: d(g, h2),
        style: L(g, h2)
      };
    });
  }
  function d(g, V) {
    var X = g.labelPosition, Z = g.label, c = g.data, y = Z.offset, h2 = Z.formatter, P = G(X[V], y), q = c[V] ? c[V].toString() : "0", K = (0, r.default)(h2);
    return K === "string" && (q = h2.replace("{value}", q)), K === "function" && (q = h2(q)), {
      content: q,
      position: P
    };
  }
  function G(g, V) {
    var X = (0, n.default)(g, 2), Z = X[0], c = X[1], y = (0, n.default)(V, 2), h2 = y[0], P = y[1];
    return [Z + h2, c + P];
  }
  function L(g, V) {
    var X = g.label, Z = g.color, c = g.labelAlign, y = X.style, h2 = U({
      fill: Z
    }, c[V]);
    return (0, W.deepMerge)(h2, y);
  }
  return Wt;
}
var zt = {};
var ja;
function Ru() {
  if (ja)
    return zt;
  ja = 1;
  var e = we;
  Object.defineProperty(zt, "__esModule", {
    value: true
  }), zt.gauge = F;
  var t = e(Ue()), r = e(ze()), n = e(Ne()), a = e(Be()), o = Qe(), l = yi(), s = Fe(), D = qe(), W = St;
  function M(j, k) {
    var S = Object.keys(j);
    if (Object.getOwnPropertySymbols) {
      var B = Object.getOwnPropertySymbols(j);
      k && (B = B.filter(function(H) {
        return Object.getOwnPropertyDescriptor(j, H).enumerable;
      })), S.push.apply(S, B);
    }
    return S;
  }
  function U(j) {
    for (var k = 1; k < arguments.length; k++) {
      var S = arguments[k] != null ? arguments[k] : {};
      k % 2 ? M(Object(S), true).forEach(function(B) {
        (0, t.default)(j, B, S[B]);
      }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(j, Object.getOwnPropertyDescriptors(S)) : M(Object(S)).forEach(function(B) {
        Object.defineProperty(j, B, Object.getOwnPropertyDescriptor(S, B));
      });
    }
    return j;
  }
  function F(j) {
    var k = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, S = k.series;
    S || (S = []);
    var B = (0, D.initNeedSeries)(S, l.gaugeConfig, "gauge");
    B = A(B, j), B = v(B, j), B = R(B, j), B = N(B), B = I(B), B = E(B), B = b(B), B = $(B), B = f(B), B = _(B), (0, o.doUpdate)({
      chart: j,
      series: B,
      key: "gaugeAxisTick",
      getGraphConfig: m
    }), (0, o.doUpdate)({
      chart: j,
      series: B,
      key: "gaugeAxisLabel",
      getGraphConfig: G
    }), (0, o.doUpdate)({
      chart: j,
      series: B,
      key: "gaugeBackgroundArc",
      getGraphConfig: V,
      getStartGraphConfig: c
    }), (0, o.doUpdate)({
      chart: j,
      series: B,
      key: "gaugeArc",
      getGraphConfig: y,
      getStartGraphConfig: q,
      beforeChange: K
    }), (0, o.doUpdate)({
      chart: j,
      series: B,
      key: "gaugePointer",
      getGraphConfig: ee,
      getStartGraphConfig: Q
    }), (0, o.doUpdate)({
      chart: j,
      series: B,
      key: "gaugeDetails",
      getGraphConfig: ie
    });
  }
  function A(j, k) {
    var S = k.render.area;
    return j.forEach(function(B) {
      var H = B.center;
      H = H.map(function(te, p) {
        return typeof te == "number" ? te : parseInt(te) / 100 * S[p];
      }), B.center = H;
    }), j;
  }
  function v(j, k) {
    var S = k.render.area, B = Math.min.apply(Math, (0, a.default)(S)) / 2;
    return j.forEach(function(H) {
      var te = H.radius;
      typeof te != "number" && (te = parseInt(te) / 100 * B), H.radius = te;
    }), j;
  }
  function R(j, k) {
    var S = k.render.area, B = Math.min.apply(Math, (0, a.default)(S)) / 2;
    return j.forEach(function(H) {
      var te = H.radius, p = H.data, z = H.arcLineWidth;
      p.forEach(function(u) {
        var x = u.radius, w = u.lineWidth;
        x || (x = te), typeof x != "number" && (x = parseInt(x) / 100 * B), u.radius = x, w || (w = z), u.lineWidth = w;
      });
    }), j;
  }
  function N(j, k) {
    return j.forEach(function(S) {
      var B = S.startAngle, H = S.endAngle, te = S.data, p = S.min, z = S.max, u = H - B, x = z - p;
      te.forEach(function(w) {
        var T = w.value, Y = Math.abs((T - p) / x * u);
        w.startAngle = B, w.endAngle = B + Y;
      });
    }), j;
  }
  function I(j, k) {
    return j.forEach(function(S) {
      var B = S.data;
      B.forEach(function(H) {
        var te = H.color, p = H.gradient;
        (!p || !p.length) && (p = te), p instanceof Array || (p = [p]), H.gradient = p;
      });
    }), j;
  }
  function E(j, k) {
    return j.forEach(function(S) {
      var B = S.startAngle, H = S.endAngle, te = S.splitNum, p = S.center, z = S.radius, u = S.arcLineWidth, x = S.axisTick, w = x.tickLength, T = x.style.lineWidth, Y = H - B, re = z - u / 2, ue = re - w, de = Y / (te - 1), he = 2 * Math.PI * z * Y / (Math.PI * 2), pe = Math.ceil(T / 2) / he * Y;
      S.tickAngles = [], S.tickInnerRadius = [], S.tickPosition = new Array(te).fill(0).map(function(ge, ye) {
        var Ge = B + de * ye;
        return ye === 0 && (Ge += pe), ye === te - 1 && (Ge -= pe), S.tickAngles[ye] = Ge, S.tickInnerRadius[ye] = ue, [s.getCircleRadianPoint.apply(void 0, (0, a.default)(p).concat([re, Ge])), s.getCircleRadianPoint.apply(void 0, (0, a.default)(p).concat([ue, Ge]))];
      });
    }), j;
  }
  function b(j, k) {
    return j.forEach(function(S) {
      var B = S.center, H = S.tickInnerRadius, te = S.tickAngles, p = S.axisLabel.labelGap, z = te.map(function(x, w) {
        return s.getCircleRadianPoint.apply(void 0, (0, a.default)(B).concat([H[w] - p, te[w]]));
      }), u = z.map(function(x) {
        var w = (0, n.default)(x, 2), T = w[0], Y = w[1];
        return {
          textAlign: T > B[0] ? "right" : "left",
          textBaseline: Y > B[1] ? "bottom" : "top"
        };
      });
      S.labelPosition = z, S.labelAlign = u;
    }), j;
  }
  function $(j, k) {
    return j.forEach(function(S) {
      var B = S.axisLabel, H = S.min, te = S.max, p = S.splitNum, z = B.data, u = B.formatter, x = (te - H) / (p - 1), w = new Array(p).fill(0).map(function(Y, re) {
        return parseInt(H + x * re);
      }), T = (0, r.default)(u);
      z = (0, D.deepMerge)(w, z).map(function(Y, re) {
        var ue = Y;
        return T === "string" && (ue = u.replace("{value}", Y)), T === "function" && (ue = u({
          value: Y,
          index: re
        })), ue;
      }), B.data = z;
    }), j;
  }
  function f(j, k) {
    return j.forEach(function(S) {
      var B = S.data, H = S.details, te = S.center, p = H.position, z = H.offset, u = B.map(function(x) {
        var w = x.startAngle, T = x.endAngle, Y = x.radius, re = null;
        return p === "center" ? re = te : p === "start" ? re = s.getCircleRadianPoint.apply(void 0, (0, a.default)(te).concat([Y, w])) : p === "end" && (re = s.getCircleRadianPoint.apply(void 0, (0, a.default)(te).concat([Y, T]))), O(re, z);
      });
      S.detailsPosition = u;
    }), j;
  }
  function _(j, k) {
    return j.forEach(function(S) {
      var B = S.data, H = S.details, te = H.formatter, p = (0, r.default)(te), z = B.map(function(u) {
        var x = u.value;
        return p === "string" && (x = te.replace("{value}", "{nt}"), x = x.replace("{name}", u.name)), p === "function" && (x = te(u)), x.toString();
      });
      S.detailsContent = z;
    }), j;
  }
  function O(j, k) {
    var S = (0, n.default)(j, 2), B = S[0], H = S[1], te = (0, n.default)(k, 2), p = te[0], z = te[1];
    return [B + p, H + z];
  }
  function m(j) {
    var k = j.tickPosition, S = j.animationCurve, B = j.animationFrame, H = j.rLevel;
    return k.map(function(te, p) {
      return {
        name: "polyline",
        index: H,
        visible: j.axisTick.show,
        animationCurve: S,
        animationFrame: B,
        shape: C(j, p),
        style: d(j)
      };
    });
  }
  function C(j, k) {
    var S = j.tickPosition;
    return {
      points: S[k]
    };
  }
  function d(j, k) {
    var S = j.axisTick.style;
    return S;
  }
  function G(j) {
    var k = j.labelPosition, S = j.animationCurve, B = j.animationFrame, H = j.rLevel;
    return k.map(function(te, p) {
      return {
        name: "text",
        index: H,
        visible: j.axisLabel.show,
        animationCurve: S,
        animationFrame: B,
        shape: L(j, p),
        style: g(j, p)
      };
    });
  }
  function L(j, k) {
    var S = j.labelPosition, B = j.axisLabel.data;
    return {
      content: B[k].toString(),
      position: S[k]
    };
  }
  function g(j, k) {
    var S = j.labelAlign, B = j.axisLabel, H = B.style;
    return (0, D.deepMerge)(U({}, S[k]), H);
  }
  function V(j) {
    var k = j.animationCurve, S = j.animationFrame, B = j.rLevel;
    return [{
      name: "arc",
      index: B,
      visible: j.backgroundArc.show,
      animationCurve: k,
      animationFrame: S,
      shape: X(j),
      style: Z(j)
    }];
  }
  function X(j) {
    var k = j.startAngle, S = j.endAngle, B = j.center, H = j.radius;
    return {
      rx: B[0],
      ry: B[1],
      r: H,
      startAngle: k,
      endAngle: S
    };
  }
  function Z(j) {
    var k = j.backgroundArc, S = j.arcLineWidth, B = k.style;
    return (0, D.deepMerge)({
      lineWidth: S
    }, B);
  }
  function c(j) {
    var k = V(j)[0], S = U({}, k.shape);
    return S.endAngle = k.shape.startAngle, k.shape = S, [k];
  }
  function y(j) {
    var k = j.data, S = j.animationCurve, B = j.animationFrame, H = j.rLevel;
    return k.map(function(te, p) {
      return {
        name: "agArc",
        index: H,
        animationCurve: S,
        animationFrame: B,
        shape: h2(j, p),
        style: P(j, p)
      };
    });
  }
  function h2(j, k) {
    var S = j.data, B = j.center, H = j.endAngle, te = S[k], p = te.radius, z = te.startAngle, u = te.endAngle, x = te.localGradient;
    return x && (H = u), {
      rx: B[0],
      ry: B[1],
      r: p,
      startAngle: z,
      endAngle: u,
      gradientEndAngle: H
    };
  }
  function P(j, k) {
    var S = j.data, B = j.dataItemStyle, H = S[k], te = H.lineWidth, p = H.gradient;
    return p = p.map(function(z) {
      return (0, W.getRgbaValue)(z);
    }), (0, D.deepMerge)({
      lineWidth: te,
      gradient: p
    }, B);
  }
  function q(j) {
    var k = y(j);
    return k.map(function(S) {
      var B = U({}, S.shape);
      B.endAngle = S.shape.startAngle, S.shape = B;
    }), k;
  }
  function K(j, k) {
    var S = j.style.gradient, B = S.length, H = k.style.gradient.length;
    if (B > H)
      S.splice(H);
    else {
      var te = S.slice(-1)[0];
      S.push.apply(S, (0, a.default)(new Array(H - B).fill(0).map(function(p) {
        return (0, a.default)(te);
      })));
    }
  }
  function ee(j) {
    var k = j.animationCurve, S = j.animationFrame, B = j.center, H = j.rLevel;
    return [{
      name: "polyline",
      index: H,
      visible: j.pointer.show,
      animationCurve: k,
      animationFrame: S,
      shape: ae(j),
      style: oe(j),
      setGraphCenter: function(p, z) {
        z.style.graphCenter = B;
      }
    }];
  }
  function ae(j) {
    var k = j.center;
    return {
      points: ve(k),
      close: true
    };
  }
  function oe(j) {
    var k = j.startAngle, S = j.endAngle, B = j.min, H = j.max, te = j.data, p = j.pointer, z = j.center, u = p.valueIndex, x = p.style, w = te[u] ? te[u].value : 0, T = (w - B) / (H - B) * (S - k) + k + Math.PI / 2;
    return (0, D.deepMerge)({
      rotate: (0, D.radianToAngle)(T),
      scale: [1, 1],
      graphCenter: z
    }, x);
  }
  function ve(j) {
    var k = (0, n.default)(j, 2), S = k[0], B = k[1], H = [S, B - 40], te = [S + 5, B], p = [S, B + 10], z = [S - 5, B];
    return [H, te, p, z];
  }
  function Q(j) {
    var k = j.startAngle, S = ee(j)[0];
    return S.style.rotate = (0, D.radianToAngle)(k + Math.PI / 2), [S];
  }
  function ie(j) {
    var k = j.detailsPosition, S = j.animationCurve, B = j.animationFrame, H = j.rLevel, te = j.details.show;
    return k.map(function(p, z) {
      return {
        name: "numberText",
        index: H,
        visible: te,
        animationCurve: S,
        animationFrame: B,
        shape: ce(j, z),
        style: fe(j, z)
      };
    });
  }
  function ce(j, k) {
    var S = j.detailsPosition, B = j.detailsContent, H = j.data, te = j.details, p = S[k], z = B[k], u = H[k].value, x = te.valueToFixed;
    return {
      number: [u],
      content: z,
      position: p,
      toFixed: x
    };
  }
  function fe(j, k) {
    var S = j.details, B = j.data, H = S.style, te = B[k].color;
    return (0, D.deepMerge)({
      fill: te
    }, H);
  }
  return zt;
}
var qt = {};
var Ea;
function Tu() {
  if (Ea)
    return qt;
  Ea = 1;
  var e = we;
  Object.defineProperty(qt, "__esModule", {
    value: true
  }), qt.legend = D;
  var t = e(Ue()), r = e(Ne()), n = e(ze()), a = Qe(), o = Fe(), l = Xe(), s = qe();
  function D(c) {
    var y = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, h2 = y.legend;
    h2 ? (h2 = (0, s.deepMerge)((0, o.deepClone)(l.legendConfig, true), h2), h2 = W(h2), h2 = M(h2, y, c), h2 = U(h2, c), h2 = v(h2, c), h2 = [h2]) : h2 = [], (0, a.doUpdate)({
      chart: c,
      series: h2,
      key: "legendIcon",
      getGraphConfig: C
    }), (0, a.doUpdate)({
      chart: c,
      series: h2,
      key: "legendText",
      getGraphConfig: L
    });
  }
  function W(c) {
    var y = c.data;
    return c.data = y.map(function(h2) {
      var P = (0, n.default)(h2);
      return P === "string" ? {
        name: h2
      } : P === "object" ? h2 : {
        name: ""
      };
    }), c;
  }
  function M(c, y, h2) {
    var P = y.series, q = h2.legendStatus, K = c.data.filter(function(ee) {
      var ae = ee.name, oe = P.find(function(ve) {
        var Q = ve.name;
        return ae === Q;
      });
      return oe ? (ee.color || (ee.color = oe.color), ee.icon || (ee.icon = oe.type), ee) : false;
    });
    return (!q || q.length !== c.data.length) && (q = new Array(c.data.length).fill(true)), K.forEach(function(ee, ae) {
      return ee.status = q[ae];
    }), c.data = K, h2.legendStatus = q, c;
  }
  function U(c, y) {
    var h2 = y.render.ctx, P = c.data, q = c.textStyle, K = c.textUnselectedStyle;
    return P.forEach(function(ee) {
      var ae = ee.status, oe = ee.name;
      ee.textWidth = F(h2, oe, ae ? q : K);
    }), c;
  }
  function F(c, y, h2) {
    return c.font = A(h2), c.measureText(y).width;
  }
  function A(c) {
    var y = c.fontFamily, h2 = c.fontSize;
    return "".concat(h2, "px ").concat(y);
  }
  function v(c, y) {
    var h2 = c.orient;
    return h2 === "vertical" ? f(c, y) : R(c, y), c;
  }
  function R(c, y) {
    var h2 = c.iconHeight, P = c.itemGap, q = N(c, y), K = q.map(function(oe) {
      return E(oe, c, y);
    }), ee = b(c, y), ae = {
      textAlign: "left",
      textBaseline: "middle"
    };
    q.forEach(function(oe, ve) {
      return oe.forEach(function(Q) {
        var ie = Q.iconPosition, ce = Q.textPosition, fe = K[ve], j = ee + ve * (P + h2);
        Q.iconPosition = $(ie, [fe, j]), Q.textPosition = $(ce, [fe, j]), Q.align = ae;
      });
    });
  }
  function N(c, y) {
    var h2 = c.data, P = c.iconWidth, q = y.render.area[0], K = 0, ee = [[]];
    return h2.forEach(function(ae, oe) {
      var ve = I(K, oe, c), Q = ve + P + 5 + ae.textWidth;
      Q >= q && (K = oe, ve = I(K, oe, c), ee.push([])), ae.iconPosition = [ve, 0], ae.textPosition = [ve + P + 5, 0], ee.slice(-1)[0].push(ae);
    }), ee;
  }
  function I(c, y, h2) {
    var P = h2.data, q = h2.iconWidth, K = h2.itemGap, ee = P.slice(c, y);
    return (0, s.mulAdd)(ee.map(function(ae) {
      var oe = ae.textWidth;
      return oe;
    })) + (y - c) * (K + 5 + q);
  }
  function E(c, y, h2) {
    var P = y.left, q = y.right, K = y.iconWidth, ee = y.itemGap, ae = h2.render.area[0], oe = c.length, ve = (0, s.mulAdd)(c.map(function(ie) {
      var ce = ie.textWidth;
      return ce;
    })) + oe * (5 + K) + (oe - 1) * ee, Q = [P, q].findIndex(function(ie) {
      return ie !== "auto";
    });
    return Q === -1 ? (ae - ve) / 2 : Q === 0 ? typeof P == "number" ? P : parseInt(P) / 100 * ae : (typeof q != "number" && (q = parseInt(q) / 100 * ae), ae - (ve + q));
  }
  function b(c, y) {
    var h2 = c.top, P = c.bottom, q = c.iconHeight, K = y.render.area[1], ee = [h2, P].findIndex(function(ie) {
      return ie !== "auto";
    }), ae = q / 2;
    if (ee === -1) {
      var oe = y.gridArea, ve = oe.y, Q = oe.h;
      return ve + Q + 45 - ae;
    } else
      return ee === 0 ? typeof h2 == "number" ? h2 - ae : parseInt(h2) / 100 * K - ae : (typeof P != "number" && (P = parseInt(P) / 100 * K), K - P - ae);
  }
  function $(c, y) {
    var h2 = (0, r.default)(c, 2), P = h2[0], q = h2[1], K = (0, r.default)(y, 2), ee = K[0], ae = K[1];
    return [P + ee, q + ae];
  }
  function f(c, y) {
    var h2 = _(c, y), P = (0, r.default)(h2, 2), q = P[0], K = P[1], ee = O(c, y);
    m(c, q);
    var ae = {
      textAlign: "left",
      textBaseline: "middle"
    };
    c.data.forEach(function(oe) {
      var ve = oe.textPosition, Q = oe.iconPosition;
      oe.textPosition = $(ve, [K, ee]), oe.iconPosition = $(Q, [K, ee]), oe.align = ae;
    });
  }
  function _(c, y) {
    var h2 = c.left, P = c.right, q = y.render.area[0], K = [h2, P].findIndex(function(ae) {
      return ae !== "auto";
    });
    if (K === -1)
      return [true, q - 10];
    var ee = [h2, P][K];
    return typeof ee != "number" && (ee = parseInt(ee) / 100 * q), [!!K, ee];
  }
  function O(c, y) {
    var h2 = c.iconHeight, P = c.itemGap, q = c.data, K = c.top, ee = c.bottom, ae = y.render.area[1], oe = q.length, ve = oe * h2 + (oe - 1) * P, Q = [K, ee].findIndex(function(ce) {
      return ce !== "auto";
    });
    if (Q === -1)
      return (ae - ve) / 2;
    var ie = [K, ee][Q];
    return typeof ie != "number" && (ie = parseInt(ie) / 100 * ae), Q === 1 && (ie = ae - ie - ve), ie;
  }
  function m(c, y) {
    var h2 = c.data, P = c.iconWidth, q = c.iconHeight, K = c.itemGap, ee = q / 2;
    h2.forEach(function(ae, oe) {
      var ve = ae.textWidth, Q = (q + K) * oe + ee, ie = y ? 0 - P : 0, ce = y ? ie - 5 - ve : P + 5;
      ae.iconPosition = [ie, Q], ae.textPosition = [ce, Q];
    });
  }
  function C(c, y) {
    var h2 = c.data, P = c.selectAble, q = c.animationCurve, K = c.animationFrame, ee = c.rLevel;
    return h2.map(function(ae, oe) {
      return (0, t.default)({
        name: ae.icon === "line" ? "lineIcon" : "rect",
        index: ee,
        visible: c.show,
        hover: P,
        click: P,
        animationCurve: q,
        animationFrame: K,
        shape: d(c, oe),
        style: G(c, oe)
      }, "click", Z(c, oe, y));
    });
  }
  function d(c, y) {
    var h2 = c.data, P = c.iconWidth, q = c.iconHeight, K = (0, r.default)(h2[y].iconPosition, 2), ee = K[0], ae = K[1], oe = q / 2;
    return {
      x: ee,
      y: ae - oe,
      w: P,
      h: q
    };
  }
  function G(c, y) {
    var h2 = c.data, P = c.iconStyle, q = c.iconUnselectedStyle, K = h2[y], ee = K.status, ae = K.color, oe = ee ? P : q;
    return (0, s.deepMerge)({
      fill: ae
    }, oe);
  }
  function L(c, y) {
    var h2 = c.data, P = c.selectAble, q = c.animationCurve, K = c.animationFrame, ee = c.rLevel;
    return h2.map(function(ae, oe) {
      return {
        name: "text",
        index: ee,
        visible: c.show,
        hover: P,
        animationCurve: q,
        animationFrame: K,
        hoverRect: X(c, oe),
        shape: g(c, oe),
        style: V(c, oe),
        click: Z(c, oe, y)
      };
    });
  }
  function g(c, y) {
    var h2 = c.data[y], P = h2.textPosition, q = h2.name;
    return {
      content: q,
      position: P
    };
  }
  function V(c, y) {
    var h2 = c.textStyle, P = c.textUnselectedStyle, q = c.data[y], K = q.status, ee = q.align, ae = K ? h2 : P;
    return (0, s.deepMerge)((0, o.deepClone)(ae, true), ee);
  }
  function X(c, y) {
    var h2 = c.textStyle, P = c.textUnselectedStyle, q = c.data[y], K = q.status, ee = (0, r.default)(q.textPosition, 2), ae = ee[0], oe = ee[1], ve = q.textWidth, Q = K ? h2 : P, ie = Q.fontSize;
    return [ae, oe - ie / 2, ve, ie];
  }
  function Z(c, y, h2) {
    var P = c.data[y].name;
    return function() {
      var q = h2.chart, K = q.legendStatus, ee = q.option, ae = !K[y], oe = ee.series.find(function(ve) {
        var Q = ve.name;
        return Q === P;
      });
      oe.show = ae, K[y] = ae, h2.chart.setOption(ee);
    };
  }
  return qt;
}
var Wa;
function Du() {
  return Wa || (Wa = 1, function(e) {
    Object.defineProperty(e, "__esModule", {
      value: true
    }), Object.defineProperty(e, "mergeColor", {
      enumerable: true,
      get: function() {
        return t.mergeColor;
      }
    }), Object.defineProperty(e, "title", {
      enumerable: true,
      get: function() {
        return r.title;
      }
    }), Object.defineProperty(e, "grid", {
      enumerable: true,
      get: function() {
        return n.grid;
      }
    }), Object.defineProperty(e, "axis", {
      enumerable: true,
      get: function() {
        return a.axis;
      }
    }), Object.defineProperty(e, "line", {
      enumerable: true,
      get: function() {
        return o.line;
      }
    }), Object.defineProperty(e, "bar", {
      enumerable: true,
      get: function() {
        return l.bar;
      }
    }), Object.defineProperty(e, "pie", {
      enumerable: true,
      get: function() {
        return s.pie;
      }
    }), Object.defineProperty(e, "radarAxis", {
      enumerable: true,
      get: function() {
        return D.radarAxis;
      }
    }), Object.defineProperty(e, "radar", {
      enumerable: true,
      get: function() {
        return W.radar;
      }
    }), Object.defineProperty(e, "gauge", {
      enumerable: true,
      get: function() {
        return M.gauge;
      }
    }), Object.defineProperty(e, "legend", {
      enumerable: true,
      get: function() {
        return U.legend;
      }
    });
    var t = Pu(), r = wu(), n = ku(), a = Au(), o = Lu(), l = Su(), s = Ou(), D = Gu(), W = Mu(), M = Ru(), U = Tu();
  }(zr)), zr;
}
var za;
function Bu() {
  return za || (za = 1, function(e) {
    var t = we;
    Object.defineProperty(e, "__esModule", {
      value: true
    }), e.default = void 0;
    var r = t(ze()), n = t(Lt()), a = t(Zt), o = Fe(), l = Du(), s = function D(W) {
      if ((0, n.default)(this, D), !W)
        return console.error("Charts Missing parameters!"), false;
      var M = W.clientWidth, U = W.clientHeight, F = document.createElement("canvas");
      F.setAttribute("width", M), F.setAttribute("height", U), W.appendChild(F);
      var A = {
        container: W,
        canvas: F,
        render: new a.default(F),
        option: null
      };
      Object.assign(this, A);
    };
    e.default = s, s.prototype.setOption = function(D) {
      var W = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : false;
      if (!D || (0, r.default)(D) !== "object")
        return console.error("setOption Missing parameters!"), false;
      W && this.render.graphs.forEach(function(U) {
        return U.animationEnd();
      });
      var M = (0, o.deepClone)(D, true);
      (0, l.mergeColor)(this, M), (0, l.grid)(this, M), (0, l.axis)(this, M), (0, l.radarAxis)(this, M), (0, l.title)(this, M), (0, l.bar)(this, M), (0, l.line)(this, M), (0, l.pie)(this, M), (0, l.radar)(this, M), (0, l.gauge)(this, M), (0, l.legend)(this, M), this.option = D, this.render.launchAnimation();
    }, s.prototype.resize = function() {
      var D = this.container, W = this.canvas, M = this.render, U = this.option, F = D.clientWidth, A = D.clientHeight;
      W.setAttribute("width", F), W.setAttribute("height", A), M.area = [F, A], this.setOption(U);
    };
  }(Wr)), Wr;
}
(function(e) {
  var t = we;
  Object.defineProperty(e, "__esModule", {
    value: true
  }), Object.defineProperty(e, "changeDefaultConfig", {
    enumerable: true,
    get: function() {
      return n.changeDefaultConfig;
    }
  }), e.default = void 0;
  var r = t(Bu()), n = Xe(), a = r.default;
  e.default = a;
})(pi);
var bi = ci(pi);
var Fu = {
  __name: "index",
  props: {
    option: {
      type: Object,
      default: () => ({})
    }
  },
  setup(e) {
    const t = e, r = ref(null), n = ref(null);
    let a = reactive({});
    xe(n, s, o), watch(() => t.option, () => {
      a && a.setOption(t.option, true);
    }, { deep: true });
    function o() {
      l();
    }
    function l() {
      a = new bi(n.value), t.option && a.setOption(t.option);
    }
    function s() {
      a && a.resize();
    }
    return (D, W) => (openBlock(), createElementBlock("div", {
      ref_key: "chartsContainerRef",
      ref: r,
      class: "dv-charts-container"
    }, [
      createBaseVNode("div", {
        ref_key: "chartRef",
        ref: n,
        class: "charts-canvas-container"
      }, null, 512)
    ], 512));
  }
};
var Ir = {
  install(e) {
    e.component("DvCharts", Fu);
  }
};
var Nu = { class: "dv-capsule-chart" };
var ju = { class: "label-column" };
var Eu = createBaseVNode("div", null, " ", -1);
var Wu = { class: "capsule-container" };
var zu = {
  key: 0,
  class: "capsule-item-value"
};
var qu = { class: "unit-label" };
var Iu = {
  key: 0,
  class: "unit-text"
};
var Hu = {
  __name: "index",
  props: {
    config: {
      type: Object,
      default: () => ({})
    }
  },
  setup(e) {
    const t = e;
    useCssVars((D) => ({
      "6be2ab5a": unref(a),
      "0accef4c": unref(n)
    }));
    const r = reactive({
      defaultConfig: {
        /**
             * @description Capsule chart data
             * @type {Array<Object>}
             * @default data = []
             * @example data = [{ name: 'foo1', value: 100 }, { name: 'foo2', value: 100 }]
             */
        data: [],
        /**
             * @description Colors (hex|rgb|rgba|color keywords)
             * @type {Array<String>}
             * @default color = ['#37a2da', '#32c5e9', '#67e0e3', '#9fe6b8', '#ffdb5c', '#ff9f7f', '#fb7293']
             * @example color = ['#000', 'rgb(0, 0, 0)', 'rgba(0, 0, 0, 1)', 'red']
             */
        colors: [
          "#37a2da",
          "#32c5e9",
          "#67e0e3",
          "#9fe6b8",
          "#ffdb5c",
          "#ff9f7f",
          "#fb7293"
        ],
        /**
             * @description Chart unit
             * @type {String}
             * @default unit = ''
             */
        unit: "",
        /**
             * @description Show item value
             * @type {Boolean}
             * @default showValue = false
             */
        showValue: false,
        /**
             * @description Text color
             * @type {String}
             * @default textColor = '#fff'
             */
        textColor: "#fff",
        /**
             * @description Chart font size
             * @type {Number}
             * @default fontSize = 12
             */
        fontSize: 12
      },
      mergedConfig: null,
      capsuleLength: [],
      capsuleValue: [],
      labelData: [],
      labelDataLength: []
    });
    watch(() => t.config, () => {
      o();
    }, {
      deep: true
    });
    const n = computed(() => `${t.config.fontSize ? t.config.fontSize : r.defaultConfig.fontSize}px`), a = computed(() => t.config.textColor ? t.config.textColor : r.defaultConfig.textColor);
    function o() {
      l(), s();
    }
    function l() {
      r.mergedConfig = Ce(
        $e(r.defaultConfig, true),
        t.config || {}
      );
    }
    function s() {
      const { data: D } = r.mergedConfig;
      if (!D.length || D.length === 0) {
        r.labelData = [], r.capsuleLength = [];
        return;
      }
      const W = D.map(({ value: A }) => A), M = Math.max(...W);
      r.capsuleValue = W, r.capsuleLength = W.map((A) => M ? A / M : 0);
      const U = M / 5, F = Array.from(
        new Set(new Array(6).fill(0).map((A, v) => Math.ceil(v * U)))
      );
      r.labelData = F, r.labelDataLength = Array.from(F).map(
        (A) => M ? A / M : 0
      );
    }
    return onMounted(() => {
      o();
    }), (D, W) => (openBlock(), createElementBlock("div", Nu, [
      unref(r).mergedConfig ? (openBlock(), createElementBlock(Fragment, { key: 0 }, [
        createBaseVNode("div", ju, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(unref(r).mergedConfig.data, (M) => (openBlock(), createElementBlock("div", {
            key: M.name
          }, toDisplayString(M.name), 1))), 128)),
          Eu
        ]),
        createBaseVNode("div", Wu, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(unref(r).capsuleLength, (M, U) => (openBlock(), createElementBlock("div", {
            key: U,
            class: "capsule-item"
          }, [
            createBaseVNode("div", {
              class: "capsule-item-column",
              style: normalizeStyle(`width: ${M * 100}%; background-color: ${unref(r).mergedConfig.colors[U % unref(r).mergedConfig.colors.length]};`)
            }, [
              unref(r).mergedConfig.showValue ? (openBlock(), createElementBlock("div", zu, toDisplayString(unref(r).capsuleValue[U]), 1)) : createCommentVNode("", true)
            ], 4)
          ]))), 128)),
          createBaseVNode("div", qu, [
            (openBlock(true), createElementBlock(Fragment, null, renderList(unref(r).labelData, (M, U) => (openBlock(), createElementBlock("div", {
              key: M + U
            }, toDisplayString(M), 1))), 128))
          ])
        ]),
        unref(r).mergedConfig.unit ? (openBlock(), createElementBlock("div", Iu, toDisplayString(unref(r).mergedConfig.unit), 1)) : createCommentVNode("", true)
      ], 64)) : createCommentVNode("", true)
    ]));
  }
};
var Hr = {
  install(e) {
    e.component("DvCapsuleChart", Hu);
  }
};
var Vu = { class: "dv-digital-flop" };
var xi = {
  __name: "index",
  props: {
    config: {
      type: Object,
      default: () => {
      }
    }
  },
  setup(e) {
    const t = e, r = ref(null), n = reactive({
      renderer: null,
      defaultConfig: {
        /**
             * @description Number for digital flop
             * @type {Array<Number>}
             * @default number = []
             * @example number = [10]
             */
        number: [],
        /**
             * @description Content formatter
             * @type {String}
             * @default content = ''
             * @example content = '{nt}个'
             */
        content: "",
        /**
             * @description Number toFixed
             * @type {Number}
             * @default toFixed = 0
             */
        toFixed: 0,
        /**
             * @description Text align
             * @type {String}
             * @default textAlign = 'center'
             * @example textAlign = 'center' | 'left' | 'right'
             */
        textAlign: "center",
        /**
             * @description rowGap
             * @type {Number}
             @default rowGap = 0
             */
        rowGap: 0,
        /**
             * @description Text style configuration
             * @type {Object} {CRender Class Style}
             */
        style: {
          fontSize: 30,
          fill: "#3de7c9"
        },
        /**
             * @description Number formatter
             * @type {Null|Function}
             */
        formatter: void 0,
        /**
             * @description CRender animationCurve
             * @type {String}
             * @default animationCurve = 'easeOutCubic'
             */
        animationCurve: "easeOutCubic",
        /**
             * @description CRender animationFrame
             * @type {String}
             * @default animationFrame = 50
             */
        animationFrame: 50
      },
      mergedConfig: null,
      graph: null
    });
    watch(() => t.config, (F) => {
      M();
    }, { deep: true }), onMounted(() => {
      a();
    });
    function a() {
      o(), l(), s();
    }
    function o() {
      n.renderer = new vi(r.value);
    }
    function l() {
      n.mergedConfig = Ce($e(n.defaultConfig, true), t.config || {});
    }
    function s() {
      const F = D(), A = W();
      n.graph = n.renderer.add({
        name: "numberText",
        animationCurve: n.mergedConfig.animationCurve,
        animationFrame: n.mergedConfig.animationFrame,
        shape: F,
        style: A
      });
    }
    function D() {
      const { number: F, content: A, toFixed: v, textAlign: R, rowGap: N, formatter: I } = n.mergedConfig, [E, b] = n.renderer.area, $ = [E / 2, b / 2];
      return R === "left" && ($[0] = 0), R === "right" && ($[0] = E), {
        number: F,
        content: A,
        toFixed: v,
        position: $,
        rowGap: N,
        formatter: I
      };
    }
    function W() {
      const { style: F, textAlign: A } = n.mergedConfig;
      return Ce(F, {
        textAlign: A,
        textBaseline: "middle"
      });
    }
    function M() {
      if (n.graph.animationEnd(), l(), !n.graph)
        return;
      const { animationCurve: F, animationFrame: A } = n.mergedConfig, v = D(), R = W();
      U(n.graph, v), n.graph.animationCurve = F, n.graph.animationFrame = A, n.graph.animation("style", R, true), n.graph.animation("shape", v);
    }
    function U(F, A) {
      const v = F.shape.number.length, R = A.number.length;
      v !== R && (F.shape.number = A.number);
    }
    return (F, A) => (openBlock(), createElementBlock("div", Vu, [
      createBaseVNode("canvas", {
        ref_key: "digitalFlop",
        ref: r
      }, null, 512)
    ]));
  }
};
var Uu = { class: "dv-active-ring-chart" };
var Xu = { class: "active-ring-info" };
var Qu = {
  __name: "index",
  props: {
    config: {
      type: Object,
      default: () => ({})
    }
  },
  setup(e) {
    const t = e;
    useCssVars((R) => ({
      "2c9ee932": unref(s)
    }));
    const r = ref(null), n = reactive({
      defaultConfig: {
        /**
             * @description Ring radius
             * @type {String|Number}
             * @default radius = '50%'
             * @example radius = '50%' | 100
             */
        radius: "50%",
        /**
             * @description Active ring radius
             * @type {String|Number}
             * @default activeRadius = '55%'
             * @example activeRadius = '55%' | 110
             */
        activeRadius: "55%",
        /**
             * @description Ring data
             * @type {Array<Object>}
             * @default data = [{ name: '', value: 0 }]
             */
        data: [{ name: "", value: 0 }],
        /**
             * @description Ring line width
             * @type {Number}
             * @default lineWidth = 20
             */
        lineWidth: 20,
        /**
             * @description Active time gap (ms)
             * @type {Number}
             * @default activeTimeGap = 3000
             */
        activeTimeGap: 3e3,
        /**
             * @description Ring color (hex|rgb|rgba|color keywords)
             * @type {Array<String>}
             * @default color = [Charts Default Color]
             * @example color = ['#000', 'rgb(0, 0, 0)', 'rgba(0, 0, 0, 1)', 'red']
             */
        color: [],
        /**
             * @description Text color
             * @type {String}
             * @default textColor = '#fff'
             */
        textColor: "#fff",
        /**
             * @description Digital flop style
             * @type {Object}
             */
        digitalFlopStyle: {
          fontSize: 25,
          fill: "#fff"
        },
        /**
             * @description Digital flop toFixed
             * @type {Number}
             */
        digitalFlopToFixed: 0,
        /**
             * @description Digital flop unit
             * @type {String}
             */
        digitalFlopUnit: "",
        /**
             * @description CRender animationCurve
             * @type {String}
             * @default animationCurve = 'easeOutCubic'
             */
        animationCurve: "easeOutCubic",
        /**
             * @description CRender animationFrame
             * @type {String}
             * @default animationFrame = 50
             */
        animationFrame: 50,
        /**
             * @description showOriginValue
             * @type {Boolean}
             * @default showOriginValue = false
             */
        showOriginValue: false
      },
      mergedConfig: null,
      chart: null,
      activeIndex: 0,
      animationHandler: ""
    }), a = computed(() => {
      if (!n.mergedConfig)
        return {};
      const {
        digitalFlopStyle: R,
        digitalFlopToFixed: N,
        data: I,
        showOriginValue: E,
        digitalFlopUnit: b
      } = n.mergedConfig, $ = I.map(({ value: _ }) => _);
      let f;
      if (E)
        f = $[n.activeIndex];
      else {
        const _ = $.reduce((m, C) => m + C, 0);
        f = parseFloat($[n.activeIndex] / _ * 100) || 0;
      }
      return {
        content: E ? `{nt}${b}` : `{nt}${b || "%"}`,
        number: [f],
        style: R,
        toFixed: N
      };
    }), o = computed(() => n.mergedConfig ? n.mergedConfig.data[n.activeIndex].name : ""), l = computed(() => n.mergedConfig ? `font-size: ${n.mergedConfig.digitalFlopStyle.fontSize}px;` : ""), s = computed(() => t.config.textColor ? t.config.textColor : n.defaultConfig.textColor);
    watch(() => t.config, () => {
      clearTimeout(n.animationHandler), n.activeIndex = 0, M(), U();
    }, {
      deep: true
    }), onMounted(() => {
      D();
    }), onUnmounted(() => {
      clearTimeout(n.animationHandler);
    });
    function D() {
      W(), M(), U();
    }
    function W() {
      n.chart = new bi(r.value);
    }
    function M() {
      n.mergedConfig = Ce(
        $e(n.defaultConfig, true),
        t.config || {}
      );
    }
    function U() {
      const R = F();
      n.chart.setOption(R, true), v();
    }
    function F() {
      const R = A();
      return n.mergedConfig.data.forEach((N) => {
        N.radius = R;
      }), {
        series: [
          {
            type: "pie",
            ...n.mergedConfig,
            outsideLabel: {
              show: false
            }
          }
        ],
        color: n.mergedConfig.color
      };
    }
    function A(R = false) {
      const { radius: N, activeRadius: I, lineWidth: E } = n.mergedConfig, b = Math.min(...n.chart.render.area) / 2, $ = E / 2;
      let f = R ? I : N;
      typeof f != "number" && (f = parseInt(f) / 100 * b);
      const _ = f - $, O = f + $;
      return [_, O];
    }
    function v() {
      const R = A(), N = A(true), I = F(), { data: E } = I.series[0];
      E.forEach(($, f) => {
        f === n.activeIndex ? $.radius = N : $.radius = R;
      }), n.chart.setOption(I, true);
      const { activeTimeGap: b } = I.series[0];
      n.animationHandler = setTimeout(($) => {
        n.activeIndex += 1, n.activeIndex >= E.length && (n.activeIndex = 0), v();
      }, b);
    }
    return (R, N) => (openBlock(), createElementBlock("div", Uu, [
      createBaseVNode("div", {
        ref_key: "activeRingChart",
        ref: r,
        class: "active-ring-chart-container"
      }, null, 512),
      createBaseVNode("div", Xu, [
        createVNode(xi, { config: unref(a) }, null, 8, ["config"]),
        createBaseVNode("div", {
          class: "active-ring-name",
          style: normalizeStyle(unref(l))
        }, toDisplayString(unref(o)), 5)
      ])
    ]));
  }
};
var Vr = {
  install(e) {
    e.component("DvActiveRingChart", Qu);
  }
};
var Ur = {
  install(e) {
    e.component("DvDigitalFlop", xi);
  }
};
var Yu = defineComponent({
  __name: "index",
  setup(e) {
    const t = ref(null), r = reactive({
      allWidth: 0,
      scale: 0,
      datavRoot: "",
      ready: false
    }), n = () => {
      const { width: s, height: D } = screen;
      r.allWidth = s, t.value && (t.value.style.width = `${s}px`, t.value.style.height = `${D}px`);
    }, a = () => {
      const s = document.body.clientWidth;
      t.value && (t.value.style.transform = `scale(${s / r.allWidth})`);
    };
    return xe(t, () => {
      a();
    }, () => {
      n(), a(), r.ready = true;
    }), (s, D) => (openBlock(), createElementBlock("div", {
      id: "dv-full-screen-container",
      ref_key: "fullScreenContainer",
      ref: t
    }, [
      unref(r).ready ? renderSlot(s.$slots, "default", { key: 0 }) : createCommentVNode("", true)
    ], 512));
  }
});
var Xr = {
  install(e) {
    e.component("DvFullScreenContainer", Yu);
  }
};
var Ku = ["width", "height"];
var Ju = ["fill", "x", "y", "width", "height"];
var Zu = ["values", "begin"];
var ec = ["fill", "x", "y", "width", "height"];
var tc = ["values"];
var rc = ["values"];
var nc = ["values"];
var ac = ["values"];
var ic = ["fill", "x", "y", "height"];
var oc = createBaseVNode("animate", {
  attributeName: "width",
  values: "0;40;0",
  dur: "2s",
  repeatCount: "indefinite"
}, null, -1);
var lc = ["values"];
var sc = defineComponent({
  __name: "index",
  props: {
    color: {
      type: Array,
      default: () => []
    }
  },
  setup(e) {
    const t = e, r = ref(null), n = reactive([200, 50]), a = ref(4), o = ref(20), l = ref(2.5), s = ref(l.value / 2), D = reactive(["#fff", "#0de7c2"]), W = reactive({
      mergedColor: [],
      rects: [],
      points: [],
      svgScale: [1, 1]
    }), M = () => {
      I();
    }, U = () => {
      I();
    }, { width: F, height: A } = xe(r, M, U), v = () => {
      const [b, $] = n, f = b / (o.value + 1), _ = $ / (a.value + 1), O = new Array(a.value).fill(0).map((m, C) => new Array(o.value).fill(0).map((d, G) => [
        f * (G + 1),
        _ * (C + 1)
      ]));
      W.points = O.reduce((m, C) => [...m, ...C], []);
    }, R = () => {
      const b = W.points[o.value * 2 - 1], $ = W.points[o.value * 2 - 3];
      W.rects = [b, $];
    }, N = () => {
      const [b, $] = n;
      W.svgScale = [F.value / b, A.value / $];
    }, I = () => {
      v(), R(), N();
    }, E = () => {
      W.mergedColor = Ce($e(D, true), t.color || []);
    };
    return watch(() => t.color, () => {
      E();
    }), onMounted(() => {
      E();
    }), (b, $) => (openBlock(), createElementBlock("div", {
      ref_key: "dvDecoration1",
      ref: r,
      class: "dv-decoration-1"
    }, [
      (openBlock(), createElementBlock("svg", {
        width: `${unref(n)[0]}px`,
        height: `${unref(n)[1]}px`,
        style: normalizeStyle(`transform:scale(${unref(W).svgScale[0]}, ${unref(W).svgScale[1]});`)
      }, [
        (openBlock(true), createElementBlock(Fragment, null, renderList(unref(W).points, (f) => (openBlock(), createElementBlock(Fragment, { key: f }, [
          Math.random() > 0.6 ? (openBlock(), createElementBlock("rect", {
            key: 0,
            fill: unref(W).mergedColor[0],
            x: f[0] - unref(s),
            y: f[1] - unref(s),
            width: unref(l),
            height: unref(l)
          }, [
            Math.random() > 0.6 ? (openBlock(), createElementBlock("animate", {
              key: 0,
              attributeName: "fill",
              values: `${unref(W).mergedColor[0]};transparent`,
              dur: "1s",
              begin: Math.random() * 2,
              repeatCount: "indefinite"
            }, null, 8, Zu)) : createCommentVNode("", true)
          ], 8, Ju)) : createCommentVNode("", true)
        ], 64))), 128)),
        unref(W).rects[0] ? (openBlock(), createElementBlock("rect", {
          key: 0,
          fill: unref(W).mergedColor[1],
          x: unref(W).rects[0][0] - unref(l),
          y: unref(W).rects[0][1] - unref(l),
          width: unref(l) * 2,
          height: unref(l) * 2
        }, [
          createBaseVNode("animate", {
            attributeName: "width",
            values: `0;${unref(l) * 2}`,
            dur: "2s",
            repeatCount: "indefinite"
          }, null, 8, tc),
          createBaseVNode("animate", {
            attributeName: "height",
            values: `0;${unref(l) * 2}`,
            dur: "2s",
            repeatCount: "indefinite"
          }, null, 8, rc),
          createBaseVNode("animate", {
            attributeName: "x",
            values: `${unref(W).rects[0][0]};${unref(W).rects[0][0] - unref(l)}`,
            dur: "2s",
            repeatCount: "indefinite"
          }, null, 8, nc),
          createBaseVNode("animate", {
            attributeName: "y",
            values: `${unref(W).rects[0][1]};${unref(W).rects[0][1] - unref(l)}`,
            dur: "2s",
            repeatCount: "indefinite"
          }, null, 8, ac)
        ], 8, ec)) : createCommentVNode("", true),
        unref(W).rects[1] ? (openBlock(), createElementBlock("rect", {
          key: 1,
          fill: unref(W).mergedColor[1],
          x: unref(W).rects[1][0] - 40,
          y: unref(W).rects[1][1] - unref(l),
          width: 40,
          height: unref(l) * 2
        }, [
          oc,
          createBaseVNode("animate", {
            attributeName: "x",
            values: `${unref(W).rects[1][0]};${unref(W).rects[1][0] - 40};${unref(W).rects[1][0]}`,
            dur: "2s",
            repeatCount: "indefinite"
          }, null, 8, lc)
        ], 8, ic)) : createCommentVNode("", true)
      ], 12, Ku))
    ], 512));
  }
});
var Qr = {
  install(e) {
    e.component("DvDecoration1", sc);
  }
};
var uc = ["width", "height"];
var cc = ["x", "y", "width", "height", "fill"];
var fc = ["attributeName", "to", "dur"];
var dc = ["x", "y", "fill"];
var hc = ["attributeName", "to", "dur"];
var vc = defineComponent({
  __name: "index",
  props: {
    color: {
      type: Array,
      default: () => []
    },
    reverse: {
      type: Boolean,
      default: false
    },
    dur: {
      type: Number,
      default: 6
    }
  },
  setup(e) {
    const t = e, r = ref(null), n = reactive({
      x: 0,
      y: 0,
      w: 0,
      h: 0,
      defaultColor: ["#3faacb", "#fff"],
      mergedColor: []
    }), a = () => {
      n.mergedColor = Ce($e(n.defaultColor, true), t.color || []);
    }, o = () => {
      W();
    }, l = () => {
      W();
    }, { width: s, height: D } = xe(r, o, l), W = () => {
      t.reverse ? (n.w = 1, n.h = D.value, n.x = s.value / 2, n.y = 0) : (n.w = s.value, n.h = 1, n.x = 0, n.y = D.value / 2);
    };
    return watch(() => t.color, () => {
      a();
    }), watch(() => t.reverse, () => {
      W();
    }), onMounted(() => {
      a();
    }), (M, U) => (openBlock(), createElementBlock("div", {
      ref_key: "decoration2",
      ref: r,
      class: "dv-decoration-2"
    }, [
      (openBlock(), createElementBlock("svg", {
        width: `${unref(s)}px`,
        height: `${unref(D)}px`
      }, [
        createBaseVNode("rect", {
          x: unref(n).x,
          y: unref(n).y,
          width: unref(n).w,
          height: unref(n).h,
          fill: unref(n).mergedColor[0]
        }, [
          createBaseVNode("animate", {
            attributeName: e.reverse ? "height" : "width",
            from: "0",
            to: e.reverse ? unref(D) : unref(s),
            dur: `${e.dur}s`,
            calcMode: "spline",
            keyTimes: "0;1",
            keySplines: ".42,0,.58,1",
            repeatCount: "indefinite"
          }, null, 8, fc)
        ], 8, cc),
        createBaseVNode("rect", {
          x: unref(n).x,
          y: unref(n).y,
          width: "1",
          height: "1",
          fill: unref(n).mergedColor[1]
        }, [
          createBaseVNode("animate", {
            attributeName: e.reverse ? "y" : "x",
            from: "0",
            to: e.reverse ? unref(D) : unref(s),
            dur: `${e.dur}s`,
            calcMode: "spline",
            keyTimes: "0;1",
            keySplines: "0.42,0,0.58,1",
            repeatCount: "indefinite"
          }, null, 8, hc)
        ], 8, dc)
      ], 8, uc))
    ], 512));
  }
});
var Yr = {
  install(e) {
    e.component("DvDecoration2", vc);
  }
};
var pc = ["width", "height"];
var gc = ["fill", "x", "y"];
var mc = ["values", "dur", "begin"];
var It = 7;
var yc = defineComponent({
  __name: "index",
  props: {
    color: {
      type: Array,
      default: () => []
    }
  },
  setup(e) {
    const t = e, r = ref(null), n = reactive({
      svgWH: [300, 35],
      svgScale: [1, 1],
      rowNum: 2,
      rowPoints: 25,
      pointSideLength: It,
      halfPointSideLength: It / 2,
      points: [],
      defaultColor: ["#7acaec", "transparent"],
      mergedColor: []
    }), a = () => {
      const [F, A] = n.svgWH, v = F / (n.rowPoints + 1), R = A / (n.rowNum + 1), N = new Array(n.rowNum).fill(0).map((I, E) => new Array(n.rowPoints).fill(0).map((b, $) => [
        v * ($ + 1),
        R * (E + 1)
      ]));
      n.points = N.reduce((I, E) => [...I, ...E], []);
    }, o = () => {
      l();
    }, l = () => {
      a(), M();
    }, s = () => {
      l();
    }, { width: D, height: W } = xe(r, s, o), M = () => {
      const [F, A] = n.svgWH;
      n.svgScale = [D.value / F, W.value / A];
    }, U = () => {
      n.mergedColor = Ce($e(n.defaultColor, true), t.color || []);
    };
    return watch(() => t.color, () => {
      U();
    }), onMounted(() => {
      U();
    }), (F, A) => (openBlock(), createElementBlock("div", {
      ref_key: "decoration3",
      ref: r,
      class: "dv-decoration-3"
    }, [
      (openBlock(), createElementBlock("svg", {
        width: `${unref(n).svgWH[0]}px`,
        height: `${unref(n).svgWH[1]}px`,
        style: normalizeStyle(`transform:scale(${unref(n).svgScale[0]},${unref(n).svgScale[1]});`)
      }, [
        (openBlock(true), createElementBlock(Fragment, null, renderList(unref(n).points, (v) => (openBlock(), createElementBlock("rect", {
          key: v,
          fill: unref(n).mergedColor[0],
          x: v[0] - unref(n).halfPointSideLength,
          y: v[1] - unref(n).halfPointSideLength,
          width: It,
          height: It
        }, [
          Math.random() > 0.6 ? (openBlock(), createElementBlock("animate", {
            key: 0,
            attributeName: "fill",
            values: `${unref(n).mergedColor.join(";")}`,
            dur: Math.random() + 1 + "s",
            begin: Math.random() * 2,
            repeatCount: "indefinite"
          }, null, 8, mc)) : createCommentVNode("", true)
        ], 8, gc))), 128))
      ], 12, pc))
    ], 512));
  }
});
var Kr = {
  install(e) {
    e.component("DvDecoration3", yc);
  }
};
var bc = ["width", "height"];
var xc = ["stroke", "points"];
var Cc = ["stroke", "points"];
var _c = defineComponent({
  __name: "index",
  props: {
    color: {
      type: Array,
      default: () => []
    },
    reverse: {
      type: Boolean,
      default: false
    },
    dur: {
      type: Number,
      default: 3
    }
  },
  setup(e) {
    const t = e, r = ref(null), n = reactive({
      defaultColor: ["rgba(255, 255, 255, 0.3)", "rgba(255, 255, 255, 0.3)"],
      mergedColor: []
    }), a = () => {
      n.mergedColor = Ce($e(n.defaultColor, true), t.color || []);
    }, { width: o, height: l } = xe(r);
    return watch(() => t.color, () => {
      a();
    }), onMounted(() => {
      a();
    }), (s, D) => (openBlock(), createElementBlock("div", {
      ref_key: "decoration3",
      ref: r,
      class: "dv-decoration-4"
    }, [
      createBaseVNode("div", {
        class: normalizeClass(`container ${e.reverse ? "reverse" : "normal"}`),
        style: normalizeStyle(e.reverse ? `width:${unref(o)}px;height:5px;animation-duration:${e.dur}s` : `width:5px;height:${unref(l)}px;animation-duration:${e.dur}s`)
      }, [
        (openBlock(), createElementBlock("svg", {
          width: e.reverse ? unref(o) : 5,
          height: e.reverse ? 5 : unref(l)
        }, [
          createBaseVNode("polyline", {
            stroke: unref(n).mergedColor[0],
            points: e.reverse ? `0, 2.5 ${unref(o)}, 2.5` : `2.5, 0 2.5, ${unref(l)}`
          }, null, 8, xc),
          createBaseVNode("polyline", {
            class: "bold-line",
            stroke: unref(n).mergedColor[1],
            "stroke-width": "3",
            "stroke-dasharray": "20, 80",
            "stroke-dashoffset": "-30",
            points: e.reverse ? `0, 2.5 ${unref(o)}, 2.5` : `2.5, 0 2.5, ${unref(l)}`
          }, null, 8, Cc)
        ], 8, bc))
      ], 6)
    ], 512));
  }
});
var Jr = {
  install(e) {
    e.component("DvDecoration4", _c);
  }
};
var $c = ["width", "height"];
var Pc = ["stroke", "points"];
var wc = ["from", "to", "dur"];
var kc = ["stroke", "points"];
var Ac = ["from", "to", "dur"];
var Lc = defineComponent({
  __name: "index",
  props: {
    color: {
      type: Array,
      default: () => []
    },
    dur: {
      type: Number,
      default: 1.2
    }
  },
  setup(e) {
    const t = e, r = ref(null), n = reactive({
      line1Points: "",
      line2Points: "",
      line1Length: 0,
      line2Length: 0,
      defaultColor: ["#3f96a5", "#3f96a5"],
      mergedColor: []
    }), a = () => {
      D();
    }, o = () => {
      D();
    }, { width: l, height: s } = xe(r, o, a), D = () => {
      const M = [
        { x: 0, y: s.value * 0.2 },
        { x: l.value * 0.18, y: s.value * 0.2 },
        { x: l.value * 0.2, y: s.value * 0.4 },
        { x: l.value * 0.25, y: s.value * 0.4 },
        { x: l.value * 0.27, y: s.value * 0.6 },
        { x: l.value * 0.72, y: s.value * 0.6 },
        { x: l.value * 0.75, y: s.value * 0.4 },
        { x: l.value * 0.8, y: s.value * 0.4 },
        { x: l.value * 0.82, y: s.value * 0.2 },
        { x: l.value, y: s.value * 0.2 }
      ], U = [
        { x: l.value * 0.3, y: s.value * 0.8 },
        { x: l.value * 0.7, y: s.value * 0.8 }
      ], F = Nn(M), A = Nn(U);
      n.line1Points = jn(M), n.line2Points = jn(U), n.line1Length = F, n.line2Length = A;
    }, W = () => {
      n.mergedColor = Ce($e(n.defaultColor, true), t.color || []);
    };
    return watch(() => t.color, () => {
      W();
    }), onMounted(() => {
      W();
    }), (M, U) => (openBlock(), createElementBlock("div", {
      ref_key: "decoration5",
      ref: r,
      class: "dv-decoration-5"
    }, [
      (openBlock(), createElementBlock("svg", {
        width: unref(l),
        height: unref(s)
      }, [
        createBaseVNode("polyline", {
          fill: "transparent",
          stroke: unref(n).mergedColor[0],
          "stroke-width": "3",
          points: unref(n).line1Points
        }, [
          createBaseVNode("animate", {
            attributeName: "stroke-dasharray",
            attributeType: "XML",
            from: `0, ${unref(n).line1Length / 2}, 0, ${unref(n).line1Length / 2}`,
            to: `0, 0, ${unref(n).line1Length}, 0`,
            dur: `${e.dur}s`,
            begin: "0s",
            calcMode: "spline",
            keyTimes: "0;1",
            keySplines: "0.4,1,0.49,0.98",
            repeatCount: "indefinite"
          }, null, 8, wc)
        ], 8, Pc),
        createBaseVNode("polyline", {
          fill: "transparent",
          stroke: unref(n).mergedColor[1],
          "stroke-width": "2",
          points: unref(n).line2Points
        }, [
          createBaseVNode("animate", {
            attributeName: "stroke-dasharray",
            attributeType: "XML",
            from: `0, ${unref(n).line2Length / 2}, 0, ${unref(n).line2Length / 2}`,
            to: `0, 0, ${unref(n).line2Length}, 0`,
            dur: `${e.dur}s`,
            begin: "0s",
            calcMode: "spline",
            keyTimes: "0;1",
            keySplines: ".4,1,.49,.98",
            repeatCount: "indefinite"
          }, null, 8, Ac)
        ], 8, kc)
      ], 8, $c))
    ], 512));
  }
});
var Zr = {
  install(e) {
    e.component("DvDecoration5", Lc);
  }
};
var Sc = ["width", "height"];
var Oc = ["fill", "x", "y", "height"];
var Gc = ["values", "dur"];
var Mc = ["values", "dur"];
var en = 7;
var Rc = defineComponent({
  __name: "index",
  props: {
    color: {
      type: Array,
      default: () => []
    }
  },
  setup(e) {
    const t = e, r = ref(null), n = reactive({
      svgWH: [300, 35],
      svgScale: [1, 1],
      rowNum: 1,
      rowPoints: 40,
      rectWidth: en,
      halfRectWidth: en / 2,
      points: [],
      heights: [],
      minHeights: [],
      randoms: [],
      defaultColor: ["#7acaec", "#7acaec"],
      mergedColor: []
    });
    watch(() => t.color, () => {
      U();
    }), onMounted(() => {
      U();
    });
    const { width: a, height: o } = xe(r, M, l);
    function l() {
      s();
    }
    function s() {
      D(), W();
    }
    function D() {
      const [F, A] = n.svgWH, v = F / (n.rowPoints + 1), R = A / (n.rowNum + 1), N = new Array(n.rowNum).fill(0).map((E, b) => new Array(n.rowPoints).fill(0).map(($, f) => [
        v * (f + 1),
        R * (b + 1)
      ]));
      n.points = N.reduce((E, b) => [...E, ...b], []);
      const I = n.heights = new Array(n.rowNum * n.rowPoints).fill(0).map((E) => Math.random() > 0.8 ? _t(0.7 * A, A) : _t(0.2 * A, 0.5 * A));
      n.minHeights = new Array(n.rowNum * n.rowPoints).fill(0).map((E, b) => I[b] * Math.random()), n.randoms = new Array(n.rowNum * n.rowPoints).fill(0).map((E) => Math.random() + 1.5);
    }
    function W() {
      const [F, A] = n.svgWH;
      n.svgScale = [a.value / F, o.value / A];
    }
    function M() {
      s();
    }
    function U() {
      n.mergedColor = Ce($e(n.defaultColor, true), t.color || []);
    }
    return (F, A) => (openBlock(), createElementBlock("div", {
      ref_key: "decoration6",
      ref: r,
      class: "dv-decoration-6"
    }, [
      (openBlock(), createElementBlock("svg", {
        width: `${unref(n).svgWH[0]}px`,
        height: `${unref(n).svgWH[1]}px`,
        style: normalizeStyle(`transform:scale(${unref(n).svgScale[0]},${unref(n).svgScale[1]});`)
      }, [
        (openBlock(true), createElementBlock(Fragment, null, renderList(unref(n).points, (v, R) => (openBlock(), createElementBlock("rect", {
          key: R,
          fill: unref(n).mergedColor[Math.random() > 0.5 ? 0 : 1],
          x: v[0] - unref(n).halfRectWidth,
          y: v[1] - unref(n).heights[R] / 2,
          width: en,
          height: unref(n).heights[R]
        }, [
          createBaseVNode("animate", {
            attributeName: "y",
            values: `${v[1] - unref(n).minHeights[R] / 2};${v[1] - unref(n).heights[R] / 2};${v[1] - unref(n).minHeights[R] / 2}`,
            dur: `${unref(n).randoms[R]}s`,
            keyTimes: "0;0.5;1",
            calcMode: "spline",
            keySplines: "0.42,0,0.58,1;0.42,0,0.58,1",
            begin: "0s",
            repeatCount: "indefinite"
          }, null, 8, Gc),
          createBaseVNode("animate", {
            attributeName: "height",
            values: `${unref(n).minHeights[R]};${unref(n).heights[R]};${unref(n).minHeights[R]}`,
            dur: `${unref(n).randoms[R]}s`,
            keyTimes: "0;0.5;1",
            calcMode: "spline",
            keySplines: "0.42,0,0.58,1;0.42,0,0.58,1",
            begin: "0s",
            repeatCount: "indefinite"
          }, null, 8, Mc)
        ], 8, Oc))), 128))
      ], 12, Sc))
    ], 512));
  }
});
var tn = {
  install(e) {
    e.component("DvDecoration6", Rc);
  }
};
var Tc = { class: "dv-decoration-7" };
var Dc = {
  width: "21px",
  height: "20px"
};
var Bc = ["stroke"];
var Fc = ["stroke"];
var Nc = {
  width: "21px",
  height: "20px"
};
var jc = ["stroke"];
var Ec = ["stroke"];
var Wc = defineComponent({
  __name: "index",
  props: {
    color: {
      type: Array,
      default: () => []
    }
  },
  setup(e) {
    const t = e, r = reactive({
      defaultColor: ["#1dc1f5", "#1dc1f5"],
      mergedColor: []
    });
    watch(() => t.color, () => {
      n();
    }), onMounted(() => {
      n();
    });
    function n() {
      r.mergedColor = Ce($e(r.defaultColor, true), t.color || []);
    }
    return (a, o) => (openBlock(), createElementBlock("div", Tc, [
      (openBlock(), createElementBlock("svg", Dc, [
        createBaseVNode("polyline", {
          "stroke-width": "4",
          fill: "transparent",
          stroke: unref(r).mergedColor[0],
          points: "10, 0 19, 10 10, 20"
        }, null, 8, Bc),
        createBaseVNode("polyline", {
          "stroke-width": "2",
          fill: "transparent",
          stroke: unref(r).mergedColor[1],
          points: "2, 0 11, 10 2, 20"
        }, null, 8, Fc)
      ])),
      renderSlot(a.$slots, "default"),
      (openBlock(), createElementBlock("svg", Nc, [
        createBaseVNode("polyline", {
          "stroke-width": "4",
          fill: "transparent",
          stroke: unref(r).mergedColor[0],
          points: "11, 0 2, 10 11, 20"
        }, null, 8, jc),
        createBaseVNode("polyline", {
          "stroke-width": "2",
          fill: "transparent",
          stroke: unref(r).mergedColor[1],
          points: "19, 0 10, 10 19, 20"
        }, null, 8, Ec)
      ]))
    ]));
  }
});
var rn = {
  install(e) {
    e.component("DvDecoration7", Wc);
  }
};
var zc = ["width", "height"];
var qc = ["stroke", "points"];
var Ic = ["stroke", "points"];
var Hc = ["stroke", "points"];
var Vc = defineComponent({
  __name: "index",
  props: {
    color: {
      type: Array,
      default: () => []
    },
    reverse: {
      type: Boolean,
      default: false
    }
  },
  setup(e) {
    const t = e, r = ref(null), n = reactive({
      defaultColor: ["#3f96a5", "#3f96a5"],
      mergedColor: []
    });
    watch(() => t.color, () => {
      s();
    }), onMounted(() => {
      s();
    });
    const { width: a, height: o } = xe(r);
    function l(D) {
      return t.reverse ? a.value - D : D;
    }
    function s() {
      n.mergedColor = Ce($e(n.defaultColor, true), t.color || []);
    }
    return (D, W) => (openBlock(), createElementBlock("div", {
      ref_key: "decoration8",
      ref: r,
      class: "dv-decoration-8"
    }, [
      (openBlock(), createElementBlock("svg", {
        width: unref(a),
        height: unref(o)
      }, [
        createBaseVNode("polyline", {
          stroke: unref(n).mergedColor[0],
          "stroke-width": "2",
          fill: "transparent",
          points: `${l(0)}, 0 ${l(30)}, ${unref(o) / 2}`
        }, null, 8, qc),
        createBaseVNode("polyline", {
          stroke: unref(n).mergedColor[0],
          "stroke-width": "2",
          fill: "transparent",
          points: `${l(20)}, 0 ${l(50)}, ${unref(o) / 2} ${l(unref(a))}, ${unref(o) / 2}`
        }, null, 8, Ic),
        createBaseVNode("polyline", {
          stroke: unref(n).mergedColor[1],
          fill: "transparent",
          "stroke-width": "3",
          points: `${l(0)}, ${unref(o) - 3}, ${l(200)}, ${unref(o) - 3}`
        }, null, 8, Hc)
      ], 8, zc))
    ], 512));
  }
});
var nn = {
  install(e) {
    e.component("DvDecoration8", Vc);
  }
};
var Uc = ["width", "height"];
var Xc = ["id"];
var Qc = ["stroke"];
var Yc = ["dur"];
var Kc = ["stroke"];
var Jc = ["dur"];
var Zc = ["stroke"];
var ef = ["xlink:href", "stroke", "fill"];
var tf = ["dur", "begin"];
var rf = ["stroke"];
var nf = {
  __name: "index",
  props: {
    color: {
      type: Array,
      default: () => []
    },
    dur: {
      type: Number,
      default: 3
    }
  },
  setup(e) {
    const t = e, r = Ve(), n = ref(null), a = reactive({
      polygonId: `decoration-9-polygon-${r}`,
      svgWH: [100, 100],
      svgScale: [1, 1],
      defaultColor: ["rgba(3, 166, 224, 0.8)", "rgba(3, 166, 224, 0.5)"],
      mergedColor: []
    });
    watch(() => t.color, () => {
      M();
    }), onMounted(() => {
      M();
    });
    const { width: o, height: l } = xe(n, W, s);
    function s() {
      D();
    }
    function D() {
      const [U, F] = a.svgWH;
      a.svgScale = [o.value / U, l.value / F];
    }
    function W() {
      D();
    }
    function M() {
      a.mergedColor = Ce($e(a.defaultColor, true), t.color || []);
    }
    return (U, F) => (openBlock(), createElementBlock("div", {
      ref_key: "decoration9",
      ref: n,
      class: "dv-decoration-9"
    }, [
      (openBlock(), createElementBlock("svg", {
        width: `${unref(a).svgWH[0]}px`,
        height: `${unref(a).svgWH[1]}px`,
        style: normalizeStyle(`transform:scale(${unref(a).svgScale[0]},${unref(a).svgScale[1]});`)
      }, [
        createBaseVNode("defs", null, [
          createBaseVNode("polygon", {
            id: unref(a).polygonId,
            points: "15, 46.5, 21, 47.5, 21, 52.5, 15, 53.5"
          }, null, 8, Xc)
        ]),
        createBaseVNode("circle", {
          cx: "50",
          cy: "50",
          r: "45",
          fill: "transparent",
          stroke: unref(a).mergedColor[1],
          "stroke-width": "10",
          "stroke-dasharray": "80, 100, 30, 100"
        }, [
          createBaseVNode("animateTransform", {
            attributeName: "transform",
            type: "rotate",
            values: "0 50 50;360 50 50",
            dur: `${e.dur}s`,
            repeatCount: "indefinite"
          }, null, 8, Yc)
        ], 8, Qc),
        createBaseVNode("circle", {
          cx: "50",
          cy: "50",
          r: "45",
          fill: "transparent",
          stroke: unref(a).mergedColor[0],
          "stroke-width": "6",
          "stroke-dasharray": "50, 66, 100, 66"
        }, [
          createBaseVNode("animateTransform", {
            attributeName: "transform",
            type: "rotate",
            values: "0 50 50;-360 50 50",
            dur: `${e.dur}s`,
            repeatCount: "indefinite"
          }, null, 8, Jc)
        ], 8, Kc),
        createBaseVNode("circle", {
          cx: "50",
          cy: "50",
          r: "38",
          fill: "transparent",
          stroke: unref(De)(unref(a).mergedColor[1] || unref(a).defaultColor[1], 30),
          "stroke-width": "1",
          "stroke-dasharray": "5, 1"
        }, null, 8, Zc),
        (openBlock(true), createElementBlock(Fragment, null, renderList(new Array(20).fill(0), (A, v) => (openBlock(), createElementBlock("use", {
          key: v,
          "xlink:href": `#${unref(a).polygonId}`,
          stroke: unref(a).mergedColor[1],
          fill: Math.random() > 0.4 ? "transparent" : unref(a).mergedColor[0]
        }, [
          createBaseVNode("animateTransform", {
            attributeName: "transform",
            type: "rotate",
            values: "0 50 50;360 50 50",
            dur: `${e.dur}s`,
            begin: `${v * e.dur / 20}s`,
            repeatCount: "indefinite"
          }, null, 8, tf)
        ], 8, ef))), 128)),
        createBaseVNode("circle", {
          cx: "50",
          cy: "50",
          r: "26",
          fill: "transparent",
          stroke: unref(De)(unref(a).mergedColor[1] || unref(a).defaultColor[1], 30),
          "stroke-width": "1",
          "stroke-dasharray": "5, 1"
        }, null, 8, rf)
      ], 12, Uc)),
      renderSlot(U.$slots, "default")
    ], 512));
  }
};
var an = {
  install(e) {
    e.component("DvDecoration9", nf);
  }
};
var af = ["width", "height"];
var of = ["stroke", "points"];
var lf = ["stroke", "points", "stroke-dasharray"];
var sf = ["id", "values", "begin"];
var uf = ["values", "begin"];
var cf = ["stroke", "points", "stroke-dasharray"];
var ff = ["id", "values", "begin"];
var df = ["values", "begin"];
var hf = ["stroke", "points", "stroke-dasharray"];
var vf = ["id", "values", "begin"];
var pf = ["values", "begin"];
var gf = ["cy", "fill"];
var mf = ["id", "values", "begin"];
var yf = ["cx", "cy", "fill"];
var bf = ["id", "values", "begin"];
var xf = ["values", "begin"];
var Cf = ["cx", "cy", "fill"];
var _f = ["id", "values", "begin"];
var $f = ["values", "begin"];
var Pf = ["cx", "cy", "fill"];
var wf = ["id", "values", "begin"];
var kf = ["values", "begin"];
var Af = defineComponent({
  __name: "index",
  props: {
    color: {
      type: Array,
      default: () => []
    }
  },
  setup(e) {
    const t = e, r = Ve(), n = ref(null), a = reactive({
      animationId1: `d10ani1${r}`,
      animationId2: `d10ani2${r}`,
      animationId3: `d10ani3${r}`,
      animationId4: `d10ani4${r}`,
      animationId5: `d10ani5${r}`,
      animationId6: `d10ani6${r}`,
      animationId7: `d10ani7${r}`,
      defaultColor: ["#00c2ff", "rgba(0, 194, 255, 0.3)"],
      mergedColor: []
    }), { width: o, height: l } = xe(n);
    watch(() => t.color, () => {
      s();
    }), onMounted(() => {
      s();
    });
    function s() {
      a.mergedColor = Ce($e(a.defaultColor, true), t.color || []);
    }
    return (D, W) => (openBlock(), createElementBlock("div", {
      ref_key: "decoration10",
      ref: n,
      class: "dv-decoration-10"
    }, [
      (openBlock(), createElementBlock("svg", {
        width: unref(o),
        height: unref(l)
      }, [
        createBaseVNode("polyline", {
          stroke: unref(a).mergedColor[1],
          "stroke-width": "2",
          points: `0, ${unref(l) / 2} ${unref(o)}, ${unref(l) / 2}`
        }, null, 8, of),
        createBaseVNode("polyline", {
          stroke: unref(a).mergedColor[0],
          "stroke-width": "2",
          points: `5, ${unref(l) / 2} ${unref(o) * 0.2 - 3}, ${unref(l) / 2}`,
          "stroke-dasharray": `0, ${unref(o) * 0.2}`,
          fill: "freeze"
        }, [
          createBaseVNode("animate", {
            id: unref(a).animationId2,
            attributeName: "stroke-dasharray",
            values: `0, ${unref(o) * 0.2};${unref(o) * 0.2}, 0;`,
            dur: "3s",
            begin: `${unref(a).animationId1}.end`,
            fill: "freeze"
          }, null, 8, sf),
          createBaseVNode("animate", {
            attributeName: "stroke-dasharray",
            values: `${unref(o) * 0.2}, 0;0, ${unref(o) * 0.2}`,
            dur: "0.01s",
            begin: `${unref(a).animationId7}.end`,
            fill: "freeze"
          }, null, 8, uf)
        ], 8, lf),
        createBaseVNode("polyline", {
          stroke: unref(a).mergedColor[0],
          "stroke-width": "2",
          points: `${unref(o) * 0.2 + 3}, ${unref(l) / 2} ${unref(o) * 0.8 - 3}, ${unref(l) / 2}`,
          "stroke-dasharray": `0, ${unref(o) * 0.6}`
        }, [
          createBaseVNode("animate", {
            id: unref(a).animationId4,
            attributeName: "stroke-dasharray",
            values: `0, ${unref(o) * 0.6};${unref(o) * 0.6}, 0`,
            dur: "3s",
            begin: `${unref(a).animationId3}.end + 1s`,
            fill: "freeze"
          }, null, 8, ff),
          createBaseVNode("animate", {
            attributeName: "stroke-dasharray",
            values: `${unref(o) * 0.6}, 0;0, ${unref(o) * 0.6}`,
            dur: "0.01s",
            begin: `${unref(a).animationId7}.end`,
            fill: "freeze"
          }, null, 8, df)
        ], 8, cf),
        createBaseVNode("polyline", {
          stroke: unref(a).mergedColor[0],
          "stroke-width": "2",
          points: `${unref(o) * 0.8 + 3}, ${unref(l) / 2} ${unref(o) - 5}, ${unref(l) / 2}`,
          "stroke-dasharray": `0, ${unref(o) * 0.2}`
        }, [
          createBaseVNode("animate", {
            id: unref(a).animationId6,
            attributeName: "stroke-dasharray",
            values: `0, ${unref(o) * 0.2};${unref(o) * 0.2}, 0`,
            dur: "3s",
            begin: `${unref(a).animationId5}.end + 1s`,
            fill: "freeze"
          }, null, 8, vf),
          createBaseVNode("animate", {
            attributeName: "stroke-dasharray",
            values: `${unref(o) * 0.2}, 0;0, ${unref(o) * 0.3}`,
            dur: "0.01s",
            begin: `${unref(a).animationId7}.end`,
            fill: "freeze"
          }, null, 8, pf)
        ], 8, hf),
        createBaseVNode("circle", {
          cx: "2",
          cy: unref(l) / 2,
          r: "2",
          fill: unref(a).mergedColor[1]
        }, [
          createBaseVNode("animate", {
            id: unref(a).animationId1,
            attributeName: "fill",
            values: `${unref(a).mergedColor[1]};${unref(a).mergedColor[0]}`,
            begin: `0s;${unref(a).animationId7}.end`,
            dur: "0.3s",
            fill: "freeze"
          }, null, 8, mf)
        ], 8, gf),
        createBaseVNode("circle", {
          cx: unref(o) * 0.2,
          cy: unref(l) / 2,
          r: "2",
          fill: unref(a).mergedColor[1]
        }, [
          createBaseVNode("animate", {
            id: unref(a).animationId3,
            attributeName: "fill",
            values: `${unref(a).mergedColor[1]};${unref(a).mergedColor[0]}`,
            begin: `${unref(a).animationId2}.end`,
            dur: "0.3s",
            fill: "freeze"
          }, null, 8, bf),
          createBaseVNode("animate", {
            attributeName: "fill",
            values: `${unref(a).mergedColor[1]};${unref(a).mergedColor[1]}`,
            dur: "0.01s",
            begin: `${unref(a).animationId7}.end`,
            fill: "freeze"
          }, null, 8, xf)
        ], 8, yf),
        createBaseVNode("circle", {
          cx: unref(o) * 0.8,
          cy: unref(l) / 2,
          r: "2",
          fill: unref(a).mergedColor[1]
        }, [
          createBaseVNode("animate", {
            id: unref(a).animationId5,
            attributeName: "fill",
            values: `${unref(a).mergedColor[1]};${unref(a).mergedColor[0]}`,
            begin: `${unref(a).animationId4}.end`,
            dur: "0.3s",
            fill: "freeze"
          }, null, 8, _f),
          createBaseVNode("animate", {
            attributeName: "fill",
            values: `${unref(a).mergedColor[1]};${unref(a).mergedColor[1]}`,
            dur: "0.01s",
            begin: `${unref(a).animationId7}.end`,
            fill: "freeze"
          }, null, 8, $f)
        ], 8, Cf),
        createBaseVNode("circle", {
          cx: unref(o) - 2,
          cy: unref(l) / 2,
          r: "2",
          fill: unref(a).mergedColor[1]
        }, [
          createBaseVNode("animate", {
            id: unref(a).animationId7,
            attributeName: "fill",
            values: `${unref(a).mergedColor[1]};${unref(a).mergedColor[0]}`,
            begin: `${unref(a).animationId6}.end`,
            dur: "0.3s",
            fill: "freeze"
          }, null, 8, wf),
          createBaseVNode("animate", {
            attributeName: "fill",
            values: `${unref(a).mergedColor[1]};${unref(a).mergedColor[1]}`,
            dur: "0.01s",
            begin: `${unref(a).animationId7}.end`,
            fill: "freeze"
          }, null, 8, kf)
        ], 8, Pf)
      ], 8, af))
    ], 512));
  }
});
var on = {
  install(e) {
    e.component("DvDecoration10", Af);
  }
};
var Lf = ["width", "height"];
var Sf = ["fill", "stroke"];
var Of = ["fill", "stroke", "points"];
var Gf = ["fill", "stroke", "points"];
var Mf = ["fill", "stroke", "points"];
var Rf = ["fill", "stroke", "points"];
var Tf = ["stroke", "points"];
var Df = ["stroke", "points"];
var Bf = { class: "decoration-content" };
var Ff = {
  __name: "index",
  props: {
    color: {
      type: Array,
      default: () => []
    }
  },
  setup(e) {
    const t = e, r = ref(null), n = reactive({
      defaultColor: ["#1a98fc", "#2cf7fe"],
      mergedColor: []
    }), { width: a, height: o } = xe(r);
    watch(() => t.color, () => {
      l();
    }), onMounted(() => {
      l();
    });
    function l() {
      n.mergedColor = Ce($e(n.defaultColor, true), t.color || []);
    }
    return (s, D) => (openBlock(), createElementBlock("div", {
      ref_key: "decoration11",
      ref: r,
      class: "dv-decoration-11"
    }, [
      (openBlock(), createElementBlock("svg", {
        width: unref(a),
        height: unref(o)
      }, [
        createBaseVNode("polygon", {
          fill: unref(De)(unref(n).mergedColor[1] || unref(n).defaultColor[1], 10),
          stroke: unref(n).mergedColor[1],
          points: "20 10, 25 4, 55 4 60 10"
        }, null, 8, Sf),
        createBaseVNode("polygon", {
          fill: unref(De)(unref(n).mergedColor[1] || unref(n).defaultColor[1], 10),
          stroke: unref(n).mergedColor[1],
          points: `20 ${unref(o) - 10}, 25 ${unref(o) - 4}, 55 ${unref(o) - 4} 60 ${unref(o) - 10}`
        }, null, 8, Of),
        createBaseVNode("polygon", {
          fill: unref(De)(unref(n).mergedColor[1] || unref(n).defaultColor[1], 10),
          stroke: unref(n).mergedColor[1],
          points: `${unref(a) - 20} 10, ${unref(a) - 25} 4, ${unref(a) - 55} 4 ${unref(a) - 60} 10`
        }, null, 8, Gf),
        createBaseVNode("polygon", {
          fill: unref(De)(unref(n).mergedColor[1] || unref(n).defaultColor[1], 10),
          stroke: unref(n).mergedColor[1],
          points: `${unref(a) - 20} ${unref(o) - 10}, ${unref(a) - 25} ${unref(o) - 4}, ${unref(a) - 55} ${unref(o) - 4} ${unref(a) - 60} ${unref(o) - 10}`
        }, null, 8, Mf),
        createBaseVNode("polygon", {
          fill: unref(De)(unref(n).mergedColor[0] || unref(n).defaultColor[0], 20),
          stroke: unref(n).mergedColor[0],
          points: `
          20 10, 5 ${unref(o) / 2} 20 ${unref(o) - 10}
          ${unref(a) - 20} ${unref(o) - 10} ${unref(a) - 5} ${unref(o) / 2} ${unref(a) - 20} 10
        `
        }, null, 8, Rf),
        createBaseVNode("polyline", {
          fill: "transparent",
          stroke: unref(De)(unref(n).mergedColor[0] || unref(n).defaultColor[0], 70),
          points: `25 18, 15 ${unref(o) / 2} 25 ${unref(o) - 18}`
        }, null, 8, Tf),
        createBaseVNode("polyline", {
          fill: "transparent",
          stroke: unref(De)(unref(n).mergedColor[0] || unref(n).defaultColor[0], 70),
          points: `${unref(a) - 25} 18, ${unref(a) - 15} ${unref(o) / 2} ${unref(a) - 25} ${unref(o) - 18}`
        }, null, 8, Df)
      ], 8, Lf)),
      createBaseVNode("div", Bf, [
        renderSlot(s.$slots, "default")
      ])
    ], 512));
  }
};
var ln = {
  install(e) {
    e.component("DvDecoration11", Ff);
  }
};
var Nf = ["width", "height"];
var jf = ["id"];
var Ef = ["stroke", "stroke-width", "d"];
var Wf = ["id"];
var zf = createBaseVNode("stop", {
  offset: "0%",
  "stop-color": "transparent",
  "stop-opacity": "1"
}, null, -1);
var qf = ["stop-color"];
var If = ["r", "cx", "cy", "stroke"];
var Hf = ["cx", "cy", "fill"];
var Vf = ["values", "dur"];
var Uf = ["dur"];
var Xf = ["cx", "cy", "fill"];
var Qf = { key: 0 };
var Yf = ["points", "stroke"];
var Kf = ["d", "stroke"];
var Jf = ["xlink:href"];
var Zf = ["values", "dur"];
var ed = { class: "decoration-content" };
var td = {
  __name: "index",
  props: {
    color: {
      type: Array,
      default: () => []
    },
    /**
       * @description Scan animation dur
       */
    scanDur: {
      type: Number,
      default: 3
    },
    /**
       * @description Halo animation dur
       */
    haloDur: {
      type: Number,
      default: 2
    }
  },
  setup(e) {
    const t = e, r = Ve(), n = ref(null), { width: a, height: o } = xe(n, () => {
    }, N), l = reactive({
      gId: `decoration-12-g-${r}`,
      gradientId: `decoration-12-gradient-${r}`,
      defaultColor: ["#2783ce", "#2cf7fe"],
      mergedColor: [],
      pathD: [],
      pathColor: [],
      circleR: [],
      splitLinePoints: [],
      arcD: [],
      segment: 30,
      sectorAngle: Math.PI / 3,
      ringNum: 3,
      ringWidth: 1,
      showSplitLine: true
    }), s = computed(() => a.value / 2), D = computed(() => o.value / 2);
    watch(() => t.color, () => {
      M(), F();
    });
    function W() {
      M(), U(), F(), A(), v(), R();
    }
    function M() {
      l.mergedColor = Ce($e(l.defaultColor, true), t.color || []);
    }
    function U() {
      const I = -Math.PI / 2, E = l.sectorAngle / l.segment, b = a.value / 4;
      let $ = at(s.value, D.value, b, I);
      l.pathD = new Array(l.segment).fill("").map((f, _) => {
        const O = at(s.value, D.value, b, I - (_ + 1) * E).map((C) => parseFloat(C.toFixed(5))), m = `M${$.join(",")} A${b}, ${b} 0 0 0 ${O.join(",")}`;
        return $ = O, m;
      });
    }
    function F() {
      const I = 100 / (l.segment - 1);
      l.pathColor = new Array(l.segment).fill(l.mergedColor[0]).map((E, b) => De(l.mergedColor[0], 100 - b * I));
    }
    function A() {
      const I = (a.value / 2 - l.ringWidth / 2) / l.ringNum;
      l.circleR = new Array(l.ringNum).fill(0).map((E, b) => I * (b + 1));
    }
    function v() {
      const I = Math.PI / 6, E = a.value / 2;
      l.splitLinePoints = new Array(6).fill("").map((b, $) => {
        const f = I * ($ + 1), _ = f + Math.PI, O = at(s.value, D.value, E, f), m = at(s.value, D.value, E, _);
        return `${O.join(",")} ${m.join(",")}`;
      });
    }
    function R() {
      const I = Math.PI / 6, E = a.value / 2 - 1;
      l.arcD = new Array(4).fill("").map((b, $) => {
        const f = I * (3 * $ + 1), _ = f + I, O = at(s.value, D.value, E, f), m = at(s.value, D.value, E, _);
        return `M${O.join(",")} A${s.value}, ${D.value} 0 0 1 ${m.join(",")}`;
      });
    }
    function N() {
      W();
    }
    return (I, E) => (openBlock(), createElementBlock("div", {
      ref_key: "decoration12",
      ref: n,
      class: "dv-decoration-12"
    }, [
      (openBlock(), createElementBlock("svg", {
        width: unref(a),
        height: unref(o)
      }, [
        createBaseVNode("defs", null, [
          createBaseVNode("g", {
            id: unref(l).gId
          }, [
            (openBlock(true), createElementBlock(Fragment, null, renderList(unref(l).pathD, (b, $) => (openBlock(), createElementBlock("path", {
              key: b,
              stroke: unref(l).pathColor[$],
              "stroke-width": unref(a) / 2,
              fill: "transparent",
              d: b
            }, null, 8, Ef))), 128))
          ], 8, jf),
          createBaseVNode("radialGradient", {
            id: unref(l).gradientId,
            cx: "50%",
            cy: "50%",
            r: "50%"
          }, [
            zf,
            createBaseVNode("stop", {
              offset: "100%",
              "stop-color": unref(De)(unref(l).mergedColor[1] || unref(l).defaultColor[1], 30),
              "stop-opacity": "1"
            }, null, 8, qf)
          ], 8, Wf)
        ]),
        (openBlock(true), createElementBlock(Fragment, null, renderList(unref(l).circleR, (b) => (openBlock(), createElementBlock("circle", {
          key: b,
          r: b,
          cx: unref(s),
          cy: unref(D),
          stroke: unref(l).mergedColor[1],
          "stroke-width": 0.8,
          fill: "transparent"
        }, null, 8, If))), 128)),
        createBaseVNode("circle", {
          r: "1",
          cx: unref(s),
          cy: unref(D),
          stroke: "transparent",
          fill: `url(#${unref(l).gradientId})`
        }, [
          createBaseVNode("animate", {
            attributeName: "r",
            values: `1;${unref(a) / 2}`,
            dur: `${e.haloDur}s`,
            repeatCount: "indefinite"
          }, null, 8, Vf),
          createBaseVNode("animate", {
            attributeName: "opacity",
            values: "1;0",
            dur: `${e.haloDur}s`,
            repeatCount: "indefinite"
          }, null, 8, Uf)
        ], 8, Hf),
        createBaseVNode("circle", {
          r: "2",
          cx: unref(s),
          cy: unref(D),
          fill: unref(l).mergedColor[1]
        }, null, 8, Xf),
        unref(l).showSplitLine ? (openBlock(), createElementBlock("g", Qf, [
          (openBlock(true), createElementBlock(Fragment, null, renderList(unref(l).splitLinePoints, (b) => (openBlock(), createElementBlock("polyline", {
            key: b,
            points: b,
            stroke: unref(l).mergedColor[1],
            "stroke-width": 0.5,
            opacity: "50"
          }, null, 8, Yf))), 128))
        ])) : createCommentVNode("", true),
        (openBlock(true), createElementBlock(Fragment, null, renderList(unref(l).arcD, (b) => (openBlock(), createElementBlock("path", {
          key: b,
          d: b,
          stroke: unref(l).mergedColor[1],
          "stroke-width": "2.3",
          fill: "transparent"
        }, null, 8, Kf))), 128)),
        createBaseVNode("use", {
          "xlink:href": `#${unref(l).gId}`
        }, [
          createBaseVNode("animateTransform", {
            attributeName: "transform",
            type: "rotate",
            values: `0, ${unref(s)} ${unref(D)};360, ${unref(s)} ${unref(D)}`,
            dur: `${e.scanDur}s`,
            repeatCount: "indefinite"
          }, null, 8, Zf)
        ], 8, Jf)
      ], 8, Nf)),
      createBaseVNode("div", ed, [
        renderSlot(I.$slots, "default")
      ])
    ], 512));
  }
};
var sn = {
  install(e) {
    e.component("DvDecoration12", td);
  }
};
var Ie = {
  color: {
    type: Array,
    default: () => []
  },
  backgroundColor: {
    type: String,
    default: "transparent"
  }
};
var rd = typeof global == "object" && global && global.Object === Object && global;
var Ci = rd;
var nd = typeof self == "object" && self && self.Object === Object && self;
var ad = Ci || nd || Function("return this")();
var ot = ad;
var id = ot.Symbol;
var Ut = id;
var _i = Object.prototype;
var od = _i.hasOwnProperty;
var ld = _i.toString;
var bt = Ut ? Ut.toStringTag : void 0;
function sd(e) {
  var t = od.call(e, bt), r = e[bt];
  try {
    e[bt] = void 0;
    var n = true;
  } catch {
  }
  var a = ld.call(e);
  return n && (t ? e[bt] = r : delete e[bt]), a;
}
var ud = Object.prototype;
var cd = ud.toString;
function fd(e) {
  return cd.call(e);
}
var dd = "[object Null]";
var hd = "[object Undefined]";
var qa = Ut ? Ut.toStringTag : void 0;
function tr(e) {
  return e == null ? e === void 0 ? hd : dd : qa && qa in Object(e) ? sd(e) : fd(e);
}
function Ot(e) {
  return e != null && typeof e == "object";
}
var vd = Array.isArray;
var kn = vd;
function nt(e) {
  var t = typeof e;
  return e != null && (t == "object" || t == "function");
}
function $i(e) {
  return e;
}
var pd = "[object AsyncFunction]";
var gd = "[object Function]";
var md = "[object GeneratorFunction]";
var yd = "[object Proxy]";
function Rn(e) {
  if (!nt(e))
    return false;
  var t = tr(e);
  return t == gd || t == md || t == pd || t == yd;
}
var bd = ot["__core-js_shared__"];
var un = bd;
var Ia = function() {
  var e = /[^.]+$/.exec(un && un.keys && un.keys.IE_PROTO || "");
  return e ? "Symbol(src)_1." + e : "";
}();
function xd(e) {
  return !!Ia && Ia in e;
}
var Cd = Function.prototype;
var _d = Cd.toString;
function $d(e) {
  if (e != null) {
    try {
      return _d.call(e);
    } catch {
    }
    try {
      return e + "";
    } catch {
    }
  }
  return "";
}
var Pd = /[\\^$.*+?()[\]{}|]/g;
var wd = /^\[object .+?Constructor\]$/;
var kd = Function.prototype;
var Ad = Object.prototype;
var Ld = kd.toString;
var Sd = Ad.hasOwnProperty;
var Od = RegExp(
  "^" + Ld.call(Sd).replace(Pd, "\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, "$1.*?") + "$"
);
function Gd(e) {
  if (!nt(e) || xd(e))
    return false;
  var t = Rn(e) ? Od : wd;
  return t.test($d(e));
}
function Md(e, t) {
  return e == null ? void 0 : e[t];
}
function Tn(e, t) {
  var r = Md(e, t);
  return Gd(r) ? r : void 0;
}
var Ha = Object.create;
var Rd = /* @__PURE__ */ function() {
  function e() {
  }
  return function(t) {
    if (!nt(t))
      return {};
    if (Ha)
      return Ha(t);
    e.prototype = t;
    var r = new e();
    return e.prototype = void 0, r;
  };
}();
var Td = Rd;
function Dd(e, t, r) {
  switch (r.length) {
    case 0:
      return e.call(t);
    case 1:
      return e.call(t, r[0]);
    case 2:
      return e.call(t, r[0], r[1]);
    case 3:
      return e.call(t, r[0], r[1], r[2]);
  }
  return e.apply(t, r);
}
function Bd(e, t) {
  var r = -1, n = e.length;
  for (t || (t = Array(n)); ++r < n; )
    t[r] = e[r];
  return t;
}
var Fd = 800;
var Nd = 16;
var jd = Date.now;
function Ed(e) {
  var t = 0, r = 0;
  return function() {
    var n = jd(), a = Nd - (n - r);
    if (r = n, a > 0) {
      if (++t >= Fd)
        return arguments[0];
    } else
      t = 0;
    return e.apply(void 0, arguments);
  };
}
function Wd(e) {
  return function() {
    return e;
  };
}
var zd = function() {
  try {
    var e = Tn(Object, "defineProperty");
    return e({}, "", {}), e;
  } catch {
  }
}();
var Xt = zd;
var qd = Xt ? function(e, t) {
  return Xt(e, "toString", {
    configurable: true,
    enumerable: false,
    value: Wd(t),
    writable: true
  });
} : $i;
var Id = qd;
var Hd = Ed(Id);
var Vd = Hd;
var Ud = 9007199254740991;
var Xd = /^(?:0|[1-9]\d*)$/;
function Pi(e, t) {
  var r = typeof e;
  return t = t ?? Ud, !!t && (r == "number" || r != "symbol" && Xd.test(e)) && e > -1 && e % 1 == 0 && e < t;
}
function Dn(e, t, r) {
  t == "__proto__" && Xt ? Xt(e, t, {
    configurable: true,
    enumerable: true,
    value: r,
    writable: true
  }) : e[t] = r;
}
function rr(e, t) {
  return e === t || e !== e && t !== t;
}
var Qd = Object.prototype;
var Yd = Qd.hasOwnProperty;
function Kd(e, t, r) {
  var n = e[t];
  (!(Yd.call(e, t) && rr(n, r)) || r === void 0 && !(t in e)) && Dn(e, t, r);
}
function Jd(e, t, r, n) {
  var a = !r;
  r || (r = {});
  for (var o = -1, l = t.length; ++o < l; ) {
    var s = t[o], D = n ? n(r[s], e[s], s, r, e) : void 0;
    D === void 0 && (D = e[s]), a ? Dn(r, s, D) : Kd(r, s, D);
  }
  return r;
}
var Va = Math.max;
function Zd(e, t, r) {
  return t = Va(t === void 0 ? e.length - 1 : t, 0), function() {
    for (var n = arguments, a = -1, o = Va(n.length - t, 0), l = Array(o); ++a < o; )
      l[a] = n[t + a];
    a = -1;
    for (var s = Array(t + 1); ++a < t; )
      s[a] = n[a];
    return s[t] = r(l), Dd(e, this, s);
  };
}
function eh(e, t) {
  return Vd(Zd(e, t, $i), e + "");
}
var th = 9007199254740991;
function wi(e) {
  return typeof e == "number" && e > -1 && e % 1 == 0 && e <= th;
}
function Bn(e) {
  return e != null && wi(e.length) && !Rn(e);
}
function rh(e, t, r) {
  if (!nt(r))
    return false;
  var n = typeof t;
  return (n == "number" ? Bn(r) && Pi(t, r.length) : n == "string" && t in r) ? rr(r[t], e) : false;
}
function nh(e) {
  return eh(function(t, r) {
    var n = -1, a = r.length, o = a > 1 ? r[a - 1] : void 0, l = a > 2 ? r[2] : void 0;
    for (o = e.length > 3 && typeof o == "function" ? (a--, o) : void 0, l && rh(r[0], r[1], l) && (o = a < 3 ? void 0 : o, a = 1), t = Object(t); ++n < a; ) {
      var s = r[n];
      s && e(t, s, n, o);
    }
    return t;
  });
}
var ah = Object.prototype;
function ki(e) {
  var t = e && e.constructor, r = typeof t == "function" && t.prototype || ah;
  return e === r;
}
function ih(e, t) {
  for (var r = -1, n = Array(e); ++r < e; )
    n[r] = t(r);
  return n;
}
var oh = "[object Arguments]";
function Ua(e) {
  return Ot(e) && tr(e) == oh;
}
var Ai = Object.prototype;
var lh = Ai.hasOwnProperty;
var sh = Ai.propertyIsEnumerable;
var uh = Ua(/* @__PURE__ */ function() {
  return arguments;
}()) ? Ua : function(e) {
  return Ot(e) && lh.call(e, "callee") && !sh.call(e, "callee");
};
var An = uh;
function ch() {
  return false;
}
var Li = typeof exports == "object" && exports && !exports.nodeType && exports;
var Xa = Li && typeof module == "object" && module && !module.nodeType && module;
var fh = Xa && Xa.exports === Li;
var Qa = fh ? ot.Buffer : void 0;
var dh = Qa ? Qa.isBuffer : void 0;
var hh = dh || ch;
var Si = hh;
var vh = "[object Arguments]";
var ph = "[object Array]";
var gh = "[object Boolean]";
var mh = "[object Date]";
var yh = "[object Error]";
var bh = "[object Function]";
var xh = "[object Map]";
var Ch = "[object Number]";
var _h = "[object Object]";
var $h = "[object RegExp]";
var Ph = "[object Set]";
var wh = "[object String]";
var kh = "[object WeakMap]";
var Ah = "[object ArrayBuffer]";
var Lh = "[object DataView]";
var Sh = "[object Float32Array]";
var Oh = "[object Float64Array]";
var Gh = "[object Int8Array]";
var Mh = "[object Int16Array]";
var Rh = "[object Int32Array]";
var Th = "[object Uint8Array]";
var Dh = "[object Uint8ClampedArray]";
var Bh = "[object Uint16Array]";
var Fh = "[object Uint32Array]";
var ke = {};
ke[Sh] = ke[Oh] = ke[Gh] = ke[Mh] = ke[Rh] = ke[Th] = ke[Dh] = ke[Bh] = ke[Fh] = true;
ke[vh] = ke[ph] = ke[Ah] = ke[gh] = ke[Lh] = ke[mh] = ke[yh] = ke[bh] = ke[xh] = ke[Ch] = ke[_h] = ke[$h] = ke[Ph] = ke[wh] = ke[kh] = false;
function Nh(e) {
  return Ot(e) && wi(e.length) && !!ke[tr(e)];
}
function jh(e) {
  return function(t) {
    return e(t);
  };
}
var Oi = typeof exports == "object" && exports && !exports.nodeType && exports;
var Ct = Oi && typeof module == "object" && module && !module.nodeType && module;
var Eh = Ct && Ct.exports === Oi;
var cn = Eh && Ci.process;
var Wh = function() {
  try {
    var e = Ct && Ct.require && Ct.require("util").types;
    return e || cn && cn.binding && cn.binding("util");
  } catch {
  }
}();
var Ya = Wh;
var Ka = Ya && Ya.isTypedArray;
var zh = Ka ? jh(Ka) : Nh;
var Gi = zh;
var qh = Object.prototype;
var Ih = qh.hasOwnProperty;
function Hh(e, t) {
  var r = kn(e), n = !r && An(e), a = !r && !n && Si(e), o = !r && !n && !a && Gi(e), l = r || n || a || o, s = l ? ih(e.length, String) : [], D = s.length;
  for (var W in e)
    (t || Ih.call(e, W)) && !(l && // Safari 9 has enumerable `arguments.length` in strict mode.
    (W == "length" || // Node.js 0.10 has enumerable non-index properties on buffers.
    a && (W == "offset" || W == "parent") || // PhantomJS 2 has enumerable non-index properties on typed arrays.
    o && (W == "buffer" || W == "byteLength" || W == "byteOffset") || // Skip index properties.
    Pi(W, D))) && s.push(W);
  return s;
}
function Vh(e, t) {
  return function(r) {
    return e(t(r));
  };
}
function Uh(e) {
  var t = [];
  if (e != null)
    for (var r in Object(e))
      t.push(r);
  return t;
}
var Xh = Object.prototype;
var Qh = Xh.hasOwnProperty;
function Yh(e) {
  if (!nt(e))
    return Uh(e);
  var t = ki(e), r = [];
  for (var n in e)
    n == "constructor" && (t || !Qh.call(e, n)) || r.push(n);
  return r;
}
function Mi(e) {
  return Bn(e) ? Hh(e, true) : Yh(e);
}
var Kh = Tn(Object, "create");
var Pt = Kh;
function Jh() {
  this.__data__ = Pt ? Pt(null) : {}, this.size = 0;
}
function Zh(e) {
  var t = this.has(e) && delete this.__data__[e];
  return this.size -= t ? 1 : 0, t;
}
var ev = "__lodash_hash_undefined__";
var tv = Object.prototype;
var rv = tv.hasOwnProperty;
function nv(e) {
  var t = this.__data__;
  if (Pt) {
    var r = t[e];
    return r === ev ? void 0 : r;
  }
  return rv.call(t, e) ? t[e] : void 0;
}
var av = Object.prototype;
var iv = av.hasOwnProperty;
function ov(e) {
  var t = this.__data__;
  return Pt ? t[e] !== void 0 : iv.call(t, e);
}
var lv = "__lodash_hash_undefined__";
function sv(e, t) {
  var r = this.__data__;
  return this.size += this.has(e) ? 0 : 1, r[e] = Pt && t === void 0 ? lv : t, this;
}
function rt(e) {
  var t = -1, r = e == null ? 0 : e.length;
  for (this.clear(); ++t < r; ) {
    var n = e[t];
    this.set(n[0], n[1]);
  }
}
rt.prototype.clear = Jh;
rt.prototype.delete = Zh;
rt.prototype.get = nv;
rt.prototype.has = ov;
rt.prototype.set = sv;
function uv() {
  this.__data__ = [], this.size = 0;
}
function nr(e, t) {
  for (var r = e.length; r--; )
    if (rr(e[r][0], t))
      return r;
  return -1;
}
var cv = Array.prototype;
var fv = cv.splice;
function dv(e) {
  var t = this.__data__, r = nr(t, e);
  if (r < 0)
    return false;
  var n = t.length - 1;
  return r == n ? t.pop() : fv.call(t, r, 1), --this.size, true;
}
function hv(e) {
  var t = this.__data__, r = nr(t, e);
  return r < 0 ? void 0 : t[r][1];
}
function vv(e) {
  return nr(this.__data__, e) > -1;
}
function pv(e, t) {
  var r = this.__data__, n = nr(r, e);
  return n < 0 ? (++this.size, r.push([e, t])) : r[n][1] = t, this;
}
function Ye(e) {
  var t = -1, r = e == null ? 0 : e.length;
  for (this.clear(); ++t < r; ) {
    var n = e[t];
    this.set(n[0], n[1]);
  }
}
Ye.prototype.clear = uv;
Ye.prototype.delete = dv;
Ye.prototype.get = hv;
Ye.prototype.has = vv;
Ye.prototype.set = pv;
var gv = Tn(ot, "Map");
var Ri = gv;
function mv() {
  this.size = 0, this.__data__ = {
    hash: new rt(),
    map: new (Ri || Ye)(),
    string: new rt()
  };
}
function yv(e) {
  var t = typeof e;
  return t == "string" || t == "number" || t == "symbol" || t == "boolean" ? e !== "__proto__" : e === null;
}
function ar(e, t) {
  var r = e.__data__;
  return yv(t) ? r[typeof t == "string" ? "string" : "hash"] : r.map;
}
function bv(e) {
  var t = ar(this, e).delete(e);
  return this.size -= t ? 1 : 0, t;
}
function xv(e) {
  return ar(this, e).get(e);
}
function Cv(e) {
  return ar(this, e).has(e);
}
function _v(e, t) {
  var r = ar(this, e), n = r.size;
  return r.set(e, t), this.size += r.size == n ? 0 : 1, this;
}
function lt(e) {
  var t = -1, r = e == null ? 0 : e.length;
  for (this.clear(); ++t < r; ) {
    var n = e[t];
    this.set(n[0], n[1]);
  }
}
lt.prototype.clear = mv;
lt.prototype.delete = bv;
lt.prototype.get = xv;
lt.prototype.has = Cv;
lt.prototype.set = _v;
var $v = Vh(Object.getPrototypeOf, Object);
var Ti = $v;
var Pv = "[object Object]";
var wv = Function.prototype;
var kv = Object.prototype;
var Di = wv.toString;
var Av = kv.hasOwnProperty;
var Lv = Di.call(Object);
function Sv(e) {
  if (!Ot(e) || tr(e) != Pv)
    return false;
  var t = Ti(e);
  if (t === null)
    return true;
  var r = Av.call(t, "constructor") && t.constructor;
  return typeof r == "function" && r instanceof r && Di.call(r) == Lv;
}
function Ov() {
  this.__data__ = new Ye(), this.size = 0;
}
function Gv(e) {
  var t = this.__data__, r = t.delete(e);
  return this.size = t.size, r;
}
function Mv(e) {
  return this.__data__.get(e);
}
function Rv(e) {
  return this.__data__.has(e);
}
var Tv = 200;
function Dv(e, t) {
  var r = this.__data__;
  if (r instanceof Ye) {
    var n = r.__data__;
    if (!Ri || n.length < Tv - 1)
      return n.push([e, t]), this.size = ++r.size, this;
    r = this.__data__ = new lt(n);
  }
  return r.set(e, t), this.size = r.size, this;
}
function st(e) {
  var t = this.__data__ = new Ye(e);
  this.size = t.size;
}
st.prototype.clear = Ov;
st.prototype.delete = Gv;
st.prototype.get = Mv;
st.prototype.has = Rv;
st.prototype.set = Dv;
var Bi = typeof exports == "object" && exports && !exports.nodeType && exports;
var Ja = Bi && typeof module == "object" && module && !module.nodeType && module;
var Bv = Ja && Ja.exports === Bi;
var Za = Bv ? ot.Buffer : void 0;
var ei = Za ? Za.allocUnsafe : void 0;
function Fv(e, t) {
  if (t)
    return e.slice();
  var r = e.length, n = ei ? ei(r) : new e.constructor(r);
  return e.copy(n), n;
}
var Nv = ot.Uint8Array;
var ti = Nv;
function jv(e) {
  var t = new e.constructor(e.byteLength);
  return new ti(t).set(new ti(e)), t;
}
function Ev(e, t) {
  var r = t ? jv(e.buffer) : e.buffer;
  return new e.constructor(r, e.byteOffset, e.length);
}
function Wv(e) {
  return typeof e.constructor == "function" && !ki(e) ? Td(Ti(e)) : {};
}
function zv(e) {
  return function(t, r, n) {
    for (var a = -1, o = Object(t), l = n(t), s = l.length; s--; ) {
      var D = l[e ? s : ++a];
      if (r(o[D], D, o) === false)
        break;
    }
    return t;
  };
}
var qv = zv();
var Iv = qv;
function Ln(e, t, r) {
  (r !== void 0 && !rr(e[t], r) || r === void 0 && !(t in e)) && Dn(e, t, r);
}
function Hv(e) {
  return Ot(e) && Bn(e);
}
function Sn(e, t) {
  if (!(t === "constructor" && typeof e[t] == "function") && t != "__proto__")
    return e[t];
}
function Vv(e) {
  return Jd(e, Mi(e));
}
function Uv(e, t, r, n, a, o, l) {
  var s = Sn(e, r), D = Sn(t, r), W = l.get(D);
  if (W) {
    Ln(e, r, W);
    return;
  }
  var M = o ? o(s, D, r + "", e, t, l) : void 0, U = M === void 0;
  if (U) {
    var F = kn(D), A = !F && Si(D), v = !F && !A && Gi(D);
    M = D, F || A || v ? kn(s) ? M = s : Hv(s) ? M = Bd(s) : A ? (U = false, M = Fv(D, true)) : v ? (U = false, M = Ev(D, true)) : M = [] : Sv(D) || An(D) ? (M = s, An(s) ? M = Vv(s) : (!nt(s) || Rn(s)) && (M = Wv(D))) : U = false;
  }
  U && (l.set(D, M), a(M, D, n, o, l), l.delete(D)), Ln(e, r, M);
}
function Fi(e, t, r, n, a) {
  e !== t && Iv(t, function(o, l) {
    if (a || (a = new st()), nt(o))
      Uv(e, t, l, r, Fi, n, a);
    else {
      var s = n ? n(Sn(e, l), o, l + "", e, t, a) : void 0;
      s === void 0 && (s = o), Ln(e, l, s);
    }
  }, Mi);
}
var Xv = nh(function(e, t, r) {
  Fi(e, t, r);
});
var ri = Xv;
function He(e, t) {
  let r = ri(e, t.value);
  const n = watchEffect(() => {
    r = ri(e, t.value);
  });
  return onUnmounted(() => {
    n();
  }), r;
}
var Qv = ["left-top", "right-top", "left-bottom", "right-bottom"];
var Yv = ["#4fd2dd", "#235fa7"];
var Kv = defineComponent({
  props: Ie,
  setup(e) {
    const t = ref(null), r = He(Yv, toRef(e, "color")), {
      width: n,
      height: a,
      initWH: o
    } = xe(t);
    return {
      width: n,
      height: a,
      initWH: o,
      mergedColor: r,
      borderBox1: t
    };
  },
  render() {
    const {
      backgroundColor: e,
      width: t,
      height: r,
      mergedColor: n,
      $slots: a
    } = this;
    return createVNode("div", {
      ref: "borderBox1",
      class: "dv-border-box-1"
    }, [createVNode("svg", {
      class: "dv-border",
      width: t,
      height: r
    }, [createVNode("polygon", {
      fill: e,
      points: `10, 27 10, ${r - 27} 13, ${r - 24} 13, ${r - 21} 24, ${r - 11}
      38, ${r - 11} 41, ${r - 8} 73, ${r - 8} 75, ${r - 10} 81, ${r - 10}
      85, ${r - 6} ${t - 85}, ${r - 6} ${t - 81}, ${r - 10} ${t - 75}, ${r - 10}
      ${t - 73}, ${r - 8} ${t - 41}, ${r - 8} ${t - 38}, ${r - 11}
      ${t - 10}, ${r - 27} ${t - 10}, 27 ${t - 13}, 25 ${t - 13}, 21
      ${t - 24}, 11 ${t - 38}, 11 ${t - 41}, 8 ${t - 73}, 8 ${t - 75}, 10
      ${t - 81}, 10 ${t - 85}, 6 85, 6 81, 10 75, 10 73, 8 41, 8 38, 11 24, 11 13, 21 13, 24`
    }, null)]), Qv.map((o) => createVNode("svg", {
      key: o,
      width: "150px",
      height: "150px",
      class: `${o} dv-border`
    }, [createVNode("polygon", {
      fill: n[0],
      points: "6,66 6,18 12,12 18,12 24,6 27,6 30,9 36,9 39,6 84,6 81,9 75,9 73.2,7 40.8,7 37.8,10.2 24,10.2 12,21 12,24 9,27 9,51 7.8,54 7.8,63"
    }, [createVNode("animate", {
      attributeName: "fill",
      values: `${n[0]};${n[1]};${n[0]}`,
      dur: "0.5s",
      begin: "0s",
      repeatCount: "indefinite"
    }, null)]), createVNode("polygon", {
      fill: n[1],
      points: "27.599999999999998,4.8 38.4,4.8 35.4,7.8 30.599999999999998,7.8"
    }, [createVNode("animate", {
      attributeName: "fill",
      values: `${n[1]};${n[0]};${n[1]}`,
      dur: "0.5s",
      begin: "0s",
      repeatCount: "indefinite"
    }, null)]), createVNode("polygon", {
      fill: n[0],
      points: "9,54 9,63 7.199999999999999,66 7.199999999999999,75 7.8,78 7.8,110 8.4,110 8.4,66 9.6,66 9.6,54"
    }, [createVNode("animate", {
      attributeName: "fill",
      values: `${n[0]};${n[1]};transparent`,
      dur: "1s",
      begin: "0s",
      repeatCount: "indefinite"
    }, null)])])), createVNode("div", {
      class: "border-box-content"
    }, [renderSlot(a, "default")])]);
  }
});
var fn = {
  install(e) {
    e.component("DvBorderBox1", Kv);
  }
};
var Jv = ["#fff", "rgba(255, 255, 255, 0.6)"];
var Zv = defineComponent({
  props: Ie,
  setup(e) {
    const t = ref(null), r = He(Jv, toRef(e, "color")), {
      width: n,
      height: a,
      initWH: o
    } = xe(t);
    return {
      width: n,
      height: a,
      initWH: o,
      mergedColor: r,
      borderBox2: t
    };
  },
  render() {
    const {
      $slots: e,
      backgroundColor: t,
      width: r,
      height: n,
      mergedColor: a
    } = this;
    return createVNode("div", {
      ref: "borderBox2",
      class: "dv-border-box-2"
    }, [createVNode("svg", {
      class: "dv-border-svg-container",
      width: r,
      height: n
    }, [createVNode("polygon", {
      fill: t,
      points: `
        7, 7 ${r - 7}, 7 ${r - 7}, ${n - 7} 7, ${n - 7}
      `
    }, null), createVNode("polyline", {
      stroke: a[0],
      points: `2, 2 ${r - 2} ,2 ${r - 2}, ${n - 2} 2, ${n - 2} 2, 2`
    }, null), createVNode("polyline", {
      stroke: a[1],
      points: `6, 6 ${r - 6}, 6 ${r - 6}, ${n - 6} 6, ${n - 6} 6, 6`
    }, null), createVNode("circle", {
      fill: a[0],
      cx: "11",
      cy: "11",
      r: "1"
    }, null), createVNode("circle", {
      fill: a[0],
      cx: r - 11,
      cy: "11",
      r: "1"
    }, null), createVNode("circle", {
      fill: a[0],
      cx: r - 11,
      cy: n - 11,
      r: "1"
    }, null), createVNode("circle", {
      fill: a[0],
      cx: "11",
      cy: n - 11,
      r: "1"
    }, null)]), createVNode("div", {
      class: "border-box-content"
    }, [renderSlot(e, "default")])]);
  }
});
var dn = {
  install(e) {
    e.component("DvBorderBox2", Zv);
  }
};
var ep = ["#2862b7", "#2862b7"];
var tp = defineComponent({
  props: Ie,
  setup(e) {
    const t = ref(null), {
      width: r,
      height: n,
      initWH: a
    } = xe(t), o = He(ep, toRef(e, "color"));
    return {
      width: r,
      height: n,
      mergedColor: o,
      initWH: a,
      borderBox3: t
    };
  },
  render() {
    const {
      $slots: e,
      width: t,
      height: r,
      backgroundColor: n,
      mergedColor: a
    } = this;
    return createVNode("div", {
      ref: "borderBox3",
      class: "dv-border-box-3"
    }, [createVNode("svg", {
      class: "dv-border-svg-container",
      width: t,
      height: r
    }, [createVNode("polygon", {
      fill: n,
      points: `
              23, 23 ${t - 24}, 23 ${t - 24}, ${r - 24} 23, ${r - 24}
            `
    }, null), createVNode("polyline", {
      class: "dv-bb3-line1",
      stroke: a[0],
      points: `4, 4 ${t - 22} ,4 ${t - 22}, ${r - 22} 4, ${r - 22} 4, 4`
    }, null), createVNode("polyline", {
      class: "dv-bb3-line2",
      stroke: a[1],
      points: `10, 10 ${t - 16}, 10 ${t - 16}, ${r - 16} 10, ${r - 16} 10, 10`
    }, null), createVNode("polyline", {
      class: "dv-bb3-line2",
      stroke: a[1],
      points: `16, 16 ${t - 10}, 16 ${t - 10}, ${r - 10} 16, ${r - 10} 16, 16`
    }, null), createVNode("polyline", {
      class: "dv-bb3-line2",
      stroke: a[1],
      points: `22, 22 ${t - 4}, 22 ${t - 4}, ${r - 4} 22, ${r - 4} 22, 22`
    }, null)]), createVNode("div", {
      class: "border-box-content"
    }, [renderSlot(e, "default")])]);
  }
});
var hn = {
  install(e) {
    e.component("DvBorderBox3", tp);
  }
};
var rp = {
  ...Ie,
  reverse: {
    type: Boolean,
    default: false
  }
};
var np = ["red", "rgba(0,0,255,0.8)"];
var ap = defineComponent({
  props: rp,
  setup(e) {
    const t = ref(null), {
      width: r,
      height: n,
      initWH: a
    } = xe(t), o = He(np, toRef(e, "color"));
    return {
      width: r,
      height: n,
      initWH: a,
      mergedColor: o,
      borderBox4: t
    };
  },
  render() {
    const {
      $slots: e,
      backgroundColor: t,
      reverse: r,
      width: n,
      height: a,
      mergedColor: o
    } = this;
    return createVNode("div", {
      ref: "borderBox4",
      class: "dv-border-box-4"
    }, [createVNode("svg", {
      class: `dv-border-svg-container ${r && "dv-reverse"}`,
      width: n,
      height: a
    }, [createVNode("polygon", {
      fill: t,
      points: `
        ${n - 15}, 22 170, 22 150, 7 40, 7 28, 21 32, 24
        16, 42 16, ${a - 32} 41, ${a - 7} ${n - 15}, ${a - 7}
      `
    }, null), createVNode("polyline", {
      class: "dv-bb4-line-1",
      stroke: o[0],
      points: `145, ${a - 5} 40, ${a - 5} 10, ${a - 35}
          10, 40 40, 5 150, 5 170, 20 ${n - 15}, 20`
    }, null), createVNode("polyline", {
      stroke: o[1],
      class: "dv-bb4-line-2",
      points: `245, ${a - 1} 36, ${a - 1} 14, ${a - 23}
          14, ${a - 100}`
    }, null), createVNode("polyline", {
      class: "dv-bb4-line-3",
      stroke: o[0],
      points: `7, ${a - 40} 7, ${a - 75}`
    }, null), createVNode("polyline", {
      class: "dv-bb4-line-4",
      stroke: o[0],
      points: "28, 24 13, 41 13, 64"
    }, null), createVNode("polyline", {
      class: "dv-bb4-line-5",
      stroke: o[0],
      points: "5, 45 5, 140"
    }, null), createVNode("polyline", {
      class: "dv-bb4-line-6",
      stroke: o[1],
      points: "14, 75 14, 180"
    }, null), createVNode("polyline", {
      class: "dv-bb4-line-7",
      stroke: o[1],
      points: "55, 11 147, 11 167, 26 250, 26"
    }, null), createVNode("polyline", {
      class: "dv-bb4-line-8",
      stroke: o[1],
      points: "158, 5 173, 16"
    }, null), createVNode("polyline", {
      class: "dv-bb4-line-9",
      stroke: o[0],
      points: `200, 17 ${n - 10}, 17`
    }, null), createVNode("polyline", {
      class: "dv-bb4-line-10",
      stroke: o[1],
      points: `385, 17 ${n - 10}, 17`
    }, null)]), createVNode("div", {
      class: "border-box-content"
    }, [renderSlot(e, "default")])]);
  }
});
var vn = {
  install(e) {
    e.component("DvBorderBox4", ap);
  }
};
var ip = {
  ...Ie,
  reverse: {
    type: Boolean,
    default: false
  }
};
var op = ["rgba(255, 255, 255, 0.35)", "rgba(255, 255, 255, 0.20)"];
var lp = defineComponent({
  props: ip,
  setup(e) {
    const t = ref(null), {
      width: r,
      height: n,
      initWH: a
    } = xe(t), o = He(op, toRef(e, "color"));
    return {
      width: r,
      height: n,
      initWH: a,
      mergedColor: o,
      borderBox5: t
    };
  },
  render() {
    const {
      $slots: e,
      width: t,
      height: r,
      mergedColor: n,
      backgroundColor: a,
      reverse: o
    } = this;
    return createVNode("div", {
      ref: "borderBox5",
      class: "dv-border-box-5"
    }, [createVNode("svg", {
      class: `dv-border-svg-container  ${o && "dv-reverse"}`,
      width: t,
      height: r
    }, [createVNode("polygon", {
      fill: a,
      points: `
            10, 22 ${t - 22}, 22 ${t - 22}, ${r - 86} ${t - 84}, ${r - 24} 10, ${r - 24}
          `
    }, null), createVNode("polyline", {
      class: "dv-bb5-line-1",
      stroke: n[0],
      points: `8, 5 ${t - 5}, 5 ${t - 5}, ${r - 100}
          ${t - 100}, ${r - 5} 8, ${r - 5} 8, 5`
    }, null), createVNode("polyline", {
      class: "dv-bb5-line-2",
      stroke: n[1],
      points: `3, 5 ${t - 20}, 5 ${t - 20}, ${r - 60}
          ${t - 74}, ${r - 5} 3, ${r - 5} 3, 5`
    }, null), createVNode("polyline", {
      class: "dv-bb5-line-3",
      stroke: n[1],
      points: `50, 13 ${t - 35}, 13`
    }, null), createVNode("polyline", {
      class: "dv-bb5-line-4",
      stroke: n[1],
      points: `15, 20 ${t - 35}, 20`
    }, null), createVNode("polyline", {
      class: "dv-bb5-line-5",
      stroke: n[1],
      points: `15, ${r - 20} ${t - 110}, ${r - 20}`
    }, null), createVNode("polyline", {
      class: "dv-bb5-line-6",
      stroke: n[1],
      points: `15, ${r - 13} ${t - 110}, ${r - 13}`
    }, null)]), createVNode("div", {
      class: "border-box-content"
    }, [renderSlot(e, "default")])]);
  }
});
var pn = {
  install(e) {
    e.component("DvBorderBox5", lp);
  }
};
var sp = ["rgba(255, 255, 255, 0.35)", "gray"];
var up = defineComponent({
  props: Ie,
  setup(e) {
    const t = ref(null), {
      width: r,
      height: n,
      initWH: a
    } = xe(t), o = He(sp, toRef(e, "color"));
    return {
      width: r,
      height: n,
      initWH: a,
      mergedColor: o,
      borderBox6: t
    };
  },
  render() {
    const {
      $slots: e,
      width: t,
      height: r,
      mergedColor: n,
      backgroundColor: a
    } = this;
    return createVNode("div", {
      ref: "borderBox6",
      class: "dv-border-box-6"
    }, [createVNode("svg", {
      class: "dv-border-svg-container",
      width: t,
      height: r
    }, [createVNode("polygon", {
      fill: a,
      points: `
            9, 7 ${t - 9}, 7 ${t - 9}, ${r - 7} 9, ${r - 7}
            `
    }, null), createVNode("circle", {
      fill: n[1],
      cx: "5",
      cy: "5",
      r: "2"
    }, null), createVNode("circle", {
      fill: n[1],
      cx: t - 5,
      cy: "5",
      r: "2"
    }, null), createVNode("circle", {
      fill: n[1],
      cx: t - 5,
      cy: r - 5,
      r: "2"
    }, null), createVNode("circle", {
      fill: n[1],
      cx: "5",
      cy: r - 5,
      r: "2"
    }, null), createVNode("polyline", {
      stroke: n[0],
      points: `10, 4 ${t - 10}, 4`
    }, null), createVNode("polyline", {
      stroke: n[0],
      points: `10, ${r - 4} ${t - 10}, ${r - 4}`
    }, null), createVNode("polyline", {
      stroke: n[0],
      points: `5, 70 5, ${r - 70}`
    }, null), createVNode("polyline", {
      stroke: n[0],
      points: `${t - 5}, 70 ${t - 5}, ${r - 70}`
    }, null), createVNode("polyline", {
      stroke: n[0],
      points: "3, 10, 3, 50"
    }, null), createVNode("polyline", {
      stroke: n[0],
      points: "7, 30 7, 80"
    }, null), createVNode("polyline", {
      stroke: n[0],
      points: `${t - 3}, 10 ${t - 3}, 50`
    }, null), createVNode("polyline", {
      stroke: n[0],
      points: `${t - 7}, 30 ${t - 7}, 80`
    }, null), createVNode("polyline", {
      stroke: n[0],
      points: `3, ${r - 10} 3, ${r - 50}`
    }, null), createVNode("polyline", {
      stroke: n[0],
      points: `7, ${r - 30} 7, ${r - 80}`
    }, null), createVNode("polyline", {
      stroke: n[0],
      points: `${t - 3}, ${r - 10} ${t - 3}, ${r - 50}`
    }, null), createVNode("polyline", {
      stroke: n[0],
      points: `${t - 7}, ${r - 30} ${t - 7}, ${r - 80}`
    }, null)]), createVNode("div", {
      class: "border-box-content"
    }, [renderSlot(e, "default")])]);
  }
});
var gn = {
  install(e) {
    e.component("DvBorderBox6", up);
  }
};
var cp = ["rgba(128,128,128,0.3)", "rgba(128,128,128,0.5)"];
var fp = defineComponent({
  props: Ie,
  setup(e) {
    const t = ref(null), {
      width: r,
      height: n,
      initWH: a
    } = xe(t), o = He(cp, toRef(e, "color"));
    return {
      width: r,
      height: n,
      initWH: a,
      mergedColor: o,
      borderBox7: t
    };
  },
  render() {
    const {
      $slots: e,
      width: t,
      height: r,
      mergedColor: n,
      backgroundColor: a
    } = this;
    return createVNode("div", {
      ref: "borderBox7",
      class: "dv-border-box-7",
      style: `box-shadow: inset 0 0 40px ${n[0]}; border: 1px solid ${n[0]}; background-color: ${a}`
    }, [createVNode("svg", {
      class: "dv-border-svg-container",
      width: t,
      height: r
    }, [createVNode("polyline", {
      class: "dv-bb7-line-width-2",
      stroke: n[0],
      points: "0, 25 0, 0 25, 0"
    }, null), createVNode("polyline", {
      class: "dv-bb7-line-width-2",
      stroke: n[0],
      points: `${t - 25}, 0 ${t}, 0 ${t}, 25`
    }, null), createVNode("polyline", {
      class: "dv-bb7-line-width-2",
      stroke: n[0],
      points: `${t - 25}, ${r} ${t}, ${r} ${t}, ${r - 25}`
    }, null), createVNode("polyline", {
      class: "dv-bb7-line-width-2",
      stroke: n[0],
      points: `0, ${r - 25} 0, ${r} 25, ${r}`
    }, null), createVNode("polyline", {
      class: "dv-bb7-line-width-5",
      stroke: n[1],
      points: "0, 10 0, 0 10, 0"
    }, null), createVNode("polyline", {
      class: "dv-bb7-line-width-5",
      stroke: n[1],
      points: `${t - 10}, 0 ${t}, 0 ${t}, 10`
    }, null), createVNode("polyline", {
      class: "dv-bb7-line-width-5",
      stroke: n[1],
      points: `${t - 10}, ${r} ${t}, ${r} ${t}, ${r - 10}`
    }, null), createVNode("polyline", {
      class: "dv-bb7-line-width-5",
      stroke: n[1],
      points: `0, ${r - 10} 0, ${r} 10, ${r}`
    }, null)]), createVNode("div", {
      class: "border-box-content"
    }, [renderSlot(e, "default")])]);
  }
});
var mn = {
  install(e) {
    e.component("DvBorderBox7", fp);
  }
};
var dp = {
  ...Ie,
  reverse: {
    type: Boolean,
    default: false
  },
  dur: {
    type: Number,
    default: 3
  }
};
var hp = ["#235fa7", "#4fd2dd"];
var vp = defineComponent({
  props: dp,
  setup(e) {
    const t = Ve(), r = ref(null), n = reactive({
      path: `border-box-8-path-${t}`,
      gradient: `border-box-8-gradient-${t}`,
      mask: `border-box-8-mask-${t}`
    }), {
      width: a,
      height: o,
      initWH: l
    } = xe(r), s = computed(() => (a.value + o.value - 5) * 2), D = computed(() => e.reverse ? `M 2.5, 2.5 L 2.5, ${o.value - 2.5} L ${a.value - 2.5}, ${o.value - 2.5} L ${a.value - 2.5}, 2.5 L 2.5, 2.5` : `M2.5, 2.5 L${a.value - 2.5}, 2.5 L${a.value - 2.5}, ${o.value - 2.5} L2.5, ${o.value - 2.5} L2.5, 2.5`), W = He(hp, toRef(e, "color"));
    return {
      width: a,
      height: o,
      initWH: l,
      state: n,
      mergedColor: W,
      pathD: D,
      length: s,
      borderBox8: r
    };
  },
  render() {
    const {
      $slots: e,
      width: t,
      height: r,
      state: n,
      mergedColor: a,
      pathD: o,
      length: l,
      backgroundColor: s,
      dur: D
    } = this;
    return createVNode("div", {
      ref: "borderBox8",
      class: "dv-border-box-8"
    }, [createVNode("svg", {
      class: "dv-border-svg-container",
      width: t,
      height: r
    }, [createVNode("defs", null, [createVNode("path", {
      id: n.path,
      d: o,
      fill: "transparent"
    }, null), createVNode("radialGradient", {
      id: n.gradient,
      cx: "50%",
      cy: "50%",
      r: "50%"
    }, [createVNode("stop", {
      offset: "0%",
      "stop-color": "#fff",
      "stop-opacity": "1"
    }, null), createVNode("stop", {
      offset: "100%",
      "stop-color": "#fff",
      "stop-opacity": "0"
    }, null)]), createVNode("mask", {
      id: n.mask
    }, [createVNode("circle", {
      cx: "0",
      cy: "0",
      r: "150",
      fill: `url(#${n.gradient})`
    }, [h("animateMotion", {
      dur: `${D}s`,
      path: o,
      rotate: "auto",
      repeatCount: "indefinite"
    })])])]), createVNode("polygon", {
      fill: s,
      points: `5, 5 ${t - 5}, 5 ${t - 5} ${r - 5} 5, ${r - 5}`
    }, null), createVNode("use", {
      stroke: a[0],
      "stroke-width": "1",
      "xlink:href": `#${n.path}`
    }, null), createVNode("use", {
      stroke: a[1],
      "stroke-width": "3",
      "xlink:href": `#${n.path}`,
      mask: `url(#${n.mask})`
    }, [createVNode("animate", {
      attributeName: "stroke-dasharray",
      from: `0, ${l}`,
      to: `${l}, 0`,
      dur: `${D}s`,
      repeatCount: "indefinite"
    }, null)])]), createVNode("div", {
      class: "border-box-content"
    }, [renderSlot(e, "default")])]);
  }
});
var yn = {
  install(e) {
    e.component("DvBorderBox8", vp);
  }
};
var pp = ["#11eefd", "#0078d2"];
var gp = defineComponent({
  props: Ie,
  setup(e) {
    const t = Ve(), r = ref(null), {
      width: n,
      height: a,
      initWH: o
    } = xe(r), l = reactive({
      gradientId: `border-box-9-gradient-${t}`,
      maskId: `border-box-9-mask-${t}`
    }), s = He(pp, toRef(e, "color"));
    return {
      width: n,
      height: a,
      initWH: o,
      state: l,
      mergedColor: s,
      borderBox9: r
    };
  },
  render() {
    const {
      $slots: e,
      width: t,
      height: r,
      state: n,
      mergedColor: a,
      backgroundColor: o
    } = this;
    return createVNode("div", {
      ref: "borderBox9",
      class: "dv-border-box-9"
    }, [createVNode("svg", {
      class: "dv-border-svg-container",
      width: t,
      height: r
    }, [createVNode("defs", null, [createVNode("linearGradient", {
      id: n.gradientId,
      x1: "0%",
      y1: "0%",
      x2: "100%",
      y2: "100%"
    }, [createVNode("animate", {
      attributeName: "x1",
      values: "0%;100%;0%",
      dur: "10s",
      begin: "0s",
      repeatCount: "indefinite"
    }, null), createVNode("animate", {
      attributeName: "x2",
      values: "100%;0%;100%",
      dur: "10s",
      begin: "0s",
      repeatCount: "indefinite"
    }, null), createVNode("stop", {
      offset: "0%",
      "stop-color": a[0]
    }, [createVNode("animate", {
      attributeName: "stop-color",
      values: `${a[0]};${a[1]};${a[0]}`,
      dur: "10s",
      begin: "0s",
      repeatCount: "indefinite"
    }, null)]), createVNode("stop", {
      offset: "100%",
      "stop-color": a[1]
    }, [createVNode("animate", {
      attributeName: "stop-color",
      values: `${a[1]};${a[0]};${a[1]}`,
      dur: "10s",
      begin: "0s",
      repeatCount: "indefinite"
    }, null)])]), createVNode("mask", {
      id: n.maskId
    }, [createVNode("polyline", {
      stroke: "#fff",
      "stroke-width": "3",
      fill: "transparent",
      points: `8, ${r * 0.4} 8, 3, ${t * 0.4 + 7}, 3`
    }, null), createVNode("polyline", {
      fill: "#fff",
      points: `8, ${r * 0.15} 8, 3, ${t * 0.1 + 7}, 3
              ${t * 0.1}, 8 14, 8 14, ${r * 0.15 - 7}
            `
    }, null), createVNode("polyline", {
      stroke: "#fff",
      "stroke-width": "3",
      fill: "transparent",
      points: `${t * 0.5}, 3 ${t - 3}, 3, ${t - 3}, ${r * 0.25}`
    }, null), createVNode("polyline", {
      fill: "#fff",
      points: `
              ${t * 0.52}, 3 ${t * 0.58}, 3
              ${t * 0.58 - 7}, 9 ${t * 0.52 + 7}, 9
            `
    }, null), createVNode("polyline", {
      fill: "#fff",
      points: `
              ${t * 0.9}, 3 ${t - 3}, 3 ${t - 3}, ${r * 0.1}
              ${t - 9}, ${r * 0.1 - 7} ${t - 9}, 9 ${t * 0.9 + 7}, 9
            `
    }, null), createVNode("polyline", {
      stroke: "#fff",
      "stroke-width": "3",
      fill: "transparent",
      points: `8, ${r * 0.5} 8, ${r - 3} ${t * 0.3 + 7}, ${r - 3}`
    }, null), createVNode("polyline", {
      fill: "#fff",
      points: `
              8, ${r * 0.55} 8, ${r * 0.7}
              2, ${r * 0.7 - 7} 2, ${r * 0.55 + 7}
            `
    }, null), createVNode("polyline", {
      stroke: "#fff",
      "stroke-width": "3",
      fill: "transparent",
      points: `${t * 0.35}, ${r - 3} ${t - 3}, ${r - 3} ${t - 3}, ${r * 0.35}`
    }, null), createVNode("polyline", {
      fill: "#fff",
      points: `
              ${t * 0.92}, ${r - 3} ${t - 3}, ${r - 3} ${t - 3}, ${r * 0.8}
              ${t - 9}, ${r * 0.8 + 7} ${t - 9}, ${r - 9} ${t * 0.92 + 7}, ${r - 9}
            `
    }, null)])]), createVNode("polygon", {
      fill: o,
      points: `
              15, 9 ${t * 0.1 + 1}, 9 ${t * 0.1 + 4}, 6 ${t * 0.52 + 2}, 6
              ${t * 0.52 + 6}, 10 ${t * 0.58 - 7}, 10 ${t * 0.58 - 2}, 6
              ${t * 0.9 + 2}, 6 ${t * 0.9 + 6}, 10 ${t - 10}, 10 ${t - 10}, ${r * 0.1 - 6}
              ${t - 6}, ${r * 0.1 - 1} ${t - 6}, ${r * 0.8 + 1} ${t - 10}, ${r * 0.8 + 6}
              ${t - 10}, ${r - 10} ${t * 0.92 + 7}, ${r - 10}  ${t * 0.92 + 2}, ${r - 6}
              11, ${r - 6} 11, ${r * 0.15 - 2} 15, ${r * 0.15 - 7}
            `
    }, null), createVNode("rect", {
      x: "0",
      y: "0",
      width: t,
      height: r,
      fill: `url(#${n.gradientId})`,
      mask: `url(#${n.maskId})`
    }, null)]), createVNode("div", {
      class: "border-box-content"
    }, [renderSlot(e, "default")])]);
  }
});
var bn = {
  install(e) {
    e.component("DvBorderBox9", gp);
  }
};
var mp = ["left-top", "right-top", "left-bottom", "right-bottom"];
var yp = ["#1d48c4", "#d3e1f8"];
var bp = defineComponent({
  props: Ie,
  setup(e) {
    const t = ref(null), {
      width: r,
      height: n,
      initWH: a
    } = xe(t), o = He(yp, toRef(e, "color"));
    return {
      width: r,
      height: n,
      initWH: a,
      mergedColor: o,
      borderBox10: t
    };
  },
  render() {
    const {
      $slots: e,
      width: t,
      height: r,
      mergedColor: n,
      backgroundColor: a
    } = this;
    return createVNode("div", {
      ref: "borderBox10",
      class: "dv-border-box-10",
      style: `box-shadow: inset 0 0 25px 3px ${n[0]}`
    }, [createVNode("svg", {
      class: "dv-border-svg-container",
      width: t,
      height: r
    }, [createVNode("polygon", {
      fill: a,
      points: `
              4, 0 ${t - 4}, 0 ${t}, 4 ${t}, ${r - 4} ${t - 4}, ${r}
              4, ${r} 0, ${r - 4} 0, 4
            `
    }, null)]), mp.map((o) => createVNode("svg", {
      width: "150px",
      height: "150px",
      class: `${o} dv-border-svg-container`
    }, [createVNode("polygon", {
      fill: n[1],
      points: "40, 0 5, 0 0, 5 0, 16 3, 19 3, 7 7, 3 35, 3"
    }, null)])), createVNode("div", {
      class: "border-box-content"
    }, [renderSlot(e, "default")])]);
  }
});
var xn = {
  install(e) {
    e.component("DvBorderBox10", bp);
  }
};
var xp = {
  ...Ie,
  title: {
    type: String,
    default: ""
  },
  titleWidth: {
    type: Number,
    default: 250
  },
  animate: {
    type: Boolean,
    default: true
  }
};
var ni = ["#8aaafb", "#1f33a2"];
var Cp = defineComponent({
  props: xp,
  setup(e) {
    const t = Ve(), r = ref(null), {
      width: n,
      height: a,
      initWH: o
    } = xe(r), l = ref(`border-box-11-filterId-${t}`), s = He(ni, toRef(e, "color"));
    return {
      width: n,
      height: a,
      initWH: o,
      filterId: l,
      mergedColor: s,
      borderBox11: r
    };
  },
  render() {
    const {
      $slots: e,
      width: t,
      height: r,
      filterId: n,
      mergedColor: a,
      backgroundColor: o,
      title: l,
      titleWidth: s,
      animate: D
    } = this;
    return createVNode("div", {
      ref: "borderBox11",
      class: "dv-border-box-11"
    }, [createVNode("svg", {
      class: "dv-border-svg-container",
      width: t,
      height: r
    }, [createVNode("defs", null, [createVNode("filter", {
      id: n,
      height: "150%",
      width: "150%",
      x: "-25%",
      y: "-25%"
    }, [createVNode("feMorphology", {
      operator: "dilate",
      radius: "2",
      in: "SourceAlpha",
      result: "thicken"
    }, null), createVNode("feGaussianBlur", {
      in: "thicken",
      stdDeviation: "3",
      result: "blurred"
    }, null), createVNode("feFlood", {
      "flood-color": a[1],
      result: "glowColor"
    }, null), createVNode("feComposite", {
      in: "glowColor",
      in2: "blurred",
      operator: "in",
      result: "softGlowColored"
    }, null), createVNode("feMerge", null, [createVNode("feMergeNode", {
      in: "softGlowColored"
    }, null), createVNode("feMergeNode", {
      in: "SourceGraphic"
    }, null)])])]), createVNode("polygon", {
      fill: o,
      points: `
        20, 32 ${t * 0.5 - s / 2}, 32 ${t * 0.5 - s / 2 + 20}, 53
        ${t * 0.5 + s / 2 - 20}, 53 ${t * 0.5 + s / 2}, 32
        ${t - 20}, 32 ${t - 8}, 48 ${t - 8}, ${r - 25} ${t - 20}, ${r - 8}
        20, ${r - 8} 8, ${r - 25} 8, 50
      `
    }, null), createVNode("polyline", {
      stroke: a[0],
      filter: `url(#${n})`,
      points: `
          ${(t - s) / 2}, 30
          20, 30 7, 50 7, ${50 + (r - 167) / 2}
          13, ${55 + (r - 167) / 2} 13, ${135 + (r - 167) / 2}
          7, ${140 + (r - 167) / 2} 7, ${r - 27}
          20, ${r - 7} ${t - 20}, ${r - 7} ${t - 7}, ${r - 27}
          ${t - 7}, ${140 + (r - 167) / 2} ${t - 13}, ${135 + (r - 167) / 2}
          ${t - 13}, ${55 + (r - 167) / 2} ${t - 7}, ${50 + (r - 167) / 2}
          ${t - 7}, 50 ${t - 20}, 30 ${(t + s) / 2}, 30
          ${(t + s) / 2 - 20}, 7 ${(t - s) / 2 + 20}, 7
          ${(t - s) / 2}, 30 ${(t - s) / 2 + 20}, 52
          ${(t + s) / 2 - 20}, 52 ${(t + s) / 2}, 30
        `
    }, null), createVNode("polygon", {
      stroke: a[0],
      fill: "transparent",
      points: `
          ${(t + s) / 2 - 5}, 30 ${(t + s) / 2 - 21}, 11
          ${(t + s) / 2 - 27}, 11 ${(t + s) / 2 - 8}, 34
        `
    }, null), createVNode("polygon", {
      stroke: a[0],
      fill: "transparent",
      points: `
          ${(t - s) / 2 + 5}, 30 ${(t - s) / 2 + 22}, 49
          ${(t - s) / 2 + 28}, 49 ${(t - s) / 2 + 8}, 26
        `
    }, null), createVNode("polygon", {
      stroke: a[0],
      fill: De(a[1] || ni[1], 30),
      filter: `url(#${n})`,
      points: `
          ${(t + s) / 2 - 11}, 37 ${(t + s) / 2 - 32}, 11
          ${(t - s) / 2 + 23}, 11 ${(t - s) / 2 + 11}, 23
          ${(t - s) / 2 + 33}, 49 ${(t + s) / 2 - 22}, 49
        `
    }, null), createVNode("polygon", {
      filter: `url(#${n})`,
      fill: a[0],
      opacity: "1",
      points: `
          ${(t - s) / 2 - 10}, 37 ${(t - s) / 2 - 31}, 37
          ${(t - s) / 2 - 25}, 46 ${(t - s) / 2 - 4}, 46
        `
    }, [D && createVNode("animate", {
      attributeName: "opacity",
      values: "1;0.7;1",
      dur: "2s",
      begin: "0s",
      repeatCount: "indefinite"
    }, null)]), createVNode("polygon", {
      filter: `url(#${n})`,
      fill: a[0],
      opacity: "0.7",
      points: `
          ${(t - s) / 2 - 40}, 37 ${(t - s) / 2 - 61}, 37
          ${(t - s) / 2 - 55}, 46 ${(t - s) / 2 - 34}, 46
        `
    }, [D && createVNode("animate", {
      attributeName: "opacity",
      values: "0.7;0.4;0.7",
      dur: "2s",
      begin: "0s",
      repeatCount: "indefinite"
    }, null)]), createVNode("polygon", {
      filter: `url(#${n})`,
      fill: a[0],
      opacity: "0.5",
      points: `
          ${(t - s) / 2 - 70}, 37 ${(t - s) / 2 - 91}, 37
          ${(t - s) / 2 - 85}, 46 ${(t - s) / 2 - 64}, 46
        `
    }, [D && createVNode("animate", {
      attributeName: "opacity",
      values: "0.5;0.2;0.5",
      dur: "2s",
      begin: "0s",
      repeatCount: "indefinite"
    }, null)]), createVNode("polygon", {
      filter: `url(#${n})`,
      fill: a[0],
      opacity: "1",
      points: `
          ${(t + s) / 2 + 30}, 37 ${(t + s) / 2 + 9}, 37
          ${(t + s) / 2 + 3}, 46 ${(t + s) / 2 + 24}, 46
        `
    }, [D && createVNode("animate", {
      attributeName: "opacity",
      values: "1;0.7;1",
      dur: "2s",
      begin: "0s",
      repeatCount: "indefinite"
    }, null)]), createVNode("polygon", {
      filter: `url(#${n})`,
      fill: a[0],
      opacity: "0.7",
      points: `
          ${(t + s) / 2 + 60}, 37 ${(t + s) / 2 + 39}, 37
          ${(t + s) / 2 + 33}, 46 ${(t + s) / 2 + 54}, 46
        `
    }, [D && createVNode("animate", {
      attributeName: "opacity",
      values: "0.7;0.4;0.7",
      dur: "2s",
      begin: "0s",
      repeatCount: "indefinite"
    }, null)]), createVNode("polygon", {
      filter: `url(#${n})`,
      fill: a[0],
      opacity: "0.5",
      points: `
          ${(t + s) / 2 + 90}, 37 ${(t + s) / 2 + 69}, 37
          ${(t + s) / 2 + 63}, 46 ${(t + s) / 2 + 84}, 46
        `
    }, [D && createVNode("animate", {
      attributeName: "opacity",
      values: "0.5;0.2;0.5",
      dur: "2s",
      begin: "0s",
      repeatCount: "indefinite"
    }, null)]), createVNode("text", {
      class: "dv-border-box-11-title",
      x: `${t / 2}`,
      y: "32",
      fill: "#fff",
      "font-size": "18",
      "text-anchor": "middle",
      "dominant-baseline": "middle"
    }, [l]), createVNode("polygon", {
      fill: a[0],
      filter: `url(#${n})`,
      points: `
          7, ${53 + (r - 167) / 2} 11, ${57 + (r - 167) / 2}
          11, ${133 + (r - 167) / 2} 7, ${137 + (r - 167) / 2}
        `
    }, null), createVNode("polygon", {
      fill: a[0],
      filter: `url(#${n})`,
      points: `
          ${t - 7}, ${53 + (r - 167) / 2} ${t - 11}, ${57 + (r - 167) / 2}
          ${t - 11}, ${133 + (r - 167) / 2} ${t - 7}, ${137 + (r - 167) / 2}
        `
    }, null)]), createVNode("div", {
      class: "border-box-content"
    }, [renderSlot(e, "default")])]);
  }
});
var Cn = {
  install(e) {
    e.component("DvBorderBox11", Cp);
  }
};
var xt = ["#2e6099", "#7ce7fd"];
var _p = defineComponent({
  props: Ie,
  setup(e) {
    const t = Ve(), r = ref(null), {
      width: n,
      height: a,
      initWH: o
    } = xe(r), l = ref(`borderr-box-12-filterId-${t}`), s = He(xt, toRef(e, "color"));
    return {
      width: n,
      height: a,
      filterId: l,
      mergedColor: s,
      initWH: o,
      borderBox12: r
    };
  },
  render() {
    const {
      $slots: e,
      width: t,
      height: r,
      filterId: n,
      mergedColor: a,
      backgroundColor: o
    } = this;
    return createVNode("div", {
      ref: "borderBox12",
      class: "dv-border-box-12"
    }, [createVNode("svg", {
      class: "dv-border-svg-container",
      width: t,
      height: r
    }, [createVNode("defs", null, [createVNode("filter", {
      id: n,
      height: "150%",
      width: "150%",
      x: "-25%",
      y: "-25%"
    }, [createVNode("feMorphology", {
      operator: "dilate",
      radius: "1",
      in: "SourceAlpha",
      result: "thicken"
    }, null), createVNode("feGaussianBlur", {
      in: "thicken",
      stdDeviation: "2",
      result: "blurred"
    }, null), createVNode("feFlood", {
      "flood-color": De(a[1] || xt[1], 70),
      result: "glowColor"
    }, [createVNode("animate", {
      attributeName: "flood-color",
      values: `
                ${De(a[1] || xt[1], 70)};
                ${De(a[1] || xt[1], 30)};
                ${De(a[1] || xt[1], 70)};
              `,
      dur: "3s",
      begin: "0s",
      repeatCount: "indefinite"
    }, null)]), createVNode("feComposite", {
      in: "glowColor",
      in2: "blurred",
      operator: "in",
      result: "softGlowColored"
    }, null), createVNode("feMerge", null, [createVNode("feMergeNode", {
      in: "softGlowColored"
    }, null), createVNode("feMergeNode", {
      in: "SourceGraphic"
    }, null)])])]), t && r && createVNode("path", {
      fill: o,
      "stroke-width": "2",
      stroke: a[0],
      d: `
          M15 5 L ${t - 15} 5 Q ${t - 5} 5, ${t - 5} 15
          L ${t - 5} ${r - 15} Q ${t - 5} ${r - 5}, ${t - 15} ${r - 5}
          L 15, ${r - 5} Q 5 ${r - 5} 5 ${r - 15} L 5 15
          Q 5 5 15 5
        `
    }, null), createVNode("path", {
      "stroke-width": "2",
      fill: "transparent",
      "stroke-linecap": "round",
      filter: `url(#${n})`,
      stroke: a[1],
      d: "M 20 5 L 15 5 Q 5 5 5 15 L 5 20"
    }, null), createVNode("path", {
      "stroke-width": "2",
      fill: "transparent",
      "stroke-linecap": "round",
      filter: `url(#${n})`,
      stroke: a[1],
      d: `M ${t - 20} 5 L ${t - 15} 5 Q ${t - 5} 5 ${t - 5} 15 L ${t - 5} 20`
    }, null), createVNode("path", {
      "stroke-width": "2",
      fill: "transparent",
      "stroke-linecap": "round",
      filter: `url(#${n})`,
      stroke: a[1],
      d: `
          M ${t - 20} ${r - 5} L ${t - 15} ${r - 5}
          Q ${t - 5} ${r - 5} ${t - 5} ${r - 15}
          L ${t - 5} ${r - 20}
          `
    }, null), createVNode("path", {
      "stroke-width": "2",
      fill: "transparent",
      "stroke-linecap": "round",
      filter: `url(#${n})`,
      stroke: a[1],
      d: `
          M 20 ${r - 5} L 15 ${r - 5}
          Q 5 ${r - 5} 5 ${r - 15}
          L 5 ${r - 20}
          `
    }, null)]), createVNode("div", {
      class: "border-box-content"
    }, [renderSlot(e, "default")])]);
  }
});
var _n = {
  install(e) {
    e.component("DvBorderBox12", _p);
  }
};
var $p = ["#6586ec", "#2cf7fe"];
var Pp = defineComponent({
  props: Ie,
  setup(e) {
    const t = ref(null), {
      width: r,
      height: n,
      initWH: a
    } = xe(t), o = He($p, toRef(e, "color"));
    return {
      width: r,
      height: n,
      mergedColor: o,
      initWH: a,
      borderBox13: t
    };
  },
  render() {
    const {
      $slots: e,
      width: t,
      height: r,
      mergedColor: n,
      backgroundColor: a
    } = this;
    return createVNode("div", {
      ref: "borderBox13",
      class: "dv-border-box-13"
    }, [createVNode("svg", {
      class: "dv-border-svg-container",
      width: t,
      height: r
    }, [createVNode("path", {
      fill: a,
      stroke: n[0],
      d: `
          M 5 20 L 5 10 L 12 3  L 60 3 L 68 10
          L ${t - 20} 10 L ${t - 5} 25
          L ${t - 5} ${r - 5} L 20 ${r - 5}
          L 5 ${r - 20} L 5 20
        `
    }, null), createVNode("path", {
      fill: "transparent",
      "stroke-width": "3",
      "stroke-linecap": "round",
      "stroke-dasharray": "10, 5",
      stroke: n[0],
      d: "M 16 9 L 61 9"
    }, null), createVNode("path", {
      fill: "transparent",
      stroke: n[1],
      d: "M 5 20 L 5 10 L 12 3  L 60 3 L 68 10"
    }, null), createVNode("path", {
      fill: "transparent",
      stroke: n[1],
      d: `M ${t - 5} ${r - 30} L ${t - 5} ${r - 5} L ${t - 30} ${r - 5}`
    }, null)]), createVNode("div", {
      class: "border-box-content"
    }, [renderSlot(e, "default")])]);
  }
});
var $n = {
  install(e) {
    e.component("DvBorderBox13", Pp);
  }
};
var Ap = {
  install(e) {
    var t, r, n, a, o, l, s, D, W, M, U, F, A, v, R, N, I, E, b, $, f, _, O, m, C, d, G, L, g, V, X, Z, c, y, h2, P, q, K, ee;
    (t = ir.install) == null || t.call(ir, e), (r = or.install) == null || r.call(or, e), (n = lr.install) == null || n.call(lr, e), (a = Dr.install) == null || a.call(Dr, e), (o = Br.install) == null || o.call(Br, e), (l = Fr.install) == null || l.call(Fr, e), (s = Nr.install) == null || s.call(Nr, e), (D = jr.install) == null || D.call(jr, e), (W = Er.install) == null || W.call(Er, e), (M = Ir.install) == null || M.call(Ir, e), (U = Hr.install) == null || U.call(Hr, e), (F = Vr.install) == null || F.call(Vr, e), (A = Ur.install) == null || A.call(Ur, e), (v = Xr.install) == null || v.call(Xr, e), (R = Qr.install) == null || R.call(Qr, e), (N = Yr.install) == null || N.call(Yr, e), (I = Kr.install) == null || I.call(Kr, e), (E = Jr.install) == null || E.call(Jr, e), (b = Zr.install) == null || b.call(Zr, e), ($ = tn.install) == null || $.call(tn, e), (f = rn.install) == null || f.call(rn, e), (_ = nn.install) == null || _.call(nn, e), (O = an.install) == null || O.call(an, e), (m = on.install) == null || m.call(on, e), (C = ln.install) == null || C.call(ln, e), (d = sn.install) == null || d.call(sn, e), (G = fn.install) == null || G.call(fn, e), (L = dn.install) == null || L.call(dn, e), (g = hn.install) == null || g.call(hn, e), (V = vn.install) == null || V.call(vn, e), (X = pn.install) == null || X.call(pn, e), (Z = gn.install) == null || Z.call(gn, e), (c = mn.install) == null || c.call(mn, e), (y = yn.install) == null || y.call(yn, e), (h2 = bn.install) == null || h2.call(bn, e), (P = xn.install) == null || P.call(xn, e), (q = Cn.install) == null || q.call(Cn, e), (K = _n.install) == null || K.call(_n, e), (ee = $n.install) == null || ee.call($n, e);
  }
};
export {
  Qu as ActiveRingChart,
  Vr as ActiveRingChartPlugin,
  Kv as BorderBox1,
  bp as BorderBox10,
  xn as BorderBox10Plugin,
  Cp as BorderBox11,
  Cn as BorderBox11Plugin,
  _p as BorderBox12,
  _n as BorderBox12Plugin,
  Pp as BorderBox13,
  $n as BorderBox13Plugin,
  fn as BorderBox1Plugin,
  Zv as BorderBox2,
  dn as BorderBox2Plugin,
  tp as BorderBox3,
  hn as BorderBox3Plugin,
  ap as BorderBox4,
  vn as BorderBox4Plugin,
  lp as BorderBox5,
  pn as BorderBox5Plugin,
  up as BorderBox6,
  gn as BorderBox6Plugin,
  fp as BorderBox7,
  mn as BorderBox7Plugin,
  vp as BorderBox8,
  yn as BorderBox8Plugin,
  gp as BorderBox9,
  bn as BorderBox9Plugin,
  Ho as Button,
  ir as ButtonPlugin,
  Hu as CapsuleChart,
  Hr as CapsuleChartPlugin,
  Fu as Charts,
  Ir as ChartsPlugin,
  cl as ConicalColumnChart,
  or as ConicalColumnChartPlugin,
  sc as Decoration1,
  Af as Decoration10,
  on as Decoration10Plugin,
  Ff as Decoration11,
  ln as Decoration11Plugin,
  td as Decoration12,
  sn as Decoration12Plugin,
  Qr as Decoration1Plugin,
  vc as Decoration2,
  Yr as Decoration2Plugin,
  yc as Decoration3,
  Kr as Decoration3Plugin,
  _c as Decoration4,
  Jr as Decoration4Plugin,
  Lc as Decoration5,
  Zr as Decoration5Plugin,
  Rc as Decoration6,
  tn as Decoration6Plugin,
  Wc as Decoration7,
  rn as Decoration7Plugin,
  Vc as Decoration8,
  nn as Decoration8Plugin,
  nf as Decoration9,
  an as Decoration9Plugin,
  xi as DigitalFlop,
  Ur as DigitalFlopPlugin,
  Us as FlylineChart,
  Cs as FlylineChartEnhanced,
  Fr as FlylineChartEnhancedPlugin,
  Nr as FlylineChartPlugin,
  Yu as FullScreenContainer,
  Xr as FullScreenContainerPlugin,
  Yl as Loading,
  Br as LoadingPlugin,
  yl as PercentPond,
  lr as PercentPondPlugin,
  ou as ScrollBoard,
  Er as ScrollBoardPlugin,
  nu as ScrollRankingBoard,
  jr as ScrollRankingBoardPlugin,
  Il as WaterLevelPond,
  Dr as WaterLevelPondPlugin,
  Ap as default
};
//# sourceMappingURL=@kjgl77_datav-vue3.js.map
