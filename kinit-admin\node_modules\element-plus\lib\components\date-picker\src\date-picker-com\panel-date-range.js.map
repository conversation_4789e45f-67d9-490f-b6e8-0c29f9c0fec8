{"version": 3, "file": "panel-date-range.js", "sources": ["../../../../../../../packages/components/date-picker/src/date-picker-com/panel-date-range.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[\n      ppNs.b(),\n      drpNs.b(),\n      {\n        'has-sidebar': $slots.sidebar || hasShortcuts,\n        'has-time': showTime,\n      },\n    ]\"\n  >\n    <div :class=\"ppNs.e('body-wrapper')\">\n      <slot name=\"sidebar\" :class=\"ppNs.e('sidebar')\" />\n      <div v-if=\"hasShortcuts\" :class=\"ppNs.e('sidebar')\">\n        <button\n          v-for=\"(shortcut, key) in shortcuts\"\n          :key=\"key\"\n          type=\"button\"\n          :class=\"ppNs.e('shortcut')\"\n          @click=\"handleShortcutClick(shortcut)\"\n        >\n          {{ shortcut.text }}\n        </button>\n      </div>\n      <div :class=\"ppNs.e('body')\">\n        <div v-if=\"showTime\" :class=\"drpNs.e('time-header')\">\n          <span :class=\"drpNs.e('editors-wrap')\">\n            <span :class=\"drpNs.e('time-picker-wrap')\">\n              <el-input\n                size=\"small\"\n                :disabled=\"rangeState.selecting\"\n                :placeholder=\"t('el.datepicker.startDate')\"\n                :class=\"drpNs.e('editor')\"\n                :model-value=\"minVisibleDate\"\n                :validate-event=\"false\"\n                @input=\"(val) => handleDateInput(val, 'min')\"\n                @change=\"(val) => handleDateChange(val, 'min')\"\n              />\n            </span>\n            <span\n              v-clickoutside=\"handleMinTimeClose\"\n              :class=\"drpNs.e('time-picker-wrap')\"\n            >\n              <el-input\n                size=\"small\"\n                :class=\"drpNs.e('editor')\"\n                :disabled=\"rangeState.selecting\"\n                :placeholder=\"t('el.datepicker.startTime')\"\n                :model-value=\"minVisibleTime\"\n                :validate-event=\"false\"\n                @focus=\"minTimePickerVisible = true\"\n                @input=\"(val) => handleTimeInput(val, 'min')\"\n                @change=\"(val) => handleTimeChange(val, 'min')\"\n              />\n              <time-pick-panel\n                :visible=\"minTimePickerVisible\"\n                :format=\"timeFormat\"\n                datetime-role=\"start\"\n                :parsed-value=\"leftDate\"\n                @pick=\"handleMinTimePick\"\n              />\n            </span>\n          </span>\n          <span>\n            <el-icon><arrow-right /></el-icon>\n          </span>\n          <span :class=\"drpNs.e('editors-wrap')\" class=\"is-right\">\n            <span :class=\"drpNs.e('time-picker-wrap')\">\n              <el-input\n                size=\"small\"\n                :class=\"drpNs.e('editor')\"\n                :disabled=\"rangeState.selecting\"\n                :placeholder=\"t('el.datepicker.endDate')\"\n                :model-value=\"maxVisibleDate\"\n                :readonly=\"!minDate\"\n                :validate-event=\"false\"\n                @input=\"(val) => handleDateInput(val, 'max')\"\n                @change=\"(val) => handleDateChange(val, 'max')\"\n              />\n            </span>\n            <span\n              v-clickoutside=\"handleMaxTimeClose\"\n              :class=\"drpNs.e('time-picker-wrap')\"\n            >\n              <el-input\n                size=\"small\"\n                :class=\"drpNs.e('editor')\"\n                :disabled=\"rangeState.selecting\"\n                :placeholder=\"t('el.datepicker.endTime')\"\n                :model-value=\"maxVisibleTime\"\n                :readonly=\"!minDate\"\n                :validate-event=\"false\"\n                @focus=\"minDate && (maxTimePickerVisible = true)\"\n                @input=\"(val) => handleTimeInput(val, 'max')\"\n                @change=\"(val) => handleTimeChange(val, 'max')\"\n              />\n              <time-pick-panel\n                datetime-role=\"end\"\n                :visible=\"maxTimePickerVisible\"\n                :format=\"timeFormat\"\n                :parsed-value=\"rightDate\"\n                @pick=\"handleMaxTimePick\"\n              />\n            </span>\n          </span>\n        </div>\n        <div :class=\"[ppNs.e('content'), drpNs.e('content')]\" class=\"is-left\">\n          <div :class=\"drpNs.e('header')\">\n            <button\n              type=\"button\"\n              :class=\"ppNs.e('icon-btn')\"\n              :aria-label=\"t(`el.datepicker.prevYear`)\"\n              class=\"d-arrow-left\"\n              @click=\"leftPrevYear\"\n            >\n              <el-icon><d-arrow-left /></el-icon>\n            </button>\n            <button\n              type=\"button\"\n              :class=\"ppNs.e('icon-btn')\"\n              :aria-label=\"t(`el.datepicker.prevMonth`)\"\n              class=\"arrow-left\"\n              @click=\"leftPrevMonth\"\n            >\n              <el-icon><arrow-left /></el-icon>\n            </button>\n            <button\n              v-if=\"unlinkPanels\"\n              type=\"button\"\n              :disabled=\"!enableYearArrow\"\n              :class=\"[ppNs.e('icon-btn'), { 'is-disabled': !enableYearArrow }]\"\n              :aria-label=\"t(`el.datepicker.nextYear`)\"\n              class=\"d-arrow-right\"\n              @click=\"leftNextYear\"\n            >\n              <el-icon><d-arrow-right /></el-icon>\n            </button>\n            <button\n              v-if=\"unlinkPanels\"\n              type=\"button\"\n              :disabled=\"!enableMonthArrow\"\n              :class=\"[\n                ppNs.e('icon-btn'),\n                { 'is-disabled': !enableMonthArrow },\n              ]\"\n              :aria-label=\"t(`el.datepicker.nextMonth`)\"\n              class=\"arrow-right\"\n              @click=\"leftNextMonth\"\n            >\n              <el-icon><arrow-right /></el-icon>\n            </button>\n            <div>{{ leftLabel }}</div>\n          </div>\n          <date-table\n            selection-mode=\"range\"\n            :date=\"leftDate\"\n            :min-date=\"minDate\"\n            :max-date=\"maxDate\"\n            :range-state=\"rangeState\"\n            :disabled-date=\"disabledDate\"\n            :cell-class-name=\"cellClassName\"\n            @changerange=\"handleChangeRange\"\n            @pick=\"handleRangePick\"\n            @select=\"onSelect\"\n          />\n        </div>\n        <div :class=\"[ppNs.e('content'), drpNs.e('content')]\" class=\"is-right\">\n          <div :class=\"drpNs.e('header')\">\n            <button\n              v-if=\"unlinkPanels\"\n              type=\"button\"\n              :disabled=\"!enableYearArrow\"\n              :class=\"[ppNs.e('icon-btn'), { 'is-disabled': !enableYearArrow }]\"\n              :aria-label=\"t(`el.datepicker.prevYear`)\"\n              class=\"d-arrow-left\"\n              @click=\"rightPrevYear\"\n            >\n              <el-icon><d-arrow-left /></el-icon>\n            </button>\n            <button\n              v-if=\"unlinkPanels\"\n              type=\"button\"\n              :disabled=\"!enableMonthArrow\"\n              :class=\"[\n                ppNs.e('icon-btn'),\n                { 'is-disabled': !enableMonthArrow },\n              ]\"\n              :aria-label=\"t(`el.datepicker.prevMonth`)\"\n              class=\"arrow-left\"\n              @click=\"rightPrevMonth\"\n            >\n              <el-icon><arrow-left /></el-icon>\n            </button>\n            <button\n              type=\"button\"\n              :aria-label=\"t(`el.datepicker.nextYear`)\"\n              :class=\"ppNs.e('icon-btn')\"\n              class=\"d-arrow-right\"\n              @click=\"rightNextYear\"\n            >\n              <el-icon><d-arrow-right /></el-icon>\n            </button>\n            <button\n              type=\"button\"\n              :class=\"ppNs.e('icon-btn')\"\n              :aria-label=\"t(`el.datepicker.nextMonth`)\"\n              class=\"arrow-right\"\n              @click=\"rightNextMonth\"\n            >\n              <el-icon><arrow-right /></el-icon>\n            </button>\n            <div>{{ rightLabel }}</div>\n          </div>\n          <date-table\n            selection-mode=\"range\"\n            :date=\"rightDate\"\n            :min-date=\"minDate\"\n            :max-date=\"maxDate\"\n            :range-state=\"rangeState\"\n            :disabled-date=\"disabledDate\"\n            :cell-class-name=\"cellClassName\"\n            @changerange=\"handleChangeRange\"\n            @pick=\"handleRangePick\"\n            @select=\"onSelect\"\n          />\n        </div>\n      </div>\n    </div>\n    <div v-if=\"showTime\" :class=\"ppNs.e('footer')\">\n      <el-button\n        v-if=\"clearable\"\n        text\n        size=\"small\"\n        :class=\"ppNs.e('link-btn')\"\n        @click=\"handleClear\"\n      >\n        {{ t('el.datepicker.clear') }}\n      </el-button>\n      <el-button\n        plain\n        size=\"small\"\n        :class=\"ppNs.e('link-btn')\"\n        :disabled=\"btnDisabled\"\n        @click=\"handleRangeConfirm(false)\"\n      >\n        {{ t('el.datepicker.confirm') }}\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, ref, toRef, unref } from 'vue'\nimport dayjs from 'dayjs'\nimport { ClickOutside as vClickoutside } from '@element-plus/directives'\nimport { isArray } from '@element-plus/utils'\nimport { useLocale } from '@element-plus/hooks'\nimport ElButton from '@element-plus/components/button'\nimport ElInput from '@element-plus/components/input'\nimport {\n  TimePickPanel,\n  extractDateFormat,\n  extractTimeFormat,\n} from '@element-plus/components/time-picker'\nimport ElIcon from '@element-plus/components/icon'\nimport {\n  ArrowLeft,\n  ArrowRight,\n  DArrowLeft,\n  DArrowRight,\n} from '@element-plus/icons-vue'\nimport { panelDateRangeProps } from '../props/panel-date-range'\nimport { useRangePicker } from '../composables/use-range-picker'\nimport { getDefaultValue, isValidRange } from '../utils'\nimport DateTable from './basic-date-table.vue'\n\nimport type { Dayjs } from 'dayjs'\n\ntype ChangeType = 'min' | 'max'\ntype UserInput = {\n  min: string | null\n  max: string | null\n}\n\nconst props = defineProps(panelDateRangeProps)\nconst emit = defineEmits([\n  'pick',\n  'set-picker-option',\n  'calendar-change',\n  'panel-change',\n])\n\nconst unit = 'month'\n// FIXME: fix the type for ep picker\nconst pickerBase = inject('EP_PICKER_BASE') as any\nconst { disabledDate, cellClassName, format, defaultTime, clearable } =\n  pickerBase.props\nconst shortcuts = toRef(pickerBase.props, 'shortcuts')\nconst defaultValue = toRef(pickerBase.props, 'defaultValue')\nconst { lang } = useLocale()\nconst leftDate = ref<Dayjs>(dayjs().locale(lang.value))\nconst rightDate = ref<Dayjs>(dayjs().locale(lang.value).add(1, unit))\n\nconst {\n  minDate,\n  maxDate,\n  rangeState,\n  ppNs,\n  drpNs,\n\n  handleChangeRange,\n  handleRangeConfirm,\n  handleShortcutClick,\n  onSelect,\n  t,\n} = useRangePicker(props, {\n  defaultValue,\n  leftDate,\n  rightDate,\n  unit,\n  onParsedValueChanged,\n})\n\nconst dateUserInput = ref<UserInput>({\n  min: null,\n  max: null,\n})\n\nconst timeUserInput = ref<UserInput>({\n  min: null,\n  max: null,\n})\n\nconst leftLabel = computed(() => {\n  return `${leftDate.value.year()} ${t('el.datepicker.year')} ${t(\n    `el.datepicker.month${leftDate.value.month() + 1}`\n  )}`\n})\n\nconst rightLabel = computed(() => {\n  return `${rightDate.value.year()} ${t('el.datepicker.year')} ${t(\n    `el.datepicker.month${rightDate.value.month() + 1}`\n  )}`\n})\n\nconst leftYear = computed(() => {\n  return leftDate.value.year()\n})\n\nconst leftMonth = computed(() => {\n  return leftDate.value.month()\n})\n\nconst rightYear = computed(() => {\n  return rightDate.value.year()\n})\n\nconst rightMonth = computed(() => {\n  return rightDate.value.month()\n})\n\nconst hasShortcuts = computed(() => !!shortcuts.value.length)\n\nconst minVisibleDate = computed(() => {\n  if (dateUserInput.value.min !== null) return dateUserInput.value.min\n  if (minDate.value) return minDate.value.format(dateFormat.value)\n  return ''\n})\n\nconst maxVisibleDate = computed(() => {\n  if (dateUserInput.value.max !== null) return dateUserInput.value.max\n  if (maxDate.value || minDate.value)\n    return (maxDate.value || minDate.value)!.format(dateFormat.value)\n  return ''\n})\n\nconst minVisibleTime = computed(() => {\n  if (timeUserInput.value.min !== null) return timeUserInput.value.min\n  if (minDate.value) return minDate.value.format(timeFormat.value)\n  return ''\n})\n\nconst maxVisibleTime = computed(() => {\n  if (timeUserInput.value.max !== null) return timeUserInput.value.max\n  if (maxDate.value || minDate.value)\n    return (maxDate.value || minDate.value)!.format(timeFormat.value)\n  return ''\n})\n\nconst timeFormat = computed(() => {\n  return props.timeFormat || extractTimeFormat(format)\n})\n\nconst dateFormat = computed(() => {\n  return props.dateFormat || extractDateFormat(format)\n})\n\nconst isValidValue = (date: [Dayjs, Dayjs]) => {\n  return (\n    isValidRange(date) &&\n    (disabledDate\n      ? !disabledDate(date[0].toDate()) && !disabledDate(date[1].toDate())\n      : true)\n  )\n}\n\nconst leftPrevYear = () => {\n  leftDate.value = leftDate.value.subtract(1, 'year')\n  if (!props.unlinkPanels) {\n    rightDate.value = leftDate.value.add(1, 'month')\n  }\n  handlePanelChange('year')\n}\n\nconst leftPrevMonth = () => {\n  leftDate.value = leftDate.value.subtract(1, 'month')\n  if (!props.unlinkPanels) {\n    rightDate.value = leftDate.value.add(1, 'month')\n  }\n  handlePanelChange('month')\n}\n\nconst rightNextYear = () => {\n  if (!props.unlinkPanels) {\n    leftDate.value = leftDate.value.add(1, 'year')\n    rightDate.value = leftDate.value.add(1, 'month')\n  } else {\n    rightDate.value = rightDate.value.add(1, 'year')\n  }\n  handlePanelChange('year')\n}\n\nconst rightNextMonth = () => {\n  if (!props.unlinkPanels) {\n    leftDate.value = leftDate.value.add(1, 'month')\n    rightDate.value = leftDate.value.add(1, 'month')\n  } else {\n    rightDate.value = rightDate.value.add(1, 'month')\n  }\n  handlePanelChange('month')\n}\n\nconst leftNextYear = () => {\n  leftDate.value = leftDate.value.add(1, 'year')\n  handlePanelChange('year')\n}\n\nconst leftNextMonth = () => {\n  leftDate.value = leftDate.value.add(1, 'month')\n  handlePanelChange('month')\n}\n\nconst rightPrevYear = () => {\n  rightDate.value = rightDate.value.subtract(1, 'year')\n  handlePanelChange('year')\n}\n\nconst rightPrevMonth = () => {\n  rightDate.value = rightDate.value.subtract(1, 'month')\n  handlePanelChange('month')\n}\n\nconst handlePanelChange = (mode: 'month' | 'year') => {\n  emit(\n    'panel-change',\n    [leftDate.value.toDate(), rightDate.value.toDate()],\n    mode\n  )\n}\n\nconst enableMonthArrow = computed(() => {\n  const nextMonth = (leftMonth.value + 1) % 12\n  const yearOffset = leftMonth.value + 1 >= 12 ? 1 : 0\n  return (\n    props.unlinkPanels &&\n    new Date(leftYear.value + yearOffset, nextMonth) <\n      new Date(rightYear.value, rightMonth.value)\n  )\n})\n\nconst enableYearArrow = computed(() => {\n  return (\n    props.unlinkPanels &&\n    rightYear.value * 12 +\n      rightMonth.value -\n      (leftYear.value * 12 + leftMonth.value + 1) >=\n      12\n  )\n})\n\nconst btnDisabled = computed(() => {\n  return !(\n    minDate.value &&\n    maxDate.value &&\n    !rangeState.value.selecting &&\n    isValidRange([minDate.value, maxDate.value])\n  )\n})\n\nconst showTime = computed(\n  () => props.type === 'datetime' || props.type === 'datetimerange'\n)\n\nconst formatEmit = (emitDayjs: Dayjs | null, index?: number) => {\n  if (!emitDayjs) return\n  if (defaultTime) {\n    const defaultTimeD = dayjs(\n      defaultTime[index as number] || defaultTime\n    ).locale(lang.value)\n    return defaultTimeD\n      .year(emitDayjs.year())\n      .month(emitDayjs.month())\n      .date(emitDayjs.date())\n  }\n  return emitDayjs\n}\n\nconst handleRangePick = (\n  val: {\n    minDate: Dayjs\n    maxDate: Dayjs | null\n  },\n  close = true\n) => {\n  const min_ = val.minDate\n  const max_ = val.maxDate\n  const minDate_ = formatEmit(min_, 0)\n  const maxDate_ = formatEmit(max_, 1)\n\n  if (maxDate.value === maxDate_ && minDate.value === minDate_) {\n    return\n  }\n  emit('calendar-change', [min_.toDate(), max_ && max_.toDate()])\n  maxDate.value = maxDate_\n  minDate.value = minDate_\n\n  if (!close || showTime.value) return\n  handleRangeConfirm()\n}\n\nconst minTimePickerVisible = ref(false)\nconst maxTimePickerVisible = ref(false)\n\nconst handleMinTimeClose = () => {\n  minTimePickerVisible.value = false\n}\n\nconst handleMaxTimeClose = () => {\n  maxTimePickerVisible.value = false\n}\n\nconst handleDateInput = (value: string | null, type: ChangeType) => {\n  dateUserInput.value[type] = value\n  const parsedValueD = dayjs(value, dateFormat.value).locale(lang.value)\n  if (parsedValueD.isValid()) {\n    if (disabledDate && disabledDate(parsedValueD.toDate())) {\n      return\n    }\n    if (type === 'min') {\n      leftDate.value = parsedValueD\n      minDate.value = (minDate.value || leftDate.value)\n        .year(parsedValueD.year())\n        .month(parsedValueD.month())\n        .date(parsedValueD.date())\n      if (\n        !props.unlinkPanels &&\n        (!maxDate.value || maxDate.value.isBefore(minDate.value))\n      ) {\n        rightDate.value = parsedValueD.add(1, 'month')\n        maxDate.value = minDate.value.add(1, 'month')\n      }\n    } else {\n      rightDate.value = parsedValueD\n      maxDate.value = (maxDate.value || rightDate.value)\n        .year(parsedValueD.year())\n        .month(parsedValueD.month())\n        .date(parsedValueD.date())\n      if (\n        !props.unlinkPanels &&\n        (!minDate.value || minDate.value.isAfter(maxDate.value))\n      ) {\n        leftDate.value = parsedValueD.subtract(1, 'month')\n        minDate.value = maxDate.value.subtract(1, 'month')\n      }\n    }\n  }\n}\n\nconst handleDateChange = (_: unknown, type: ChangeType) => {\n  dateUserInput.value[type] = null\n}\n\nconst handleTimeInput = (value: string | null, type: ChangeType) => {\n  timeUserInput.value[type] = value\n  const parsedValueD = dayjs(value, timeFormat.value).locale(lang.value)\n\n  if (parsedValueD.isValid()) {\n    if (type === 'min') {\n      minTimePickerVisible.value = true\n      minDate.value = (minDate.value || leftDate.value)\n        .hour(parsedValueD.hour())\n        .minute(parsedValueD.minute())\n        .second(parsedValueD.second())\n      if (!maxDate.value || maxDate.value.isBefore(minDate.value)) {\n        maxDate.value = minDate.value\n      }\n    } else {\n      maxTimePickerVisible.value = true\n      maxDate.value = (maxDate.value || rightDate.value)\n        .hour(parsedValueD.hour())\n        .minute(parsedValueD.minute())\n        .second(parsedValueD.second())\n      rightDate.value = maxDate.value\n      if (maxDate.value && maxDate.value.isBefore(minDate.value)) {\n        minDate.value = maxDate.value\n      }\n    }\n  }\n}\n\nconst handleTimeChange = (value: string | null, type: ChangeType) => {\n  timeUserInput.value[type] = null\n  if (type === 'min') {\n    leftDate.value = minDate.value!\n    minTimePickerVisible.value = false\n  } else {\n    rightDate.value = maxDate.value!\n    maxTimePickerVisible.value = false\n  }\n}\n\nconst handleMinTimePick = (value: Dayjs, visible: boolean, first: boolean) => {\n  if (timeUserInput.value.min) return\n  if (value) {\n    leftDate.value = value\n    minDate.value = (minDate.value || leftDate.value)\n      .hour(value.hour())\n      .minute(value.minute())\n      .second(value.second())\n  }\n\n  if (!first) {\n    minTimePickerVisible.value = visible\n  }\n\n  if (!maxDate.value || maxDate.value.isBefore(minDate.value)) {\n    maxDate.value = minDate.value\n    rightDate.value = value\n  }\n}\n\nconst handleMaxTimePick = (\n  value: Dayjs | null,\n  visible: boolean,\n  first: boolean\n) => {\n  if (timeUserInput.value.max) return\n  if (value) {\n    rightDate.value = value\n    maxDate.value = (maxDate.value || rightDate.value)\n      .hour(value.hour())\n      .minute(value.minute())\n      .second(value.second())\n  }\n\n  if (!first) {\n    maxTimePickerVisible.value = visible\n  }\n\n  if (maxDate.value && maxDate.value.isBefore(minDate.value)) {\n    minDate.value = maxDate.value\n  }\n}\n\nconst handleClear = () => {\n  leftDate.value = getDefaultValue(unref(defaultValue), {\n    lang: unref(lang),\n    unit: 'month',\n    unlinkPanels: props.unlinkPanels,\n  })[0]\n  rightDate.value = leftDate.value.add(1, 'month')\n  emit('pick', null)\n}\n\nconst formatToString = (value: Dayjs | Dayjs[]) => {\n  return isArray(value)\n    ? value.map((_) => _.format(format))\n    : value.format(format)\n}\n\nconst parseUserInput = (value: Dayjs | Dayjs[]) => {\n  return isArray(value)\n    ? value.map((_) => dayjs(_, format).locale(lang.value))\n    : dayjs(value, format).locale(lang.value)\n}\n\nfunction onParsedValueChanged(\n  minDate: Dayjs | undefined,\n  maxDate: Dayjs | undefined\n) {\n  if (props.unlinkPanels && maxDate) {\n    const minDateYear = minDate?.year() || 0\n    const minDateMonth = minDate?.month() || 0\n    const maxDateYear = maxDate.year()\n    const maxDateMonth = maxDate.month()\n    rightDate.value =\n      minDateYear === maxDateYear && minDateMonth === maxDateMonth\n        ? maxDate.add(1, unit)\n        : maxDate\n  } else {\n    rightDate.value = leftDate.value.add(1, unit)\n    if (maxDate) {\n      rightDate.value = rightDate.value\n        .hour(maxDate.hour())\n        .minute(maxDate.minute())\n        .second(maxDate.second())\n    }\n  }\n}\n\nemit('set-picker-option', ['isValidValue', isValidValue])\nemit('set-picker-option', ['parseUserInput', parseUserInput])\nemit('set-picker-option', ['formatToString', formatToString])\nemit('set-picker-option', ['handleClear', handleClear])\n</script>\n"], "names": ["inject", "toRef", "useLocale", "ref", "dayjs", "useRangePicker", "computed", "extractTimeFormat", "extractDateFormat", "isValidRange", "getDefaultValue", "unref", "isArray"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsSA,IAAM,MAAA,UAAA,GAAaA,WAAO,gBAAgB,CAAA,CAAA;AAC1C,IAAA,MAAM,EAAE,YAAc,EAAA,aAAA,EAAe,MAAQ,EAAA,WAAA,EAAa,cACxD,UAAW,CAAA,KAAA,CAAA;AACb,IAAA,MAAM,SAAY,GAAAC,SAAA,CAAM,UAAW,CAAA,KAAA,EAAO,WAAW,CAAA,CAAA;AACrD,IAAA,MAAM,YAAe,GAAAA,SAAA,CAAM,UAAW,CAAA,KAAA,EAAO,cAAc,CAAA,CAAA;AAC3D,IAAM,MAAA,EAAE,SAASC,eAAU,EAAA,CAAA;AAC3B,IAAA,MAAM,WAAWC,OAAW,CAAAC,yBAAA,GAAQ,MAAO,CAAA,IAAA,CAAK,KAAK,CAAC,CAAA,CAAA;AACtD,IAAM,MAAA,SAAA,GAAYD,OAAW,CAAAC,yBAAA,EAAQ,CAAA,MAAA,CAAO,IAAK,CAAA,KAAK,CAAE,CAAA,GAAA,CAAI,CAAG,EAAA,IAAI,CAAC,CAAA,CAAA;AAEpE,IAAM,MAAA;AAAA,MACJ,OAAA;AAAA,MACA,OAAA;AAAA,MACA,UAAA;AAAA,MACA,IAAA;AAAA,MACA,KAAA;AAAA,MAEA,iBAAA;AAAA,MACA,kBAAA;AAAA,MACA,mBAAA;AAAA,MACA,QAAA;AAAA,MACA,CAAA;AAAA,KAAA,GACEC,8BAAe,KAAO,EAAA;AAAA,MACxB,YAAA;AAAA,MACA,QAAA;AAAA,MACA,SAAA;AAAA,MACA,IAAA;AAAA,MACA,oBAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAA,MAAM,gBAAgBF,OAAe,CAAA;AAAA,MACnC,GAAK,EAAA,IAAA;AAAA,MACL,GAAK,EAAA,IAAA;AAAA,KACN,CAAA,CAAA;AAED,IAAA,MAAM,gBAAgBA,OAAe,CAAA;AAAA,MACnC,GAAK,EAAA,IAAA;AAAA,MACL,GAAK,EAAA,IAAA;AAAA,KACN,CAAA,CAAA;AAED,IAAM,MAAA,SAAA,GAAYG,aAAS,MAAM;AAC/B,MAAA,OAAO,CAAG,EAAA,QAAA,CAAS,KAAM,CAAA,IAAA,MAAU,CAAE,CAAA,oBAAoB,CAAK,CAAA,CAAA,EAAA,CAAA,CAC5D,CAAsB,mBAAA,EAAA,QAAA,CAAS,KAAM,CAAA,KAAA,KAAU,CACjD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,UAAA,GAAaA,aAAS,MAAM;AAChC,MAAA,OAAO,CAAG,EAAA,SAAA,CAAU,KAAM,CAAA,IAAA,MAAU,CAAE,CAAA,oBAAoB,CAAK,CAAA,CAAA,EAAA,CAAA,CAC7D,CAAsB,mBAAA,EAAA,SAAA,CAAU,KAAM,CAAA,KAAA,KAAU,CAClD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,QAAA,GAAWA,aAAS,MAAM;AAC9B,MAAO,OAAA,QAAA,CAAS,MAAM,IAAK,EAAA,CAAA;AAAA,KAC5B,CAAA,CAAA;AAED,IAAM,MAAA,SAAA,GAAYA,aAAS,MAAM;AAC/B,MAAO,OAAA,QAAA,CAAS,MAAM,KAAM,EAAA,CAAA;AAAA,KAC7B,CAAA,CAAA;AAED,IAAM,MAAA,SAAA,GAAYA,aAAS,MAAM;AAC/B,MAAO,OAAA,SAAA,CAAU,MAAM,IAAK,EAAA,CAAA;AAAA,KAC7B,CAAA,CAAA;AAED,IAAM,MAAA,UAAA,GAAaA,aAAS,MAAM;AAChC,MAAO,OAAA,SAAA,CAAU,MAAM,KAAM,EAAA,CAAA;AAAA,KAC9B,CAAA,CAAA;AAED,IAAA,MAAM,eAAeA,YAAS,CAAA,MAAM,CAAC,CAAC,SAAA,CAAU,MAAM,MAAM,CAAA,CAAA;AAE5D,IAAM,MAAA,cAAA,GAAiBA,aAAS,MAAM;AACpC,MAAI,IAAA,aAAA,CAAc,MAAM,GAAQ,KAAA,IAAA;AAAM,QAAA,OAAO,cAAc,KAAM,CAAA,GAAA,CAAA;AACjE,MAAA,IAAI,OAAQ,CAAA,KAAA;AAAO,QAAA,OAAO,OAAQ,CAAA,KAAA,CAAM,MAAO,CAAA,UAAA,CAAW,KAAK,CAAA,CAAA;AAC/D,MAAO,OAAA,EAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,cAAA,GAAiBA,aAAS,MAAM;AACpC,MAAI,IAAA,aAAA,CAAc,MAAM,GAAQ,KAAA,IAAA;AAAM,QAAA,OAAO,cAAc,KAAM,CAAA,GAAA,CAAA;AACjE,MAAI,IAAA,OAAA,CAAQ,SAAS,OAAQ,CAAA,KAAA;AAC3B,QAAA,OAAQ,SAAQ,KAAS,IAAA,OAAA,CAAQ,KAAQ,EAAA,MAAA,CAAO,WAAW,KAAK,CAAA,CAAA;AAClE,MAAO,OAAA,EAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,cAAA,GAAiBA,aAAS,MAAM;AACpC,MAAI,IAAA,aAAA,CAAc,MAAM,GAAQ,KAAA,IAAA;AAAM,QAAA,OAAO,cAAc,KAAM,CAAA,GAAA,CAAA;AACjE,MAAA,IAAI,OAAQ,CAAA,KAAA;AAAO,QAAA,OAAO,OAAQ,CAAA,KAAA,CAAM,MAAO,CAAA,UAAA,CAAW,KAAK,CAAA,CAAA;AAC/D,MAAO,OAAA,EAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,cAAA,GAAiBA,aAAS,MAAM;AACpC,MAAI,IAAA,aAAA,CAAc,MAAM,GAAQ,KAAA,IAAA;AAAM,QAAA,OAAO,cAAc,KAAM,CAAA,GAAA,CAAA;AACjE,MAAI,IAAA,OAAA,CAAQ,SAAS,OAAQ,CAAA,KAAA;AAC3B,QAAA,OAAQ,SAAQ,KAAS,IAAA,OAAA,CAAQ,KAAQ,EAAA,MAAA,CAAO,WAAW,KAAK,CAAA,CAAA;AAClE,MAAO,OAAA,EAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,UAAA,GAAaA,aAAS,MAAM;AAChC,MAAO,OAAA,KAAA,CAAM,UAAc,IAAAC,uBAAA,CAAkB,MAAM,CAAA,CAAA;AAAA,KACpD,CAAA,CAAA;AAED,IAAM,MAAA,UAAA,GAAaD,aAAS,MAAM;AAChC,MAAO,OAAA,KAAA,CAAM,UAAc,IAAAE,uBAAA,CAAkB,MAAM,CAAA,CAAA;AAAA,KACpD,CAAA,CAAA;AAED,IAAM,MAAA,YAAA,GAAe,CAAC,IAAyB,KAAA;AAC7C,MAAA,OACEC,qBAAa,IAAI,CAAA,KAEb,YAAA,GAAA,CAAC,aAAa,IAAK,CAAA,CAAA,CAAA,CAAG,MAAO,EAAC,KAAK,CAAC,YAAA,CAAa,KAAK,CAAG,CAAA,CAAA,MAAA,EAAQ,CACjE,GAAA,IAAA,CAAA,CAAA;AAAA,KAER,CAAA;AAEA,IAAA,MAAM,eAAe,MAAM;AACzB,MAAA,QAAA,CAAS,KAAQ,GAAA,QAAA,CAAS,KAAM,CAAA,QAAA,CAAS,GAAG,MAAM,CAAA,CAAA;AAClD,MAAI,IAAA,CAAC,MAAM,YAAc,EAAA;AACvB,QAAA,SAAA,CAAU,KAAQ,GAAA,QAAA,CAAS,KAAM,CAAA,GAAA,CAAI,GAAG,OAAO,CAAA,CAAA;AAAA,OACjD;AACA,MAAA,iBAAA,CAAkB,MAAM,CAAA,CAAA;AAAA,KAC1B,CAAA;AAEA,IAAA,MAAM,gBAAgB,MAAM;AAC1B,MAAA,QAAA,CAAS,KAAQ,GAAA,QAAA,CAAS,KAAM,CAAA,QAAA,CAAS,GAAG,OAAO,CAAA,CAAA;AACnD,MAAI,IAAA,CAAC,MAAM,YAAc,EAAA;AACvB,QAAA,SAAA,CAAU,KAAQ,GAAA,QAAA,CAAS,KAAM,CAAA,GAAA,CAAI,GAAG,OAAO,CAAA,CAAA;AAAA,OACjD;AACA,MAAA,iBAAA,CAAkB,OAAO,CAAA,CAAA;AAAA,KAC3B,CAAA;AAEA,IAAA,MAAM,gBAAgB,MAAM;AAC1B,MAAI,IAAA,CAAC,MAAM,YAAc,EAAA;AACvB,QAAA,QAAA,CAAS,KAAQ,GAAA,QAAA,CAAS,KAAM,CAAA,GAAA,CAAI,GAAG,MAAM,CAAA,CAAA;AAC7C,QAAA,SAAA,CAAU,KAAQ,GAAA,QAAA,CAAS,KAAM,CAAA,GAAA,CAAI,GAAG,OAAO,CAAA,CAAA;AAAA,OAC1C,MAAA;AACL,QAAA,SAAA,CAAU,KAAQ,GAAA,SAAA,CAAU,KAAM,CAAA,GAAA,CAAI,GAAG,MAAM,CAAA,CAAA;AAAA,OACjD;AACA,MAAA,iBAAA,CAAkB,MAAM,CAAA,CAAA;AAAA,KAC1B,CAAA;AAEA,IAAA,MAAM,iBAAiB,MAAM;AAC3B,MAAI,IAAA,CAAC,MAAM,YAAc,EAAA;AACvB,QAAA,QAAA,CAAS,KAAQ,GAAA,QAAA,CAAS,KAAM,CAAA,GAAA,CAAI,GAAG,OAAO,CAAA,CAAA;AAC9C,QAAA,SAAA,CAAU,KAAQ,GAAA,QAAA,CAAS,KAAM,CAAA,GAAA,CAAI,GAAG,OAAO,CAAA,CAAA;AAAA,OAC1C,MAAA;AACL,QAAA,SAAA,CAAU,KAAQ,GAAA,SAAA,CAAU,KAAM,CAAA,GAAA,CAAI,GAAG,OAAO,CAAA,CAAA;AAAA,OAClD;AACA,MAAA,iBAAA,CAAkB,OAAO,CAAA,CAAA;AAAA,KAC3B,CAAA;AAEA,IAAA,MAAM,eAAe,MAAM;AACzB,MAAA,QAAA,CAAS,KAAQ,GAAA,QAAA,CAAS,KAAM,CAAA,GAAA,CAAI,GAAG,MAAM,CAAA,CAAA;AAC7C,MAAA,iBAAA,CAAkB,MAAM,CAAA,CAAA;AAAA,KAC1B,CAAA;AAEA,IAAA,MAAM,gBAAgB,MAAM;AAC1B,MAAA,QAAA,CAAS,KAAQ,GAAA,QAAA,CAAS,KAAM,CAAA,GAAA,CAAI,GAAG,OAAO,CAAA,CAAA;AAC9C,MAAA,iBAAA,CAAkB,OAAO,CAAA,CAAA;AAAA,KAC3B,CAAA;AAEA,IAAA,MAAM,gBAAgB,MAAM;AAC1B,MAAA,SAAA,CAAU,KAAQ,GAAA,SAAA,CAAU,KAAM,CAAA,QAAA,CAAS,GAAG,MAAM,CAAA,CAAA;AACpD,MAAA,iBAAA,CAAkB,MAAM,CAAA,CAAA;AAAA,KAC1B,CAAA;AAEA,IAAA,MAAM,iBAAiB,MAAM;AAC3B,MAAA,SAAA,CAAU,KAAQ,GAAA,SAAA,CAAU,KAAM,CAAA,QAAA,CAAS,GAAG,OAAO,CAAA,CAAA;AACrD,MAAA,iBAAA,CAAkB,OAAO,CAAA,CAAA;AAAA,KAC3B,CAAA;AAEA,IAAM,MAAA,iBAAA,GAAoB,CAAC,IAA2B,KAAA;AACpD,MACE,IAAA,CAAA,cAAA,EACA,CAAC,QAAA,CAAS,KAAM,CAAA,MAAA,EAAU,EAAA,SAAA,CAAU,KAAM,CAAA,MAAA,EAAQ,CAAA,EAClD,IACF,CAAA,CAAA;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,gBAAA,GAAmBH,aAAS,MAAM;AACtC,MAAM,MAAA,SAAA,GAAa,CAAU,SAAA,CAAA,KAAA,GAAQ,CAAK,IAAA,EAAA,CAAA;AAC1C,MAAA,MAAM,UAAa,GAAA,SAAA,CAAU,KAAQ,GAAA,CAAA,IAAK,KAAK,CAAI,GAAA,CAAA,CAAA;AACnD,MAAA,OACE,KAAM,CAAA,YAAA,IACN,IAAI,IAAA,CAAK,SAAS,KAAQ,GAAA,UAAA,EAAY,SAAS,CAAA,GAC7C,IAAI,IAAA,CAAK,SAAU,CAAA,KAAA,EAAO,WAAW,KAAK,CAAA,CAAA;AAAA,KAE/C,CAAA,CAAA;AAED,IAAM,MAAA,eAAA,GAAkBA,aAAS,MAAM;AACrC,MAAA,OACE,KAAM,CAAA,YAAA,IACN,SAAU,CAAA,KAAA,GAAQ,EAChB,GAAA,UAAA,CAAW,KACV,IAAA,QAAA,CAAS,KAAQ,GAAA,EAAA,GAAK,SAAU,CAAA,KAAA,GAAQ,CACzC,CAAA,IAAA,EAAA,CAAA;AAAA,KAEL,CAAA,CAAA;AAED,IAAM,MAAA,WAAA,GAAcA,aAAS,MAAM;AACjC,MAAA,OAAO,EACL,OAAA,CAAQ,KACR,IAAA,OAAA,CAAQ,SACR,CAAC,UAAA,CAAW,KAAM,CAAA,SAAA,IAClBG,qBAAa,CAAC,OAAA,CAAQ,KAAO,EAAA,OAAA,CAAQ,KAAK,CAAC,CAAA,CAAA,CAAA;AAAA,KAE9C,CAAA,CAAA;AAED,IAAM,MAAA,QAAA,GAAWH,aACf,MAAM,KAAA,CAAM,SAAS,UAAc,IAAA,KAAA,CAAM,SAAS,eACpD,CAAA,CAAA;AAEA,IAAM,MAAA,UAAA,GAAa,CAAC,SAAA,EAAyB,KAAmB,KAAA;AAC9D,MAAA,IAAI,CAAC,SAAA;AAAW,QAAA,OAAA;AAChB,MAAA,IAAI,WAAa,EAAA;AACf,QAAM,MAAA,YAAA,GAAeF,0BACnB,WAAY,CAAA,KAAA,CAAA,IAAoB,WAClC,CAAE,CAAA,MAAA,CAAO,KAAK,KAAK,CAAA,CAAA;AACnB,QAAA,OAAO,YACJ,CAAA,IAAA,CAAK,SAAU,CAAA,IAAA,EAAM,CACrB,CAAA,KAAA,CAAM,SAAU,CAAA,KAAA,EAAO,CAAA,CACvB,IAAK,CAAA,SAAA,CAAU,MAAM,CAAA,CAAA;AAAA,OAC1B;AACA,MAAO,OAAA,SAAA,CAAA;AAAA,KACT,CAAA;AAEA,IAAA,MAAM,eAAkB,GAAA,CACtB,GAIA,EAAA,KAAA,GAAQ,IACL,KAAA;AACH,MAAA,MAAM,OAAO,GAAI,CAAA,OAAA,CAAA;AACjB,MAAA,MAAM,OAAO,GAAI,CAAA,OAAA,CAAA;AACjB,MAAM,MAAA,QAAA,GAAW,UAAW,CAAA,IAAA,EAAM,CAAC,CAAA,CAAA;AACnC,MAAM,MAAA,QAAA,GAAW,UAAW,CAAA,IAAA,EAAM,CAAC,CAAA,CAAA;AAEnC,MAAA,IAAI,OAAQ,CAAA,KAAA,KAAU,QAAY,IAAA,OAAA,CAAQ,UAAU,QAAU,EAAA;AAC5D,QAAA,OAAA;AAAA,OACF;AACA,MAAK,IAAA,CAAA,iBAAA,EAAmB,CAAC,IAAK,CAAA,MAAA,IAAU,IAAQ,IAAA,IAAA,CAAK,MAAO,EAAC,CAAC,CAAA,CAAA;AAC9D,MAAA,OAAA,CAAQ,KAAQ,GAAA,QAAA,CAAA;AAChB,MAAA,OAAA,CAAQ,KAAQ,GAAA,QAAA,CAAA;AAEhB,MAAI,IAAA,CAAC,SAAS,QAAS,CAAA,KAAA;AAAO,QAAA,OAAA;AAC9B,MAAmB,kBAAA,EAAA,CAAA;AAAA,KACrB,CAAA;AAEA,IAAM,MAAA,oBAAA,GAAuBD,QAAI,KAAK,CAAA,CAAA;AACtC,IAAM,MAAA,oBAAA,GAAuBA,QAAI,KAAK,CAAA,CAAA;AAEtC,IAAA,MAAM,qBAAqB,MAAM;AAC/B,MAAA,oBAAA,CAAqB,KAAQ,GAAA,KAAA,CAAA;AAAA,KAC/B,CAAA;AAEA,IAAA,MAAM,qBAAqB,MAAM;AAC/B,MAAA,oBAAA,CAAqB,KAAQ,GAAA,KAAA,CAAA;AAAA,KAC/B,CAAA;AAEA,IAAM,MAAA,eAAA,GAAkB,CAAC,KAAA,EAAsB,IAAqB,KAAA;AAClE,MAAA,aAAA,CAAc,MAAM,IAAQ,CAAA,GAAA,KAAA,CAAA;AAC5B,MAAM,MAAA,YAAA,GAAeC,0BAAM,KAAO,EAAA,UAAA,CAAW,KAAK,CAAE,CAAA,MAAA,CAAO,KAAK,KAAK,CAAA,CAAA;AACrE,MAAI,IAAA,YAAA,CAAa,SAAW,EAAA;AAC1B,QAAA,IAAI,YAAgB,IAAA,YAAA,CAAa,YAAa,CAAA,MAAA,EAAQ,CAAG,EAAA;AACvD,UAAA,OAAA;AAAA,SACF;AACA,QAAA,IAAI,SAAS,KAAO,EAAA;AAClB,UAAA,QAAA,CAAS,KAAQ,GAAA,YAAA,CAAA;AACjB,UAAA,OAAA,CAAQ,QAAS,CAAQ,OAAA,CAAA,KAAA,IAAS,SAAS,KACxC,EAAA,IAAA,CAAK,aAAa,IAAK,EAAC,CACxB,CAAA,KAAA,CAAM,aAAa,KAAM,EAAC,EAC1B,IAAK,CAAA,YAAA,CAAa,MAAM,CAAA,CAAA;AAC3B,UACE,IAAA,CAAC,KAAM,CAAA,YAAA,KACL,CAAA,OAAA,CAAQ,KAAS,IAAA,OAAA,CAAQ,KAAM,CAAA,QAAA,CAAS,OAAQ,CAAA,KAAK,CACvD,CAAA,EAAA;AACA,YAAA,SAAA,CAAU,KAAQ,GAAA,YAAA,CAAa,GAAI,CAAA,CAAA,EAAG,OAAO,CAAA,CAAA;AAC7C,YAAA,OAAA,CAAQ,KAAQ,GAAA,OAAA,CAAQ,KAAM,CAAA,GAAA,CAAI,GAAG,OAAO,CAAA,CAAA;AAAA,WAC9C;AAAA,SACK,MAAA;AACL,UAAA,SAAA,CAAU,KAAQ,GAAA,YAAA,CAAA;AAClB,UAAA,OAAA,CAAQ,QAAS,CAAQ,OAAA,CAAA,KAAA,IAAS,UAAU,KACzC,EAAA,IAAA,CAAK,aAAa,IAAK,EAAC,CACxB,CAAA,KAAA,CAAM,aAAa,KAAM,EAAC,EAC1B,IAAK,CAAA,YAAA,CAAa,MAAM,CAAA,CAAA;AAC3B,UACE,IAAA,CAAC,KAAM,CAAA,YAAA,KACL,CAAA,OAAA,CAAQ,KAAS,IAAA,OAAA,CAAQ,KAAM,CAAA,OAAA,CAAQ,OAAQ,CAAA,KAAK,CACtD,CAAA,EAAA;AACA,YAAA,QAAA,CAAS,KAAQ,GAAA,YAAA,CAAa,QAAS,CAAA,CAAA,EAAG,OAAO,CAAA,CAAA;AACjD,YAAA,OAAA,CAAQ,KAAQ,GAAA,OAAA,CAAQ,KAAM,CAAA,QAAA,CAAS,GAAG,OAAO,CAAA,CAAA;AAAA,WACnD;AAAA,SACF;AAAA,OACF;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,gBAAA,GAAmB,CAAC,CAAA,EAAY,IAAqB,KAAA;AACzD,MAAA,aAAA,CAAc,MAAM,IAAQ,CAAA,GAAA,IAAA,CAAA;AAAA,KAC9B,CAAA;AAEA,IAAM,MAAA,eAAA,GAAkB,CAAC,KAAA,EAAsB,IAAqB,KAAA;AAClE,MAAA,aAAA,CAAc,MAAM,IAAQ,CAAA,GAAA,KAAA,CAAA;AAC5B,MAAM,MAAA,YAAA,GAAeA,0BAAM,KAAO,EAAA,UAAA,CAAW,KAAK,CAAE,CAAA,MAAA,CAAO,KAAK,KAAK,CAAA,CAAA;AAErE,MAAI,IAAA,YAAA,CAAa,SAAW,EAAA;AAC1B,QAAA,IAAI,SAAS,KAAO,EAAA;AAClB,UAAA,oBAAA,CAAqB,KAAQ,GAAA,IAAA,CAAA;AAC7B,UAAA,OAAA,CAAQ,QAAS,CAAQ,OAAA,CAAA,KAAA,IAAS,SAAS,KACxC,EAAA,IAAA,CAAK,aAAa,IAAK,EAAC,CACxB,CAAA,MAAA,CAAO,aAAa,MAAO,EAAC,EAC5B,MAAO,CAAA,YAAA,CAAa,QAAQ,CAAA,CAAA;AAC/B,UAAI,IAAA,CAAC,QAAQ,KAAS,IAAA,OAAA,CAAQ,MAAM,QAAS,CAAA,OAAA,CAAQ,KAAK,CAAG,EAAA;AAC3D,YAAA,OAAA,CAAQ,QAAQ,OAAQ,CAAA,KAAA,CAAA;AAAA,WAC1B;AAAA,SACK,MAAA;AACL,UAAA,oBAAA,CAAqB,KAAQ,GAAA,IAAA,CAAA;AAC7B,UAAA,OAAA,CAAQ,QAAS,CAAQ,OAAA,CAAA,KAAA,IAAS,UAAU,KACzC,EAAA,IAAA,CAAK,aAAa,IAAK,EAAC,CACxB,CAAA,MAAA,CAAO,aAAa,MAAO,EAAC,EAC5B,MAAO,CAAA,YAAA,CAAa,QAAQ,CAAA,CAAA;AAC/B,UAAA,SAAA,CAAU,QAAQ,OAAQ,CAAA,KAAA,CAAA;AAC1B,UAAA,IAAI,QAAQ,KAAS,IAAA,OAAA,CAAQ,MAAM,QAAS,CAAA,OAAA,CAAQ,KAAK,CAAG,EAAA;AAC1D,YAAA,OAAA,CAAQ,QAAQ,OAAQ,CAAA,KAAA,CAAA;AAAA,WAC1B;AAAA,SACF;AAAA,OACF;AAAA,KACF,CAAA;AAEA,IAAM,MAAA,gBAAA,GAAmB,CAAC,KAAA,EAAsB,IAAqB,KAAA;AACnE,MAAA,aAAA,CAAc,MAAM,IAAQ,CAAA,GAAA,IAAA,CAAA;AAC5B,MAAA,IAAI,SAAS,KAAO,EAAA;AAClB,QAAA,QAAA,CAAS,QAAQ,OAAQ,CAAA,KAAA,CAAA;AACzB,QAAA,oBAAA,CAAqB,KAAQ,GAAA,KAAA,CAAA;AAAA,OACxB,MAAA;AACL,QAAA,SAAA,CAAU,QAAQ,OAAQ,CAAA,KAAA,CAAA;AAC1B,QAAA,oBAAA,CAAqB,KAAQ,GAAA,KAAA,CAAA;AAAA,OAC/B;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,iBAAoB,GAAA,CAAC,KAAc,EAAA,OAAA,EAAkB,KAAmB,KAAA;AAC5E,MAAA,IAAI,cAAc,KAAM,CAAA,GAAA;AAAK,QAAA,OAAA;AAC7B,MAAA,IAAI,KAAO,EAAA;AACT,QAAA,QAAA,CAAS,KAAQ,GAAA,KAAA,CAAA;AACjB,QAAA,OAAA,CAAQ,QAAS,CAAQ,OAAA,CAAA,KAAA,IAAS,SAAS,KACxC,EAAA,IAAA,CAAK,MAAM,IAAK,EAAC,CACjB,CAAA,MAAA,CAAO,MAAM,MAAO,EAAC,EACrB,MAAO,CAAA,KAAA,CAAM,QAAQ,CAAA,CAAA;AAAA,OAC1B;AAEA,MAAA,IAAI,CAAC,KAAO,EAAA;AACV,QAAA,oBAAA,CAAqB,KAAQ,GAAA,OAAA,CAAA;AAAA,OAC/B;AAEA,MAAI,IAAA,CAAC,QAAQ,KAAS,IAAA,OAAA,CAAQ,MAAM,QAAS,CAAA,OAAA,CAAQ,KAAK,CAAG,EAAA;AAC3D,QAAA,OAAA,CAAQ,QAAQ,OAAQ,CAAA,KAAA,CAAA;AACxB,QAAA,SAAA,CAAU,KAAQ,GAAA,KAAA,CAAA;AAAA,OACpB;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,iBAAoB,GAAA,CACxB,KACA,EAAA,OAAA,EACA,KACG,KAAA;AACH,MAAA,IAAI,cAAc,KAAM,CAAA,GAAA;AAAK,QAAA,OAAA;AAC7B,MAAA,IAAI,KAAO,EAAA;AACT,QAAA,SAAA,CAAU,KAAQ,GAAA,KAAA,CAAA;AAClB,QAAA,OAAA,CAAQ,QAAS,CAAQ,OAAA,CAAA,KAAA,IAAS,UAAU,KACzC,EAAA,IAAA,CAAK,MAAM,IAAK,EAAC,CACjB,CAAA,MAAA,CAAO,MAAM,MAAO,EAAC,EACrB,MAAO,CAAA,KAAA,CAAM,QAAQ,CAAA,CAAA;AAAA,OAC1B;AAEA,MAAA,IAAI,CAAC,KAAO,EAAA;AACV,QAAA,oBAAA,CAAqB,KAAQ,GAAA,OAAA,CAAA;AAAA,OAC/B;AAEA,MAAA,IAAI,QAAQ,KAAS,IAAA,OAAA,CAAQ,MAAM,QAAS,CAAA,OAAA,CAAQ,KAAK,CAAG,EAAA;AAC1D,QAAA,OAAA,CAAQ,QAAQ,OAAQ,CAAA,KAAA,CAAA;AAAA,OAC1B;AAAA,KACF,CAAA;AAEA,IAAA,MAAM,cAAc,MAAM;AACxB,MAAA,QAAA,CAAS,KAAQ,GAAAM,uBAAA,CAAgBC,SAAM,CAAA,YAAY,CAAG,EAAA;AAAA,QACpD,IAAA,EAAMA,UAAM,IAAI,CAAA;AAAA,QAChB,IAAM,EAAA,OAAA;AAAA,QACN,cAAc,KAAM,CAAA,YAAA;AAAA,OACrB,CAAE,CAAA,CAAA,CAAA,CAAA;AACH,MAAA,SAAA,CAAU,KAAQ,GAAA,QAAA,CAAS,KAAM,CAAA,GAAA,CAAI,GAAG,OAAO,CAAA,CAAA;AAC/C,MAAA,IAAA,CAAK,QAAQ,IAAI,CAAA,CAAA;AAAA,KACnB,CAAA;AAEA,IAAM,MAAA,cAAA,GAAiB,CAAC,KAA2B,KAAA;AACjD,MAAA,OAAOC,cAAQ,CAAA,KAAK,CAChB,GAAA,KAAA,CAAM,IAAI,CAAC,CAAA,KAAM,CAAE,CAAA,MAAA,CAAO,MAAM,CAAC,CACjC,GAAA,KAAA,CAAM,OAAO,MAAM,CAAA,CAAA;AAAA,KACzB,CAAA;AAEA,IAAM,MAAA,cAAA,GAAiB,CAAC,KAA2B,KAAA;AACjD,MAAO,OAAAA,cAAA,CAAQ,KAAK,CAChB,GAAA,KAAA,CAAM,IAAI,CAAC,CAAA,KAAMR,yBAAM,CAAA,CAAA,EAAG,MAAM,CAAA,CAAE,OAAO,IAAK,CAAA,KAAK,CAAC,CACpD,GAAAA,yBAAA,CAAM,OAAO,MAAM,CAAA,CAAE,MAAO,CAAA,IAAA,CAAK,KAAK,CAAA,CAAA;AAAA,KAC5C,CAAA;AAEA,IAAA,SAAA,oBAAA,CACE,UACA,QACA,EAAA;AACA,MAAI,IAAA,KAAA,CAAM,gBAAgB,QAAS,EAAA;AACjC,QAAM,MAAA,WAAA,GAAc,CAAS,QAAA,IAAA,IAAU,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,IAAA,EAAA,KAAA,CAAA,CAAA;AACvC,QAAM,MAAA,YAAA,GAAe,CAAS,QAAA,IAAA,IAAW,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,KAAA,EAAA,KAAA,CAAA,CAAA;AACzC,QAAM,MAAA,WAAA,GAAc,SAAQ,IAAK,EAAA,CAAA;AACjC,QAAM,MAAA,YAAA,GAAe,SAAQ,KAAM,EAAA,CAAA;AACnC,QAAU,SAAA,CAAA,KAAA,GACR,gBAAgB,WAAe,IAAA,YAAA,KAAiB,eAC5C,QAAQ,CAAA,GAAA,CAAI,CAAG,EAAA,IAAI,CACnB,GAAA,QAAA,CAAA;AAAA,OACD,MAAA;AACL,QAAA,SAAA,CAAU,KAAQ,GAAA,QAAA,CAAS,KAAM,CAAA,GAAA,CAAI,GAAG,IAAI,CAAA,CAAA;AAC5C,QAAA,IAAI,QAAS,EAAA;AACX,UAAA,SAAA,CAAU,QAAQ,SAAU,CAAA,KAAA,CACzB,IAAK,CAAA,QAAA,CAAQ,MAAM,CAAA,CACnB,MAAO,CAAA,QAAA,CAAQ,QAAQ,CAAA,CACvB,MAAO,CAAA,QAAA,CAAQ,QAAQ,CAAA,CAAA;AAAA,SAC5B;AAAA,OACF;AAAA,KACF;AAEA,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,cAAgB,EAAA,YAAY,CAAC,CAAA,CAAA;AACxD,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,gBAAkB,EAAA,cAAc,CAAC,CAAA,CAAA;AAC5D,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,gBAAkB,EAAA,cAAc,CAAC,CAAA,CAAA;AAC5D,IAAA,IAAA,CAAK,mBAAqB,EAAA,CAAC,aAAe,EAAA,WAAW,CAAC,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}