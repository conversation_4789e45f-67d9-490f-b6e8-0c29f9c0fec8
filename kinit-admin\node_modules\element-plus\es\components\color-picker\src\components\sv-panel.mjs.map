{"version": 3, "file": "sv-panel.mjs", "sources": ["../../../../../../../packages/components/color-picker/src/components/sv-panel.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"ns.b()\"\n    :style=\"{\n      backgroundColor: background,\n    }\"\n  >\n    <div :class=\"ns.e('white')\" />\n    <div :class=\"ns.e('black')\" />\n    <div\n      :class=\"ns.e('cursor')\"\n      :style=\"{\n        top: cursorTop + 'px',\n        left: cursorLeft + 'px',\n      }\"\n    >\n      <div />\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  onMounted,\n  ref,\n  watch,\n} from 'vue'\nimport { getClientXY } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { draggable } from '../utils/draggable'\n\nimport type { PropType } from 'vue'\nimport type Color from '../utils/color'\n\nexport default defineComponent({\n  name: 'ElSlPanel',\n\n  props: {\n    color: {\n      type: Object as PropType<Color>,\n      required: true,\n    },\n  },\n\n  setup(props) {\n    const ns = useNamespace('color-svpanel')\n\n    // instance\n    const instance = getCurrentInstance()!\n\n    // data\n    const cursorTop = ref(0)\n    const cursorLeft = ref(0)\n    const background = ref('hsl(0, 100%, 50%)')\n    const colorValue = computed(() => {\n      const hue = props.color.get('hue')\n      const value = props.color.get('value')\n      return { hue, value }\n    })\n\n    // methods\n    function update() {\n      const saturation = props.color.get('saturation')\n      const value = props.color.get('value')\n\n      const el = instance.vnode.el!\n      const { clientWidth: width, clientHeight: height } = el\n\n      cursorLeft.value = (saturation * width) / 100\n      cursorTop.value = ((100 - value) * height) / 100\n\n      background.value = `hsl(${props.color.get('hue')}, 100%, 50%)`\n    }\n\n    function handleDrag(event: MouseEvent | TouchEvent) {\n      const el = instance.vnode.el!\n      const rect = el.getBoundingClientRect()\n      const { clientX, clientY } = getClientXY(event)\n\n      let left = clientX - rect.left\n      let top = clientY - rect.top\n      left = Math.max(0, left)\n      left = Math.min(left, rect.width)\n\n      top = Math.max(0, top)\n      top = Math.min(top, rect.height)\n\n      cursorLeft.value = left\n      cursorTop.value = top\n      props.color.set({\n        saturation: (left / rect.width) * 100,\n        value: 100 - (top / rect.height) * 100,\n      })\n    }\n\n    // watch\n    watch(\n      () => colorValue.value,\n      () => {\n        update()\n      }\n    )\n    // mounted\n    onMounted(() => {\n      draggable(instance.vnode.el as HTMLElement, {\n        drag: (event) => {\n          handleDrag(event)\n        },\n        end: (event) => {\n          handleDrag(event)\n        },\n      })\n\n      update()\n    })\n    return {\n      cursorTop,\n      cursorLeft,\n      background,\n      colorValue,\n      handleDrag,\n      update,\n      ns,\n    }\n  },\n})\n</script>\n"], "names": ["_createElementVNode", "_createElementBlock", "_normalizeClass", "_normalizeStyle"], "mappings": ";;;;;;;;AAqCA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,WAAA;AAAA,EAEN,KAAO,EAAA;AAAA,IACL,KAAO,EAAA;AAAA,MACL,IAAM,EAAA,MAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,GACF;AAAA,EAEA,MAAM,KAAO,EAAA;AACX,IAAM,MAAA,EAAA,GAAK,aAAa,eAAe,CAAA,CAAA;AAGvC,IAAA,MAAM,WAAW,kBAAmB,EAAA,CAAA;AAGpC,IAAM,MAAA,SAAA,GAAY,IAAI,CAAC,CAAA,CAAA;AACvB,IAAM,MAAA,UAAA,GAAa,IAAI,CAAC,CAAA,CAAA;AACxB,IAAM,MAAA,UAAA,GAAa,IAAI,mBAAmB,CAAA,CAAA;AAC1C,IAAM,MAAA,UAAA,GAAa,SAAS,MAAM;AAChC,MAAA,MAAM,GAAM,GAAA,KAAA,CAAM,KAAM,CAAA,GAAA,CAAI,KAAK,CAAA,CAAA;AACjC,MAAA,MAAM,KAAQ,GAAA,KAAA,CAAM,KAAM,CAAA,GAAA,CAAI,OAAO,CAAA,CAAA;AACrC,MAAO,OAAA,EAAE,KAAK,KAAM,EAAA,CAAA;AAAA,KACrB,CAAA,CAAA;AAGD,IAAkB,SAAA,MAAA,GAAA;AAChB,MAAA,MAAM,UAAa,GAAA,KAAA,CAAM,KAAM,CAAA,GAAA,CAAI,YAAY,CAAA,CAAA;AAC/C,MAAA,MAAM,KAAQ,GAAA,KAAA,CAAM,KAAM,CAAA,GAAA,CAAI,OAAO,CAAA,CAAA;AAErC,MAAM,MAAA,EAAA,GAAK,SAAS,KAAM,CAAA,EAAA,CAAA;AAC1B,MAAA,MAAM,EAAE,WAAA,EAAa,KAAO,EAAA,YAAA,EAAc,MAAW,EAAA,GAAA,EAAA,CAAA;AAErD,MAAW,UAAA,CAAA,KAAA,GAAS,aAAa,KAAS,GAAA,GAAA,CAAA;AAC1C,MAAU,SAAA,CAAA,KAAA,GAAU,CAAM,GAAA,GAAA,KAAA,IAAS,MAAU,GAAA,GAAA,CAAA;AAE7C,MAAA,UAAA,CAAW,KAAQ,GAAA,CAAA,IAAA,EAAO,KAAM,CAAA,KAAA,CAAM,IAAI,KAAK,CAAA,CAAA,YAAA,CAAA,CAAA;AAAA,KACjD;AAEA,IAAA,SAAA,UAAA,CAAoB,KAAgC,EAAA;AAClD,MAAM,MAAA,EAAA,GAAK,SAAS,KAAM,CAAA,EAAA,CAAA;AAC1B,MAAM,MAAA,IAAA,GAAO,GAAG,qBAAsB,EAAA,CAAA;AACtC,MAAA,MAAM,EAAE,OAAA,EAAS,OAAY,EAAA,GAAA,WAAA,CAAY,KAAK,CAAA,CAAA;AAE9C,MAAI,IAAA,IAAA,GAAO,UAAU,IAAK,CAAA,IAAA,CAAA;AAC1B,MAAI,IAAA,GAAA,GAAM,UAAU,IAAK,CAAA,GAAA,CAAA;AACzB,MAAO,IAAA,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,EAAG,IAAI,CAAA,CAAA;AACvB,MAAA,IAAA,GAAO,IAAK,CAAA,GAAA,CAAI,IAAM,EAAA,IAAA,CAAK,KAAK,CAAA,CAAA;AAEhC,MAAM,GAAA,GAAA,IAAA,CAAK,GAAI,CAAA,CAAA,EAAG,GAAG,CAAA,CAAA;AACrB,MAAA,GAAA,GAAM,IAAK,CAAA,GAAA,CAAI,GAAK,EAAA,IAAA,CAAK,MAAM,CAAA,CAAA;AAE/B,MAAA,UAAA,CAAW,KAAQ,GAAA,IAAA,CAAA;AACnB,MAAA,SAAA,CAAU,KAAQ,GAAA,GAAA,CAAA;AAClB,MAAA,KAAA,CAAM,MAAM,GAAI,CAAA;AAAA,QACd,UAAA,EAAa,IAAO,GAAA,IAAA,CAAK,KAAS,GAAA,GAAA;AAAA,QAClC,KAAO,EAAA,GAAA,GAAO,GAAM,GAAA,IAAA,CAAK,MAAU,GAAA,GAAA;AAAA,OACpC,CAAA,CAAA;AAAA,KACH;AAGA,IACE,KAAA,CAAA,MAAM,UAAW,CAAA,KAAA,EACjB,MAAM;AACJ,MAAO,MAAA,EAAA,CAAA;AAAA,KAEX,CAAA,CAAA;AAEA,IAAA,SAAA,CAAU,MAAM;AACd,MAAU,SAAA,CAAA,QAAA,CAAS,MAAM,EAAmB,EAAA;AAAA,QAC1C,IAAA,EAAM,CAAC,KAAU,KAAA;AACf,UAAA,UAAA,CAAW,KAAK,CAAA,CAAA;AAAA,SAClB;AAAA,QACA,GAAA,EAAK,CAAC,KAAU,KAAA;AACd,UAAA,UAAA,CAAW,KAAK,CAAA,CAAA;AAAA,SAClB;AAAA,OACD,CAAA,CAAA;AAED,MAAO,MAAA,EAAA,CAAA;AAAA,KACR,CAAA,CAAA;AACD,IAAO,OAAA;AAAA,MACL,SAAA;AAAA,MACA,UAAA;AAAA,MACA,UAAA;AAAA,MACA,UAAA;AAAA,MACA,UAAA;AAAA,MACA,MAAA;AAAA,MACA,EAAA;AAAA,KACF,CAAA;AAAA,GACF;AACF,CAAC,CAAA,CAAA;mCAhHYA,kBAAA,CAAA,KAAA,EAAA,IAAA,EAAA,IAAA,EAAA,CAAA,CAAA,CAAA,CAAA;;AAAP,EAAA,UAAA;;;sBAfJC,kBAiBM,CAAA,KAAA,EAAA;AAAA,IAhBH,KAAA,EAAKC,cAAE,CAAA,IAAA,CAAA,EAAA,CAAG,CAAC,EAAA,CAAA;AAAA,IACX,KAAK,EAAAC,cAAA,CAAA;AAAA,MAA2B,eAAA,EAAA,IAAA,CAAA,UAAA;AAAA,KAAA,CAAA;;IAIjCH,kBAA8B,CAAA,KAAA,EAAA;AAAA,MAAxB,KAAA,EAAKE,cAAE,CAAA,IAAA,CAAA,EAAA,CAAG,CAAC,CAAA,OAAA,CAAA,CAAA;AAAA,KAAA,EAAA,IAAA,EAAA,CAAA,CAAA;IACjBF,kBAA8B,CAAA,KAAA,EAAA;AAAA,MAAxB,KAAA,EAAKE,cAAE,CAAA,IAAA,CAAA,EAAA,CAAG,CAAC,CAAA,OAAA,CAAA,CAAA;AAAA,KAAA,EAAA,IAAA,EAAA,CAAA,CAAA;IACjBF,kBAQM,CAAA,KAAA,EAAA;AAAA,MAPH,KAAA,EAAKE,cAAE,CAAA,IAAA,CAAA,EAAA,CAAG,CAAC,CAAA,QAAA,CAAA,CAAA;AAAA,MACX,KAAK,EAAAC,cAAA,CAAA;AAAA,QAAA,GAAA,EAAiB,IAAS,CAAA,SAAA,GAAA,IAAA;AAAA,QAAA,IAAA,EAAuB,IAAU,CAAA,UAAA,GAAA,IAAA;AAAA,OAAA,CAAA;;;;;;;;"}