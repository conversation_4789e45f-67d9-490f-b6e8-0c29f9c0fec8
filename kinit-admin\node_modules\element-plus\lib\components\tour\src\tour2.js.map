{"version": 3, "file": "tour2.js", "sources": ["../../../../../../packages/components/tour/src/tour.vue"], "sourcesContent": ["<template>\n  <teleport :to=\"appendTo\">\n    <div :class=\"kls\" v-bind=\"$attrs\">\n      <el-tour-mask\n        :visible=\"mergedShowMask\"\n        :fill=\"mergedMaskStyle?.color\"\n        :style=\"mergedMaskStyle?.style\"\n        :pos=\"pos\"\n        :z-index=\"mergedZIndex\"\n        :target-area-clickable=\"targetAreaClickable\"\n      />\n      <el-tour-content\n        v-if=\"modelValue\"\n        :key=\"current\"\n        :reference=\"triggerTarget\"\n        :placement=\"mergedPlacement\"\n        :show-arrow=\"mergedShowArrow\"\n        :z-index=\"mergedZIndex\"\n        :style=\"mergedContentStyle\"\n        @close=\"onEscClose\"\n      >\n        <el-tour-steps :current=\"current\" @update-total=\"onUpdateTotal\">\n          <slot />\n        </el-tour-steps>\n      </el-tour-content>\n    </div>\n  </teleport>\n  <!-- just for IDE -->\n  <slot v-if=\"false\" name=\"indicators\" :current=\"current + 1\" :total=\"total\" />\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, provide, ref, toRef, useSlots, watch } from 'vue'\nimport { useVModel } from '@vueuse/core'\nimport { useNamespace, useZIndex } from '@element-plus/hooks'\nimport { isBoolean } from '@element-plus/utils'\nimport ElTourMask from './mask.vue'\nimport ElTourContent from './content.vue'\nimport ElTourSteps from './steps'\nimport { tourEmits, tourProps } from './tour'\nimport { tourKey, useTarget } from './helper'\nimport type { TourStepProps } from './step'\n\ndefineOptions({\n  name: 'ElTour',\n})\n\nconst props = defineProps(tourProps)\nconst emit = defineEmits(tourEmits)\n\nconst ns = useNamespace('tour')\nconst total = ref(0)\nconst currentStep = ref<TourStepProps>()\n\nconst current = useVModel(props, 'current', emit, {\n  passive: true,\n})\n\nconst currentTarget = computed(() => currentStep.value?.target)\n\nconst kls = computed(() => [\n  ns.b(),\n  mergedType.value === 'primary' ? ns.m('primary') : '',\n])\n\nconst mergedPlacement = computed(\n  () => currentStep.value?.placement || props.placement\n)\n\nconst mergedContentStyle = computed(\n  () => currentStep.value?.contentStyle ?? props.contentStyle\n)\n\nconst mergedMask = computed(() => currentStep.value?.mask ?? props.mask)\nconst mergedShowMask = computed(() => !!mergedMask.value && props.modelValue)\nconst mergedMaskStyle = computed(() =>\n  isBoolean(mergedMask.value) ? undefined : mergedMask.value\n)\n\nconst mergedShowArrow = computed(\n  () =>\n    !!currentTarget.value && (currentStep.value?.showArrow ?? props.showArrow)\n)\n\nconst mergedScrollIntoViewOptions = computed(\n  () => currentStep.value?.scrollIntoViewOptions ?? props.scrollIntoViewOptions\n)\nconst mergedType = computed(() => currentStep.value?.type ?? props.type)\n\nconst { nextZIndex } = useZIndex()\nconst nowZIndex = nextZIndex()\nconst mergedZIndex = computed(() => props.zIndex ?? nowZIndex)\n\nconst { mergedPosInfo: pos, triggerTarget } = useTarget(\n  currentTarget,\n  toRef(props, 'modelValue'),\n  toRef(props, 'gap'),\n  mergedMask,\n  mergedScrollIntoViewOptions\n)\n\nwatch(\n  () => props.modelValue,\n  (val) => {\n    if (!val) {\n      current.value = 0\n    }\n  }\n)\n\nconst onEscClose = () => {\n  if (props.closeOnPressEscape) {\n    emit('update:modelValue', false)\n    emit('close', current.value)\n  }\n}\n\nconst onUpdateTotal = (val: number) => {\n  total.value = val\n}\n\nconst slots = useSlots()\n\nprovide(tourKey, {\n  currentStep,\n  current,\n  total,\n  showClose: toRef(props, 'showClose'),\n  closeIcon: toRef(props, 'closeIcon') as any,\n  mergedType: mergedType as any,\n  ns,\n  slots,\n  updateModelValue(modelValue) {\n    emit('update:modelValue', modelValue)\n  },\n  onClose() {\n    emit('close', current.value)\n  },\n  onFinish() {\n    emit('finish')\n  },\n  onChange() {\n    emit('change', current.value)\n  },\n})\n</script>\n"], "names": ["useNamespace", "ref", "useVModel", "computed", "isBoolean", "useZIndex", "useTarget", "toRef", "watch"], "mappings": ";;;;;;;;;;;;;;;;;;uCA2Cc,CAAA;AAAA,EACZ,IAAM,EAAA,QAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAA,GAAKA,mBAAa,MAAM,CAAA,CAAA;AAC9B,IAAM,MAAA,KAAA,GAAQC,QAAI,CAAC,CAAA,CAAA;AACnB,IAAA,MAAM,cAAcA,OAAmB,EAAA,CAAA;AAEvC,IAAA,MAAM,OAAU,GAAAC,cAAA,CAAU,KAAO,EAAA,SAAA,EAAW,IAAM,EAAA;AAAA,MAChD,OAAS,EAAA,IAAA;AAAA,KACV,CAAA,CAAA;AAED,IAAA,MAAM,aAAgB,GAAAC,YAAA,CAAS,MAAM;AAErC,MAAM,IAAA,EAAA,CAAA;AAAqB,MACzB,OAAK,CAAA,EAAA,GAAA,WAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,CAAA;AAAA,KAAA,CACL;AAAmD,IACrD,MAAC,GAAA,GAAAA,YAAA,CAAA,MAAA;AAED,MAAA,EAAA,CAAA,CAAM;AAIN,MAAA,8BACE,GAAA,EAAA,CAAA,CAAA,CAAA,eAAyB;AAG3B,KAAA,CAAA,CAAA;AACA,IAAM,MAAA,eAAA,eAA0B,CAAM,MAAC;AACvC,MAAM,IAAA,EAAA,CAAA;AAIN,MAAM,OAAA,CAAA,CAAA,EAAA,GAAA,WACJ,CAAA,KAAA,KAAA,IACkB,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAsB,KAAA,KAAA,CAAA,SAAoB,CAAA;AAG9D,KAAA,CAAA,CAAA;AAGA,IAAA,MAAM,kBAAsB,GAAAA;AAE5B,MAAM,IAAA,EAAE;AACR,MAAA,gBAAkB,GAAW,WAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA,YAAA,CAAA;AAC7B,KAAA,CAAA,CAAA;AAEA,IAAA,MAAM,UAAE,GAAAA,YAAoB,CAAA,MAAA;AAQ5B,MAAA,IACE,EAAM,EAAA,EAAA,CAAA;AAEJ,MAAA,OAAU,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,WAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA,IAAA,CAAA;AACR,KAAA,CAAA,CAAA;AAAgB,IAClB,MAAA,cAAA,GAAAA,YAAA,CAAA,MAAA,CAAA,CAAA,UAAA,CAAA,KAAA,IAAA,KAAA,CAAA,UAAA,CAAA,CAAA;AAAA,IACF,MACF,eAAA,GAAAA,YAAA,CAAA,MAAAC,eAAA,CAAA,UAAA,CAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AAEA,IAAA,MAAM,eAAmB,GAAAD,YAAA,CAAA,MAAA;AACvB,MAAA,IAAI,MAAM,CAAoB;AAC5B,MAAA,OAAK,qBAAqB,KAAK,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,WAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,SAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AAC/B,KAAK,CAAA,CAAA;AAAsB,IAC7B,MAAA,2BAAA,GAAAA,YAAA,CAAA,MAAA;AAAA,MACF,IAAA,EAAA,EAAA,EAAA,CAAA;AAEA,MAAM,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,WAAiC,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,qBAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA,qBAAA,CAAA;AACrC,KAAA,CAAA,CAAA;AAAc,IAChB,MAAA,UAAA,GAAAA,YAAA,CAAA,MAAA;AAEA,MAAA,IAAM;AAEN,MAAA,OAAiB,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,WAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA,IAAA,CAAA;AAAA,KACf,CAAA,CAAA;AAAA,IACA,MAAA,EAAA,UAAA,EAAA,GAAAE,iBAAA,EAAA,CAAA;AAAA,IACA,MAAA,SAAA,GAAA,UAAA,EAAA,CAAA;AAAA,IACA,MAAA,YAAiB,GAAAF,YAAkB,CAAA,MAAA;AAAA,MACnC,IAAA,EAAA,CAAA;AAAmC,MACnC,OAAA,CAAA,EAAA,GAAA,KAAA,CAAA,MAAA,KAAA,IAAA,GAAA,EAAA,GAAA,SAAA,CAAA;AAAA,KACA,CAAA,CAAA;AAAA,IACA,MAAA,EAAA,aAAA,EAAA,GAAA,EAAA,aAAA,EAAA,GAAAG,gBAAA,CAAA,aAAA,EAAAC,SAAA,CAAA,KAAA,EAAA,YAAA,CAAA,EAAAA,SAAA,CAAA,KAAA,EAAA,KAAA,CAAA,EAAA,UAAA,EAAA,2BAAA,CAAA,CAAA;AAAA,IAAAC,gCAC6B,EAAA,CAAA,GAAA,KAAA;AAC3B,MAAA,IAAA,CAAA;AAAoC,QACtC,OAAA,CAAA,KAAA,GAAA,CAAA,CAAA;AAAA,OACU;AACR,KAAK,CAAA,CAAA;AAAsB,IAC7B,MAAA,UAAA,GAAA,MAAA;AAAA,MACA,IAAW,KAAA,CAAA,kBAAA,EAAA;AACT,QAAA,IAAA,CAAK,mBAAQ,EAAA,KAAA,CAAA,CAAA;AAAA,QACf,IAAA,CAAA,OAAA,EAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AAAA,OACW;AACT,KAAK,CAAA;AAAuB,IAC9B,MAAA,aAAA,GAAA,CAAA,GAAA,KAAA;AAAA,MACD,KAAA,CAAA,KAAA,GAAA,GAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}