{"version": 3, "file": "thumb2.mjs", "sources": ["../../../../../../packages/components/scrollbar/src/thumb.vue"], "sourcesContent": ["<template>\n  <transition :name=\"ns.b('fade')\">\n    <div\n      v-show=\"always || visible\"\n      ref=\"instance\"\n      :class=\"[ns.e('bar'), ns.is(bar.key)]\"\n      @mousedown=\"clickTrackHandler\"\n    >\n      <div\n        ref=\"thumb\"\n        :class=\"ns.e('thumb')\"\n        :style=\"thumbStyle\"\n        @mousedown=\"clickThumbHandler\"\n      />\n    </div>\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, onBeforeUnmount, ref, toRef } from 'vue'\nimport { useEventListener } from '@vueuse/core'\nimport { isClient, throwError } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { scrollbarContextKey } from './constants'\nimport { BAR_MAP, renderThumbStyle } from './util'\nimport { thumbProps } from './thumb'\n\nconst COMPONENT_NAME = 'Thumb'\nconst props = defineProps(thumbProps)\n\nconst scrollbar = inject(scrollbarContextKey)\nconst ns = useNamespace('scrollbar')\n\nif (!scrollbar) throwError(COMPONENT_NAME, 'can not inject scrollbar context')\n\nconst instance = ref<HTMLDivElement>()\nconst thumb = ref<HTMLDivElement>()\n\nconst thumbState = ref<Partial<Record<'X' | 'Y', number>>>({})\nconst visible = ref(false)\n\nlet cursorDown = false\nlet cursorLeave = false\nlet originalOnSelectStart:\n  | ((this: GlobalEventHandlers, ev: Event) => any)\n  | null = isClient ? document.onselectstart : null\n\nconst bar = computed(() => BAR_MAP[props.vertical ? 'vertical' : 'horizontal'])\n\nconst thumbStyle = computed(() =>\n  renderThumbStyle({\n    size: props.size,\n    move: props.move,\n    bar: bar.value,\n  })\n)\n\nconst offsetRatio = computed(\n  () =>\n    // offsetRatioX = original width of thumb / current width of thumb / ratioX\n    // offsetRatioY = original height of thumb / current height of thumb / ratioY\n    // instance height = wrap height - GAP\n    instance.value![bar.value.offset] ** 2 /\n    scrollbar.wrapElement![bar.value.scrollSize] /\n    props.ratio /\n    thumb.value![bar.value.offset]\n)\n\nconst clickThumbHandler = (e: MouseEvent) => {\n  // prevent click event of middle and right button\n  e.stopPropagation()\n  if (e.ctrlKey || [1, 2].includes(e.button)) return\n\n  window.getSelection()?.removeAllRanges()\n  startDrag(e)\n\n  const el = e.currentTarget as HTMLDivElement\n  if (!el) return\n  thumbState.value[bar.value.axis] =\n    el[bar.value.offset] -\n    (e[bar.value.client] - el.getBoundingClientRect()[bar.value.direction])\n}\n\nconst clickTrackHandler = (e: MouseEvent) => {\n  if (!thumb.value || !instance.value || !scrollbar.wrapElement) return\n\n  const offset = Math.abs(\n    (e.target as HTMLElement).getBoundingClientRect()[bar.value.direction] -\n      e[bar.value.client]\n  )\n  const thumbHalf = thumb.value[bar.value.offset] / 2\n  const thumbPositionPercentage =\n    ((offset - thumbHalf) * 100 * offsetRatio.value) /\n    instance.value[bar.value.offset]\n\n  scrollbar.wrapElement[bar.value.scroll] =\n    (thumbPositionPercentage * scrollbar.wrapElement[bar.value.scrollSize]) /\n    100\n}\n\nconst startDrag = (e: MouseEvent) => {\n  e.stopImmediatePropagation()\n  cursorDown = true\n  document.addEventListener('mousemove', mouseMoveDocumentHandler)\n  document.addEventListener('mouseup', mouseUpDocumentHandler)\n  originalOnSelectStart = document.onselectstart\n  document.onselectstart = () => false\n}\n\nconst mouseMoveDocumentHandler = (e: MouseEvent) => {\n  if (!instance.value || !thumb.value) return\n  if (cursorDown === false) return\n\n  const prevPage = thumbState.value[bar.value.axis]\n  if (!prevPage) return\n\n  const offset =\n    (instance.value.getBoundingClientRect()[bar.value.direction] -\n      e[bar.value.client]) *\n    -1\n  const thumbClickPosition = thumb.value[bar.value.offset] - prevPage\n  const thumbPositionPercentage =\n    ((offset - thumbClickPosition) * 100 * offsetRatio.value) /\n    instance.value[bar.value.offset]\n  scrollbar.wrapElement[bar.value.scroll] =\n    (thumbPositionPercentage * scrollbar.wrapElement[bar.value.scrollSize]) /\n    100\n}\n\nconst mouseUpDocumentHandler = () => {\n  cursorDown = false\n  thumbState.value[bar.value.axis] = 0\n  document.removeEventListener('mousemove', mouseMoveDocumentHandler)\n  document.removeEventListener('mouseup', mouseUpDocumentHandler)\n  restoreOnselectstart()\n  if (cursorLeave) visible.value = false\n}\n\nconst mouseMoveScrollbarHandler = () => {\n  cursorLeave = false\n  visible.value = !!props.size\n}\n\nconst mouseLeaveScrollbarHandler = () => {\n  cursorLeave = true\n  visible.value = cursorDown\n}\n\nonBeforeUnmount(() => {\n  restoreOnselectstart()\n  document.removeEventListener('mouseup', mouseUpDocumentHandler)\n})\n\nconst restoreOnselectstart = () => {\n  if (document.onselectstart !== originalOnSelectStart)\n    document.onselectstart = originalOnSelectStart\n}\n\nuseEventListener(\n  toRef(scrollbar, 'scrollbarElement'),\n  'mousemove',\n  mouseMoveScrollbarHandler\n)\nuseEventListener(\n  toRef(scrollbar, 'scrollbarElement'),\n  'mouseleave',\n  mouseLeaveScrollbarHandler\n)\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AA8BA,IAAM,MAAA,SAAA,GAAY,OAAO,mBAAmB,CAAA,CAAA;AAC5C,IAAM,MAAA,EAAA,GAAK,aAAa,WAAW,CAAA,CAAA;AAEnC,IAAA,IAAI,CAAC,SAAA;AAAW,MAAA,UAAA,CAAW,gBAAgB,kCAAkC,CAAA,CAAA;AAE7E,IAAA,MAAM,WAAW,GAAoB,EAAA,CAAA;AACrC,IAAA,MAAM,QAAQ,GAAoB,EAAA,CAAA;AAElC,IAAM,MAAA,UAAA,GAAa,GAAwC,CAAA,EAAE,CAAA,CAAA;AAC7D,IAAM,MAAA,OAAA,GAAU,IAAI,KAAK,CAAA,CAAA;AAEzB,IAAA,IAAI,UAAa,GAAA,KAAA,CAAA;AACjB,IAAA,IAAI,WAAc,GAAA,KAAA,CAAA;AAClB,IAAI,IAAA,qBAAA,GAEO,QAAW,GAAA,QAAA,CAAS,aAAgB,GAAA,IAAA,CAAA;AAE/C,IAAA,MAAM,MAAM,QAAS,CAAA,MAAM,QAAQ,KAAM,CAAA,QAAA,GAAW,aAAa,YAAa,CAAA,CAAA,CAAA;AAE9E,IAAM,MAAA,UAAA,GAAa,QAAS,CAAA,MAC1B,gBAAiB,CAAA;AAAA,MACf,MAAM,KAAM,CAAA,IAAA;AAAA,MACZ,MAAM,KAAM,CAAA,IAAA;AAAA,MACZ,KAAK,GAAI,CAAA,KAAA;AAAA,KACV,CACH,CAAA,CAAA;AAEA,IAAM,MAAA,WAAA,GAAc,SAClB,MAIE,QAAA,CAAS,MAAO,GAAI,CAAA,KAAA,CAAM,WAAW,CACrC,GAAA,SAAA,CAAU,YAAa,GAAI,CAAA,KAAA,CAAM,cACjC,KAAM,CAAA,KAAA,GACN,MAAM,KAAO,CAAA,GAAA,CAAI,MAAM,MAC3B,CAAA,CAAA,CAAA;AAEA,IAAM,MAAA,iBAAA,GAAoB,CAAC,CAAkB,KAAA;AAE3C,MAAA,IAAkB,EAAA,CAAA;AAClB,MAAI,CAAA,CAAA,eAAa,EAAC,CAAA;AAA0B,MAAA,IAAA,CAAA,CAAA,OAAA,IAAA,CAAA,CAAA,EAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA,CAAA,MAAA,CAAA;AAE5C,QAAO,OAAA;AACP,MAAA,CAAA,EAAA,GAAA,MAAW,CAAA,YAAA,EAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,eAAA,EAAA,CAAA;AAEX,MAAA,WAAW,CAAE,CAAA;AACb,MAAA,MAAK,EAAA,GAAA,CAAA,CAAA,aAAA,CAAA;AAAI,MAAA,IAAA,CAAA,EAAA;AACT,QAAA,OAAA;AAE8D,MAChE,UAAA,CAAA,KAAA,CAAA,GAAA,CAAA,KAAA,CAAA,IAAA,CAAA,GAAA,EAAA,CAAA,GAAA,CAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,GAAA,CAAA,KAAA,CAAA,MAAA,CAAA,GAAA,EAAA,CAAA,qBAAA,EAAA,CAAA,GAAA,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAI,iBAAiB,GAAS,CAAA,CAAA,KAAA;AAAiC,MAAA,IAAA,CAAA,KAAA,CAAA,KAAA,IAAA,CAAA,QAAA,CAAA,KAAA,IAAA,CAAA,SAAA,CAAA,WAAA;AAE/D,QAAA,OAAe;AAIf,MAAA,MAAM,MAAY,GAAA,IAAA,CAAA,GAAA,CAAM,CAAM,CAAA,MAAA,CAAA,qBAAoB,EAAA,CAAA,GAAA,CAAA,KAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAClD,MAAM,MAAA,SAAA,GAAA,KAAA,CAAA,KAAA,CAAA,gBACoB,CAAA,GAAA,CAAA,CAAA;AAG1B,MAAU,MAAA,uBAAsB,GAAA,CAAA,MAAA,GAC7B,8BAAoC,CAAA,KAAA,GAAA,QAAA,CAAY,KAAI,CAAA,GAAA,CAAM,KAC3D,CAAA,MAAA,CAAA,CAAA;AAAA,MACJ,SAAA,CAAA,WAAA,CAAA,GAAA,CAAA,KAAA,CAAA,MAAA,CAAA,GAAA,uBAAA,GAAA,SAAA,CAAA,WAAA,CAAA,GAAA,CAAA,KAAA,CAAA,UAAA,CAAA,GAAA,GAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAA2B,SAAA,GAAA,CAAA,CAAA,KAAA;AAC3B,MAAa,CAAA,CAAA,wBAAA,EAAA,CAAA;AACb,MAAS,UAAA,GAAA,IAAA,CAAA;AACT,MAAS,QAAA,CAAA,gBAAA,CAAiB,WAAW,EAAsB,wBAAA,CAAA,CAAA;AAC3D,MAAA,QAAA,CAAA,gBAAiC,CAAA,SAAA,EAAA,sBAAA,CAAA,CAAA;AACjC,MAAA,gCAA+B,CAAA,aAAA,CAAA;AAAA,MACjC,QAAA,CAAA,aAAA,GAAA,MAAA,KAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAI,wBAA0B,GAAA,CAAA,CAAA,KAAA;AAAO,MAAA,IAAA,CAAA,QAAA,CAAA,KAAA,IAAA,CAAA,KAAA,CAAA,KAAA;AACrC,QAAA,OAAmB;AAAO,MAAA,IAAA,UAAA,KAAA,KAAA;AAE1B,QAAA,OAAiB;AACjB,MAAA,MAAK,QAAA,GAAA,UAAA,CAAA,KAAA,CAAA,GAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAAU,MAAA,IAAA,CAAA,QAAA;AAEf,QAAM,OAAA;AAIN,MAAA,MAAM,MAAqB,GAAA,CAAA,QAAA,CAAA,KAAA,CAAA,qBAAgC,EAAA,CAAA,GAAA,CAAA,KAAA,CAAA,SAAA,CAAA,GAAA,CAAA,CAAA,GAAA,CAAA,KAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AAC3D,MAAM,MAAA,kBAAA,GAAA,KACF,UAAS,CAAsB,KAAA,CAAA,MAAA,CAAA,GAAA,QAAA,CAAA;AAEnC,MAAU,MAAA,uBAAsB,GAAA,CAAA,MAAA,GAC7B,4BAAoC,WAAA,CAAA,KAAA,GAAY,QAAI,CAAA,KACrD,CAAA,GAAA,CAAA,KAAA,CAAA,MAAA,CAAA,CAAA;AAAA,MACJ,SAAA,CAAA,WAAA,CAAA,GAAA,CAAA,KAAA,CAAA,MAAA,CAAA,GAAA,uBAAA,GAAA,SAAA,CAAA,WAAA,CAAA,GAAA,CAAA,KAAA,CAAA,UAAA,CAAA,GAAA,GAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAa,MAAA,sBAAA,GAAA,MAAA;AACb,MAAW,UAAA,GAAA,KAAU,CAAA;AACrB,MAAS,UAAA,CAAA,KAAA,CAAA,GAAA,CAAA,KAAA,CAAA;AACT,MAAS,QAAA,CAAA,mBAAA,CAAoB,WAAW,EAAsB,wBAAA,CAAA,CAAA;AAC9D,MAAqB,QAAA,CAAA,mBAAA,CAAA,SAAA,EAAA,sBAAA,CAAA,CAAA;AACrB,MAAI,oBAAA,EAAA,CAAA;AAAa,MAAA,IAAA,WAAgB;AAAA,QACnC,OAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAc,MAAA,yBAAA,GAAA,MAAA;AACd,MAAQ,WAAA,GAAA,KAAgB,CAAA;AAAA,MAC1B,OAAA,CAAA,KAAA,GAAA,CAAA,CAAA,KAAA,CAAA,IAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAc,MAAA,0BAAA,GAAA,MAAA;AACd,MAAA,WAAgB,GAAA,IAAA,CAAA;AAAA,MAClB,OAAA,CAAA,KAAA,GAAA,UAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAqB,eAAA,CAAA,MAAA;AACrB,MAAS,oBAAA,EAAA,CAAA;AAAqD,MAC/D,QAAA,CAAA,mBAAA,CAAA,SAAA,EAAA,sBAAA,CAAA,CAAA;AAED,KAAA,CAAA,CAAA;AACE,IAAA,MAAI,oBAA2B,GAAA,MAAA;AAC7B,MAAA,IAAA,QAAyB,CAAA,aAAA,KAAA,qBAAA;AAAA,QAC7B,QAAA,CAAA,aAAA,GAAA,qBAAA,CAAA;AAEA,KAAA,CAAA;AAKA,IAAA,gBAAA,CACE,KAAM,CAAA,SAAA,EAAW,kBAAkB,CAAA,EACnC,sCAEF,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}