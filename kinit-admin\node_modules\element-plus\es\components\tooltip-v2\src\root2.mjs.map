{"version": 3, "file": "root2.mjs", "sources": ["../../../../../../packages/components/tooltip-v2/src/root.vue"], "sourcesContent": ["<template>\n  <slot :open=\"open\" />\n</template>\n\n<script setup lang=\"ts\">\nimport {\n  computed,\n  onBeforeUnmount,\n  onMounted,\n  provide,\n  ref,\n  unref,\n  watch,\n} from 'vue'\nimport { useTimeoutFn } from '@vueuse/core'\nimport { useId, useNamespace } from '@element-plus/hooks'\nimport { isNumber, isPropAbsent } from '@element-plus/utils'\nimport { TOOLTIP_V2_OPEN, tooltipV2RootKey } from './constants'\nimport { tooltipV2RootProps } from './root'\n\ndefineOptions({\n  name: 'ElTooltipV2Root',\n})\n\nconst props = defineProps(tooltipV2RootProps)\n\n/**\n * internal open state, when no model value was provided, use this as indicator instead\n */\nconst _open = ref(props.defaultOpen)\nconst triggerRef = ref<HTMLElement | null>(null)\n\nconst open = computed<boolean>({\n  get: () => (isPropAbsent(props.open) ? _open.value : props.open),\n  set: (open) => {\n    _open.value = open\n    props['onUpdate:open']?.(open)\n  },\n})\n\nconst isOpenDelayed = computed(\n  () => isNumber(props.delayDuration) && props.delayDuration > 0\n)\n\nconst { start: onDelayedOpen, stop: clearTimer } = useTimeoutFn(\n  () => {\n    open.value = true\n  },\n  computed(() => props.delayDuration),\n  {\n    immediate: false,\n  }\n)\n\nconst ns = useNamespace('tooltip-v2')\n\nconst contentId = useId()\n\nconst onNormalOpen = () => {\n  clearTimer()\n  open.value = true\n}\n\nconst onDelayOpen = () => {\n  unref(isOpenDelayed) ? onDelayedOpen() : onNormalOpen()\n}\n\nconst onOpen = onNormalOpen\n\nconst onClose = () => {\n  clearTimer()\n  open.value = false\n}\n\nconst onChange = (open: boolean) => {\n  if (open) {\n    document.dispatchEvent(new CustomEvent(TOOLTIP_V2_OPEN))\n    onOpen()\n  }\n\n  props.onOpenChange?.(open)\n}\n\nwatch(open, onChange)\n\nonMounted(() => {\n  // Keeps only 1 tooltip open at a time\n  document.addEventListener(TOOLTIP_V2_OPEN, onClose)\n})\n\nonBeforeUnmount(() => {\n  clearTimer()\n  document.removeEventListener(TOOLTIP_V2_OPEN, onClose)\n})\n\nprovide(tooltipV2RootKey, {\n  contentId,\n  triggerRef,\n  ns,\n\n  onClose,\n  onDelayOpen,\n  onOpen,\n})\n\ndefineExpose({\n  /**\n   * @description open tooltip programmatically\n   */\n  onOpen,\n\n  /**\n   * @description close tooltip programmatically\n   */\n  onClose,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;mCAoBc,CAAA;AAAA,EACZ,IAAM,EAAA,iBAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAOA,IAAM,MAAA,KAAA,GAAQ,GAAI,CAAA,KAAA,CAAM,WAAW,CAAA,CAAA;AACnC,IAAM,MAAA,UAAA,GAAa,IAAwB,IAAI,CAAA,CAAA;AAE/C,IAAA,MAAM,OAAO,QAAkB,CAAA;AAAA,MAC7B,GAAA,EAAK,MAAO,YAAa,CAAA,KAAA,CAAM,IAAI,CAAI,GAAA,KAAA,CAAM,QAAQ,KAAM,CAAA,IAAA;AAAA,MAC3D,GAAA,EAAK,CAAC,KAAS,KAAA;AACb,QAAA,IAAA,EAAM,CAAQ;AACd,QAAA,KAAA,CAAM;AAAuB,QAC/B,CAAA,EAAA,GAAA,KAAA,CAAA,eAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,KAAA,CAAA,CAAA;AAAA,OACD;AAED,KAAM,CAAA,CAAA;AAIN,IAAA,MAAM,aAAS,GAAA,QAAe,CAAM,MAAA,QAAA,CAAA,KAAe,cAC3C,CAAA,IAAA,KAAA,CAAA,aAAA,GAAA,CAAA,CAAA,CAAA;AACJ,IAAA,MAAA,EAAa,KAAA,EAAA,aAAA,EAAA,IAAA,EAAA,UAAA,EAAA,GAAA,YAAA,CAAA,MAAA;AAAA,MAEf,IAAA,CAAA,KAAS,GAAM,IAAA,CAAA;AACf,KAAA,EACa,QAAA,CAAA,MAAA,KAAA,CAAA,aAAA,CAAA,EAAA;AAAA,MAEf,SAAA,EAAA,KAAA;AAEA,KAAM,CAAA,CAAA;AAEN,IAAA,MAAM,iBAAkB,CAAA,YAAA,CAAA,CAAA;AAExB,IAAA,MAAM,iBAAqB,EAAA,CAAA;AACzB,IAAW,MAAA,YAAA,GAAA,MAAA;AACX,MAAA,UAAa,EAAA,CAAA;AAAA,MACf,IAAA,CAAA,KAAA,GAAA,IAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,WAAmB,GAAA,MAAkB;AAAiB,MACxD,KAAA,CAAA,aAAA,CAAA,GAAA,aAAA,EAAA,GAAA,YAAA,EAAA,CAAA;AAEA,KAAA,CAAA;AAEA,IAAA,MAAM,qBAAgB,CAAA;AACpB,IAAW,MAAA,OAAA,GAAA,MAAA;AACX,MAAA,UAAa,EAAA,CAAA;AAAA,MACf,IAAA,CAAA,KAAA,GAAA,KAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAA,MAAI,QAAM,GAAA,CAAA,KAAA,KAAA;AACR,MAAA,IAAA,EAAA,CAAA;AACA,MAAO,IAAA,KAAA,EAAA;AAAA,QACT,QAAA,CAAA,aAAA,CAAA,IAAA,WAAA,CAAA,eAAA,CAAA,CAAA,CAAA;AAEA,QAAA;AAAyB,OAC3B;AAEA,MAAA,CAAA,EAAA,QAAoB,CAAA,YAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,KAAA,EAAA,KAAA,CAAA,CAAA;AAEpB,KAAA,CAAA;AAEE,IAAS,KAAA,CAAA,IAAA,EAAA,QAAA,CAAA,CAAA;AAAyC,IACpD,SAAC,CAAA,MAAA;AAED,MAAA,QAAA,CAAA,gBAAsB,CAAA,eAAA,EAAA,OAAA,CAAA,CAAA;AACpB,KAAW,CAAA,CAAA;AACX,IAAS,eAAA,CAAA,MAAA;AAA4C,MACtD,UAAA,EAAA,CAAA;AAED,MAAA,QAA0B,CAAA,mBAAA,CAAA,eAAA,EAAA,OAAA,CAAA,CAAA;AAAA,KACxB,CAAA,CAAA;AAAA,IACA,OAAA,CAAA,gBAAA,EAAA;AAAA,MACA,SAAA;AAAA,MAEA,UAAA;AAAA,MACA,EAAA;AAAA,MACA,OAAA;AAAA,MACD,WAAA;AAED,MAAa,MAAA;AAAA,KAIX,CAAA,CAAA;AAAA,IAKA,MAAA,CAAA;AAAA,MACD,MAAA;;;;;;;;;;;;"}