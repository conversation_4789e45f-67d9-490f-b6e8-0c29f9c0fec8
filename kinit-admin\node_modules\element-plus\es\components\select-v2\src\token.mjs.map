{"version": 3, "file": "token.mjs", "sources": ["../../../../../../packages/components/select-v2/src/token.ts"], "sourcesContent": ["import type { OptionProps, SelectProps } from './defaults'\nimport type { ExtractPropTypes, InjectionKey, Ref } from 'vue'\nimport type { Option } from './select.types'\nimport type { TooltipInstance } from '@element-plus/components/tooltip'\n\nexport interface SelectV2Context {\n  props: ExtractPropTypes<typeof SelectProps>\n  expanded: boolean\n  tooltipRef: Ref<TooltipInstance>\n  onSelect: (option: Option, index: number, byClick?: boolean) => void\n  onHover: (idx: number) => void\n  onKeyboardNavigate: (direction: 'forward' | 'backward') => void\n  onKeyboardSelect: () => void\n}\n\nexport const selectV2InjectionKey: InjectionKey<SelectV2Context> = Symbol(\n  'ElSelectV2Injection'\n)\nexport type IOptionV2Props = ExtractPropTypes<typeof OptionProps>\nexport type ISelectV2Props = ExtractPropTypes<typeof SelectProps>\n"], "names": [], "mappings": "AAAY,MAAC,oBAAoB,GAAG,MAAM,CAAC,qBAAqB;;;;"}