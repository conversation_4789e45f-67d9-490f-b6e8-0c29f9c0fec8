{"version": 3, "file": "tokens.js", "sources": ["../../../../../../packages/components/dropdown/src/tokens.ts"], "sourcesContent": ["import type { ComputedRef, InjectionKey, Ref } from 'vue'\n\nexport type ElDropdownInjectionContext = {\n  contentRef: Ref<HTMLElement | null>\n  role: ComputedRef<string>\n  triggerId: ComputedRef<string>\n  isUsingKeyboard: Ref<boolean>\n  onItemLeave: (e: PointerEvent) => void\n  onItemEnter: (e: PointerEvent) => void\n}\n\nexport const DROPDOWN_INJECTION_KEY: InjectionKey<ElDropdownInjectionContext> =\n  Symbol('elDropdown')\n"], "names": [], "mappings": ";;;;AAAY,MAAC,sBAAsB,GAAG,MAAM,CAAC,YAAY;;;;"}