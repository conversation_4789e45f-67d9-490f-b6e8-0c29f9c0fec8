{"version": 3, "file": "option-group.mjs", "sources": ["../../../../../../packages/components/select/src/option-group.vue"], "sourcesContent": ["<template>\n  <ul v-show=\"visible\" ref=\"groupRef\" :class=\"ns.be('group', 'wrap')\">\n    <li :class=\"ns.be('group', 'title')\">{{ label }}</li>\n    <li>\n      <ul :class=\"ns.b('group')\">\n        <slot />\n      </ul>\n    </li>\n  </ul>\n</template>\n\n<script lang=\"ts\">\n// @ts-nocheck\nimport {\n  computed,\n  defineComponent,\n  getCurrentInstance,\n  onMounted,\n  provide,\n  reactive,\n  ref,\n  toRefs,\n} from 'vue'\nimport { isArray } from '@vue/shared'\nimport { useMutationObserver } from '@vueuse/core'\nimport { useNamespace } from '@element-plus/hooks'\nimport { selectGroupKey } from './token'\n\nexport default defineComponent({\n  name: 'ElOptionGroup',\n  componentName: 'ElOptionGroup',\n\n  props: {\n    /**\n     * @description name of the group\n     */\n    label: String,\n    /**\n     * @description whether to disable all options in this group\n     */\n    disabled: Boolean,\n  },\n  setup(props) {\n    const ns = useNamespace('select')\n    const groupRef = ref(null)\n    const instance = getCurrentInstance()\n    const children = ref([])\n\n    provide(\n      selectGroupKey,\n      reactive({\n        ...toRefs(props),\n      })\n    )\n\n    const visible = computed(() =>\n      children.value.some((option) => option.visible === true)\n    )\n\n    // get all instances of options\n    const flattedChildren = (node) => {\n      const children = []\n      if (isArray(node.children)) {\n        node.children.forEach((child) => {\n          if (\n            child.type &&\n            child.type.name === 'ElOption' &&\n            child.component &&\n            child.component.proxy\n          ) {\n            children.push(child.component.proxy)\n          } else if (child.children?.length) {\n            children.push(...flattedChildren(child))\n          } else if (child.component?.subTree) {\n            children.push(...flattedChildren(child.component.subTree))\n          }\n        })\n      }\n      return children\n    }\n\n    const updateChildren = () => {\n      children.value = flattedChildren(instance.subTree)\n    }\n\n    onMounted(() => {\n      updateChildren()\n    })\n\n    useMutationObserver(groupRef, updateChildren, {\n      attributes: true,\n      subtree: true,\n      childList: true,\n    })\n\n    return {\n      groupRef,\n      visible,\n      ns,\n    }\n  },\n})\n</script>\n"], "names": ["_withDirectives", "_openBlock", "_createElementBlock", "_normalizeClass", "_createElementVNode", "_toDisplayString", "_renderSlot", "_vShow"], "mappings": ";;;;;;;;AA4BA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,eAAA;AAAA,EACN,aAAe,EAAA,eAAA;AAAA,EAEf,KAAO,EAAA;AAAA,IAIL,KAAO,EAAA,MAAA;AAAA,IAIP,QAAU,EAAA,OAAA;AAAA,GACZ;AAAA,EACA,MAAM,KAAO,EAAA;AACX,IAAM,MAAA,EAAA,GAAK,aAAa,QAAQ,CAAA,CAAA;AAChC,IAAM,MAAA,QAAA,GAAW,IAAI,IAAI,CAAA,CAAA;AACzB,IAAA,MAAM,WAAW,kBAAmB,EAAA,CAAA;AACpC,IAAM,MAAA,QAAA,GAAW,GAAI,CAAA,EAAE,CAAA,CAAA;AAEvB,IAAA,OAAA,CACE,gBACA,QAAS,CAAA;AAAA,MACP,GAAG,OAAO,KAAK,CAAA;AAAA,KAChB,CACH,CAAA,CAAA;AAEA,IAAM,MAAA,OAAA,GAAU,QAAS,CAAA,MACvB,QAAS,CAAA,KAAA,CAAM,IAAK,CAAA,CAAC,MAAW,KAAA,MAAA,CAAO,OAAY,KAAA,IAAI,CACzD,CAAA,CAAA;AAGA,IAAM,MAAA,eAAA,GAAkB,CAAC,IAAS,KAAA;AAChC,MAAA,MAAM,YAAW,EAAC,CAAA;AAClB,MAAI,IAAA,OAAA,CAAQ,IAAK,CAAA,QAAQ,CAAG,EAAA;AAC1B,QAAK,IAAA,CAAA,QAAA,CAAS,OAAQ,CAAA,CAAC,KAAU,KAAA;AAC/B,UACE,IAAA,EAAA,EAAA,EAAM,CACN;AAIA,UAAS,IAAA,KAAA,CAAA,IAAA,IAAW,KAAA,CAAA,IAAA,CAAA,IAAU,KAAK,UAAA,IAAA,KAAA,CAAA,SAAA,IAAA,KAAA,CAAA,SAAA,CAAA,KAAA,EAAA;AAAA,YACrC,SAAW,CAAM,IAAA,CAAA,KAAA,CAAA,SAAkB,CAAA,KAAA,CAAA,CAAA;AACjC,WAAA,MAAA,IAAA,CAAS,EAAK,GAAA,KAAmB,CAAA,QAAA,KAAA,IAAA,GAAM,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA;AAAA,YACzC,SAAW,CAAM,IAAA,CAAA,GAAA,eAAoB,CAAA,KAAA,CAAA,CAAA,CAAA;AACnC,WAAA,MAAA,IAAA,CAAS,KAAK,KAAG,CAAA,SAAA,KAAsB,IAAA,GAAA,KAAA,CAAA,aAAkB,EAAA;AAAA,YAC3D,SAAA,CAAA,IAAA,CAAA,GAAA,eAAA,CAAA,KAAA,CAAA,SAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AAAA,WACD;AAAA,SACH,CAAA,CAAA;AACA,OAAO;AAAA,MACT,OAAA,SAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAS,MAAA,cAAwB,GAAA,MAAA;AAAgB,MACnD,QAAA,CAAA,KAAA,GAAA,eAAA,CAAA,QAAA,CAAA,OAAA,CAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAe,SAAA,CAAA,MAAA;AAAA,MAChB,cAAA,EAAA,CAAA;AAED,KAAA,CAAA,CAAA;AAA8C,IAAA,mBAChC,CAAA,QAAA,EAAA,cAAA,EAAA;AAAA,MACZ,UAAS,EAAA,IAAA;AAAA,MACT,OAAW,EAAA,IAAA;AAAA,MACZ,SAAA,EAAA,IAAA;AAED,KAAO,CAAA,CAAA;AAAA,IACL,OAAA;AAAA,MACA,QAAA;AAAA,MACA,OAAA;AAAA,MACF,EAAA;AAAA,KACF,CAAA;AACF,GAAC;;yCA7FM,EAAA,MAAA,EAAA,KAAA,EAAA,QAAA,EAAA;AAAA,EAAA,OAPoBA,cAAA,EAAAC,SAAA,EAAA,EAAAC,kBAAA,CAAA,IAAA,EAAA;AAAA,IAAY,GAAA,EAAA,UAAO;AAAK,IAAA,KAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,OAAA,EAAA,MAAA,CAAA,CAAA;KACM;AAAA,IAAhDC,kBAAO,CAAA,IAAA,EAAA;AAAK,MAAA,KAAA,EAAAD,cAA4B,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,OAAA,EAAA,OAAA,CAAA,CAAA;AAAA,KAKxC,EAAAE,eAAA,CAAA,IAAA,CAAA,KAAA,CAAA,EAAA,CAAA,CAAA;AAAA,IAAAD,kBADE,CAAA,IAAA,EAAA,IAAA,EAAA;AAAA,MAFAA,kBAAO,CAAA,IAAA,EAAA;AAAI,QAAA,KAAA,EAAAD,cAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;SACN;AAAA,QAAAG,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,SAAA,CAAA;;;;AAJK,IAAA,CAAAC,KAAA,EAAA,IAAA,CAAA,OAAA,CAAA;;;;;;;"}