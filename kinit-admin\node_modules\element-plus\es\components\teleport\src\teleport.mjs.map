{"version": 3, "file": "teleport.mjs", "sources": ["../../../../../../packages/components/teleport/src/teleport.ts"], "sourcesContent": ["import { buildProps, definePropType } from '@element-plus/utils'\nimport type { ExtractPropTypes, StyleValue } from 'vue'\nimport type Teleport from './teleport.vue'\n\nexport const teleportProps = buildProps({\n  container: {\n    type: definePropType<string>(String),\n    default: 'body',\n  },\n  disabled: {\n    type: Boolean,\n    default: false,\n  },\n  style: {\n    type: definePropType<StyleValue>([String, Array, Object]),\n  },\n  zIndex: {\n    type: String,\n    default: '2000',\n  },\n} as const)\n\nexport type TeleportProps = ExtractPropTypes<typeof teleportProps>\nexport type TeleportInstance = InstanceType<typeof Teleport>\n"], "names": [], "mappings": ";;;AACY,MAAC,aAAa,GAAG,UAAU,CAAC;AACxC,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;AACjD,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,MAAM;AACnB,GAAG;AACH,CAAC;;;;"}