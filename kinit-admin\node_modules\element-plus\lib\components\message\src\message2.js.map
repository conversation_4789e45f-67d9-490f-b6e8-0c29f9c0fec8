{"version": 3, "file": "message2.js", "sources": ["../../../../../../packages/components/message/src/message.vue"], "sourcesContent": ["<template>\n  <transition\n    :name=\"ns.b('fade')\"\n    @before-leave=\"onClose\"\n    @after-leave=\"$emit('destroy')\"\n  >\n    <div\n      v-show=\"visible\"\n      :id=\"id\"\n      ref=\"messageRef\"\n      :class=\"[\n        ns.b(),\n        { [ns.m(type)]: type },\n        ns.is('center', center),\n        ns.is('closable', showClose),\n        customClass,\n      ]\"\n      :style=\"customStyle\"\n      role=\"alert\"\n      @mouseenter=\"clearTimer\"\n      @mouseleave=\"startTimer\"\n    >\n      <el-badge\n        v-if=\"repeatNum > 1\"\n        :value=\"repeatNum\"\n        :type=\"badgeType\"\n        :class=\"ns.e('badge')\"\n      />\n      <el-icon v-if=\"iconComponent\" :class=\"[ns.e('icon'), typeClass]\">\n        <component :is=\"iconComponent\" />\n      </el-icon>\n      <slot>\n        <p v-if=\"!dangerouslyUseHTMLString\" :class=\"ns.e('content')\">\n          {{ message }}\n        </p>\n        <!-- Caution here, message could've been compromised, never use user's input as message -->\n        <p v-else :class=\"ns.e('content')\" v-html=\"message\" />\n      </slot>\n      <el-icon v-if=\"showClose\" :class=\"ns.e('closeBtn')\" @click.stop=\"close\">\n        <Close />\n      </el-icon>\n    </div>\n  </transition>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, onMounted, ref, watch } from 'vue'\nimport { useEventListener, useResizeObserver, useTimeoutFn } from '@vueuse/core'\nimport { TypeComponents, TypeComponentsMap } from '@element-plus/utils'\nimport { EVENT_CODE } from '@element-plus/constants'\nimport ElBadge from '@element-plus/components/badge'\nimport { useGlobalComponentSettings } from '@element-plus/components/config-provider'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { messageEmits, messageProps } from './message'\nimport { getLastOffset, getOffsetOrSpace } from './instance'\nimport type { BadgeProps } from '@element-plus/components/badge'\nimport type { CSSProperties } from 'vue'\n\nconst { Close } = TypeComponents\n\ndefineOptions({\n  name: 'ElMessage',\n})\n\nconst props = defineProps(messageProps)\ndefineEmits(messageEmits)\n\nconst { ns, zIndex } = useGlobalComponentSettings('message')\nconst { currentZIndex, nextZIndex } = zIndex\n\nconst messageRef = ref<HTMLDivElement>()\nconst visible = ref(false)\nconst height = ref(0)\n\nlet stopTimer: (() => void) | undefined = undefined\n\nconst badgeType = computed<BadgeProps['type']>(() =>\n  props.type ? (props.type === 'error' ? 'danger' : props.type) : 'info'\n)\nconst typeClass = computed(() => {\n  const type = props.type\n  return { [ns.bm('icon', type)]: type && TypeComponentsMap[type] }\n})\nconst iconComponent = computed(\n  () => props.icon || TypeComponentsMap[props.type] || ''\n)\n\nconst lastOffset = computed(() => getLastOffset(props.id))\nconst offset = computed(\n  () => getOffsetOrSpace(props.id, props.offset) + lastOffset.value\n)\nconst bottom = computed((): number => height.value + offset.value)\nconst customStyle = computed<CSSProperties>(() => ({\n  top: `${offset.value}px`,\n  zIndex: currentZIndex.value,\n}))\n\nfunction startTimer() {\n  if (props.duration === 0) return\n  ;({ stop: stopTimer } = useTimeoutFn(() => {\n    close()\n  }, props.duration))\n}\n\nfunction clearTimer() {\n  stopTimer?.()\n}\n\nfunction close() {\n  visible.value = false\n}\n\nfunction keydown({ code }: KeyboardEvent) {\n  if (code === EVENT_CODE.esc) {\n    // press esc to close the message\n    close()\n  }\n}\n\nonMounted(() => {\n  startTimer()\n  nextZIndex()\n  visible.value = true\n})\n\nwatch(\n  () => props.repeatNum,\n  () => {\n    clearTimer()\n    startTimer()\n  }\n)\n\nuseEventListener(document, 'keydown', keydown)\n\nuseResizeObserver(messageRef, () => {\n  height.value = messageRef.value!.getBoundingClientRect().height\n})\n\ndefineExpose({\n  visible,\n  bottom,\n  close,\n})\n</script>\n"], "names": ["TypeComponents", "useGlobalComponentSettings", "ref", "computed", "TypeComponentsMap", "getLastOffset", "getOffsetOrSpace", "useTimeoutFn", "EVENT_CODE", "onMounted", "watch", "useEventListener", "useResizeObserver"], "mappings": ";;;;;;;;;;;;;;;;;;;;uCA4Dc,CAAA;AAAA,EACZ,IAAM,EAAA,WAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAJA,IAAA,MAAM,EAAE,KAAU,EAAA,GAAAA,mBAAA,CAAA;AASlB,IAAA,MAAM,EAAE,EAAA,EAAI,MAAW,EAAA,GAAAC,0CAAA,CAA2B,SAAS,CAAA,CAAA;AAC3D,IAAM,MAAA,EAAE,eAAe,UAAe,EAAA,GAAA,MAAA,CAAA;AAEtC,IAAA,MAAM,aAAaC,OAAoB,EAAA,CAAA;AACvC,IAAM,MAAA,OAAA,GAAUA,QAAI,KAAK,CAAA,CAAA;AACzB,IAAM,MAAA,MAAA,GAASA,QAAI,CAAC,CAAA,CAAA;AAEpB,IAAA,IAAI,SAAsC,GAAA,KAAA,CAAA,CAAA;AAE1C,IAAM,MAAA,SAAA,GAAYC,YAA6B,CAAA,MAC7C,KAAM,CAAA,IAAA,GAAQ,KAAM,CAAA,IAAA,KAAS,OAAU,GAAA,QAAA,GAAW,KAAM,CAAA,IAAA,GAAQ,MAClE,CAAA,CAAA;AACA,IAAM,MAAA,SAAA,GAAYA,aAAS,MAAM;AAC/B,MAAA,MAAM,OAAO,KAAM,CAAA,IAAA,CAAA;AACnB,MAAO,OAAA,EAAE,CAAC,EAAG,CAAA,EAAA,CAAG,QAAQ,IAAI,CAAA,GAAI,IAAQ,IAAAC,sBAAA,CAAkB,IAAM,CAAA,EAAA,CAAA;AAAA,KACjE,CAAA,CAAA;AACD,IAAM,MAAA,aAAA,GAAgBD,aACpB,MAAM,KAAA,CAAM,QAAQC,sBAAkB,CAAA,KAAA,CAAM,SAAS,EACvD,CAAA,CAAA;AAEA,IAAA,MAAM,aAAaD,YAAS,CAAA,MAAME,sBAAc,CAAA,KAAA,CAAM,EAAE,CAAC,CAAA,CAAA;AACzD,IAAM,MAAA,MAAA,GAASF,YACb,CAAA,MAAMG,yBAAiB,CAAA,KAAA,CAAM,IAAI,KAAM,CAAA,MAAM,CAAI,GAAA,UAAA,CAAW,KAC9D,CAAA,CAAA;AACA,IAAA,MAAM,SAASH,YAAS,CAAA,MAAc,MAAO,CAAA,KAAA,GAAQ,OAAO,KAAK,CAAA,CAAA;AACjE,IAAM,MAAA,WAAA,GAAcA,aAAwB,OAAO;AAAA,MACjD,GAAA,EAAK,GAAG,MAAO,CAAA,KAAA,CAAA,EAAA,CAAA;AAAA,MACf,QAAQ,aAAc,CAAA,KAAA;AAAA,KACtB,CAAA,CAAA,CAAA;AAEF,IAAsB,SAAA,UAAA,GAAA;AACpB,MAAA,IAAI,MAAM,QAAa,KAAA,CAAA;AAAG,QAAA,OAAA;AACzB,MAAC,CAAE,EAAA,IAAA,EAAM,SAAU,EAAA,GAAII,kBAAa,MAAM;AACzC,QAAM,KAAA,EAAA,CAAA;AAAA,OACR,EAAG,MAAM,QAAQ,CAAA,EAAA;AAAA,KACnB;AAEA,IAAsB,SAAA,UAAA,GAAA;AACpB,MAAY,SAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,SAAA,EAAA,CAAA;AAAA,KACd;AAEA,IAAiB,SAAA,KAAA,GAAA;AACf,MAAA,OAAA,CAAQ,KAAQ,GAAA,KAAA,CAAA;AAAA,KAClB;AAEA,IAAA,SAAA,OAAA,CAAiB,EAAE,IAAuB,EAAA,EAAA;AACxC,MAAI,IAAA,IAAA,KAASC,gBAAW,GAAK,EAAA;AAE3B,QAAM,KAAA,EAAA,CAAA;AAAA,OACR;AAAA,KACF;AAEA,IAAAC,aAAA,CAAU,MAAM;AACd,MAAW,UAAA,EAAA,CAAA;AACX,MAAW,UAAA,EAAA,CAAA;AACX,MAAA,OAAA,CAAQ,KAAQ,GAAA,IAAA,CAAA;AAAA,KACjB,CAAA,CAAA;AAED,IACEC,SAAA,CAAA,MAAM,KAAM,CAAA,SAAA,EACZ,MAAM;AACJ,MAAW,UAAA,EAAA,CAAA;AACX,MAAW,UAAA,EAAA,CAAA;AAAA,KAEf,CAAA,CAAA;AAEA,IAAiBC,qBAAA,CAAA,QAAA,EAAU,WAAW,OAAO,CAAA,CAAA;AAE7C,IAAAC,sBAAA,CAAkB,YAAY,MAAM;AAClC,MAAA,MAAA,CAAO,KAAQ,GAAA,UAAA,CAAW,KAAO,CAAA,qBAAA,EAAwB,CAAA,MAAA,CAAA;AAAA,KAC1D,CAAA,CAAA;AAED,IAAa,MAAA,CAAA;AAAA,MACX,OAAA;AAAA,MACA,MAAA;AAAA,MACA,KAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}