{"version": 3, "file": "node2.js", "sources": ["../../../../../../packages/components/cascader-panel/src/node.vue"], "sourcesContent": ["<template>\n  <li\n    :id=\"`${menuId}-${node.uid}`\"\n    role=\"menuitem\"\n    :aria-haspopup=\"!isLeaf\"\n    :aria-owns=\"isLeaf ? null : menuId\"\n    :aria-expanded=\"inExpandingPath\"\n    :tabindex=\"expandable ? -1 : undefined\"\n    :class=\"[\n      ns.b(),\n      ns.is('selectable', checkStrictly),\n      ns.is('active', node.checked),\n      ns.is('disabled', !expandable),\n      inExpandingPath && 'in-active-path',\n      inCheckedPath && 'in-checked-path',\n    ]\"\n    @mouseenter=\"handleHoverExpand\"\n    @focus=\"handleHoverExpand\"\n    @click=\"handleClick\"\n  >\n    <!-- prefix -->\n    <el-checkbox\n      v-if=\"multiple\"\n      :model-value=\"node.checked\"\n      :indeterminate=\"node.indeterminate\"\n      :disabled=\"isDisabled\"\n      @click.stop\n      @update:model-value=\"handleSelectCheck\"\n    />\n    <el-radio\n      v-else-if=\"checkStrictly\"\n      :model-value=\"checkedNodeId\"\n      :label=\"node.uid\"\n      :disabled=\"isDisabled\"\n      @update:model-value=\"handleSelectCheck\"\n      @click.stop\n    >\n      <!--\n        Add an empty element to avoid render label,\n        do not use empty fragment here for https://github.com/vuejs/vue-next/pull/2485\n      -->\n      <span />\n    </el-radio>\n    <el-icon v-else-if=\"isLeaf && node.checked\" :class=\"ns.e('prefix')\">\n      <check />\n    </el-icon>\n\n    <!-- content -->\n    <node-content />\n\n    <!-- postfix -->\n    <template v-if=\"!isLeaf\">\n      <el-icon v-if=\"node.loading\" :class=\"[ns.is('loading'), ns.e('postfix')]\">\n        <loading />\n      </el-icon>\n      <el-icon v-else :class=\"['arrow-right', ns.e('postfix')]\">\n        <arrow-right />\n      </el-icon>\n    </template>\n  </li>\n</template>\n\n<script lang=\"ts\">\n// @ts-nocheck\nimport { computed, defineComponent, inject } from 'vue'\nimport ElCheckbox from '@element-plus/components/checkbox'\nimport ElRadio from '@element-plus/components/radio'\nimport ElIcon from '@element-plus/components/icon'\nimport { useNamespace } from '@element-plus/hooks'\nimport { ArrowRight, Check, Loading } from '@element-plus/icons-vue'\nimport NodeContent from './node-content'\nimport { CASCADER_PANEL_INJECTION_KEY } from './types'\nimport type { default as CascaderNode } from './node'\n\nimport type { PropType } from 'vue'\n\nexport default defineComponent({\n  name: 'ElCascaderNode',\n\n  components: {\n    ElCheckbox,\n    ElRadio,\n    NodeContent,\n    ElIcon,\n    Check,\n    Loading,\n    ArrowRight,\n  },\n\n  props: {\n    node: {\n      type: Object as PropType<CascaderNode>,\n      required: true,\n    },\n    menuId: String,\n  },\n\n  emits: ['expand'],\n\n  setup(props, { emit }) {\n    const panel = inject(CASCADER_PANEL_INJECTION_KEY)!\n\n    const ns = useNamespace('cascader-node')\n    const isHoverMenu = computed(() => panel.isHoverMenu)\n    const multiple = computed(() => panel.config.multiple)\n    const checkStrictly = computed(() => panel.config.checkStrictly)\n    const checkedNodeId = computed(() => panel.checkedNodes[0]?.uid)\n    const isDisabled = computed(() => props.node.isDisabled)\n    const isLeaf = computed(() => props.node.isLeaf)\n    const expandable = computed(\n      () => (checkStrictly.value && !isLeaf.value) || !isDisabled.value\n    )\n    const inExpandingPath = computed(() => isInPath(panel.expandingNode!))\n    // only useful in check-strictly mode\n    const inCheckedPath = computed(\n      () => checkStrictly.value && panel.checkedNodes.some(isInPath)\n    )\n\n    const isInPath = (node: CascaderNode) => {\n      const { level, uid } = props.node\n      return node?.pathNodes[level - 1]?.uid === uid\n    }\n\n    const doExpand = () => {\n      if (inExpandingPath.value) return\n      panel.expandNode(props.node)\n    }\n\n    const doCheck = (checked: boolean) => {\n      const { node } = props\n      if (checked === node.checked) return\n      panel.handleCheckChange(node, checked)\n    }\n\n    const doLoad = () => {\n      panel.lazyLoad(props.node, () => {\n        if (!isLeaf.value) doExpand()\n      })\n    }\n\n    const handleHoverExpand = (e: Event) => {\n      if (!isHoverMenu.value) return\n      handleExpand()\n      !isLeaf.value && emit('expand', e)\n    }\n\n    const handleExpand = () => {\n      const { node } = props\n      // do not exclude leaf node because the menus expanded might have to reset\n      if (!expandable.value || node.loading) return\n      node.loaded ? doExpand() : doLoad()\n    }\n\n    const handleClick = () => {\n      if (isHoverMenu.value && !isLeaf.value) return\n\n      if (\n        isLeaf.value &&\n        !isDisabled.value &&\n        !checkStrictly.value &&\n        !multiple.value\n      ) {\n        handleCheck(true)\n      } else {\n        handleExpand()\n      }\n    }\n\n    const handleSelectCheck = (checked: boolean) => {\n      if (checkStrictly.value) {\n        doCheck(checked)\n        if (props.node.loaded) {\n          doExpand()\n        }\n      } else {\n        handleCheck(checked)\n      }\n    }\n\n    const handleCheck = (checked: boolean) => {\n      if (!props.node.loaded) {\n        doLoad()\n      } else {\n        doCheck(checked)\n        !checkStrictly.value && doExpand()\n      }\n    }\n\n    return {\n      panel,\n      isHoverMenu,\n      multiple,\n      checkStrictly,\n      checkedNodeId,\n      isDisabled,\n      isLeaf,\n      expandable,\n      inExpandingPath,\n      inCheckedPath,\n      ns,\n      handleHoverExpand,\n      handleExpand,\n      handleClick,\n      handleCheck,\n      handleSelectCheck,\n    }\n  },\n})\n</script>\n"], "names": ["defineComponent", "ElCheckbox", "ElRadio", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ElIcon", "Check", "Loading", "ArrowRight", "inject", "CASCADER_PANEL_INJECTION_KEY", "useNamespace", "computed", "_resolveComponent", "_openBlock", "_normalizeClass", "_createCommentVNode", "_createBlock", "_withModifiers", "_createVNode", "_createElementBlock", "_Fragment"], "mappings": ";;;;;;;;;;;;;;;AA4EA,MAAK,YAAaA,mBAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,gBAAA;AAAA,EAEN,UAAY,EAAA;AAAA,gBACVC,gBAAA;AAAA,aACAC,eAAA;AAAA,iBACAC,sBAAA;AAAA,YACAC,cAAA;AAAA,WACAC,cAAA;AAAA,aACAC,gBAAA;AAAA,gBACAC,mBAAA;AAAA,GACF;AAAA,EAEA,KAAO,EAAA;AAAA,IACL,IAAM,EAAA;AAAA,MACJ,IAAM,EAAA,MAAA;AAAA,MACN,QAAU,EAAA,IAAA;AAAA,KACZ;AAAA,IACA,MAAQ,EAAA,MAAA;AAAA,GACV;AAAA,EAEA,KAAA,EAAO,CAAC,QAAQ,CAAA;AAAA,EAEhB,KAAA,CAAM,KAAO,EAAA,EAAE,IAAQ,EAAA,EAAA;AACrB,IAAM,MAAA,KAAA,GAAQC,WAAOC,kCAA4B,CAAA,CAAA;AAEjD,IAAM,MAAA,EAAA,GAAKC,qBAAa,eAAe,CAAA,CAAA;AACvC,IAAA,MAAM,WAAc,GAAAC,YAAA,CAAS,MAAM,KAAA,CAAM,WAAW,CAAA,CAAA;AACpD,IAAA,MAAM,QAAW,GAAAA,YAAA,CAAS,MAAM,KAAA,CAAM,OAAO,QAAQ,CAAA,CAAA;AACrD,IAAA,MAAM,aAAgB,GAAAA,YAAA,CAAS,MAAM,KAAA,CAAM,OAAO,aAAa,CAAA,CAAA;AAC/D,IAAA,MAAM,gBAAgBA,YAAS,CAAA,MAAM;AACrC,MAAA,IAAM,EAAa,CAAA;AACnB,MAAA,OAAe,CAAA,EAAA,GAAA,KAAA,CAAA,YAAe,CAAA,CAAA,CAAM,KAAK,IAAM,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,CAAA;AAC/C,KAAM,CAAA,CAAA;AAGN,IAAA,MAAM,yBAA2B,CAAA,MAAA,KAAM,CAAS,IAAA,CAAA;AAEhD,IAAM,MAAA,MAAA,GAAAA,wBACE,CAAA,IAAA,CAAA,MAAA,CAAA,CAAA;AAGR,IAAM,MAAA,UAAA,GAAmCA,YAAA,CAAA,MAAA,aAAA,CAAA,KAAA,IAAA,CAAA,MAAA,CAAA,KAAA,IAAA,CAAA,UAAA,CAAA,KAAA,CAAA,CAAA;AACvC,IAAM,MAAA,eAAS,GAAAA,YAAc,CAAA,MAAA,QAAA,CAAA,KAAA,CAAA,aAAA,CAAA,CAAA,CAAA;AAC7B,IAAA,MAAA,aAAa,GAAAA,YAAkB,CAAA,MAAI,aAAQ,CAAA,KAAA,IAAA,KAAA,CAAA,YAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA,IAC7C,MAAA,QAAA,GAAA,CAAA,IAAA,KAAA;AAEA,MAAA,IAAM;AACJ,MAAA,MAAoB,EAAA,KAAA,EAAA,GAAA,EAAA,GAAA,KAAA,CAAA,IAAA,CAAA;AAAO,MAAA,OAAA,CAAA,CAAA,EAAA,GAAA,IAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,IAAA,CAAA,SAAA,CAAA,KAAA,GAAA,CAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,GAAA,MAAA,GAAA,CAAA;AAC3B,KAAM,CAAA;AAAqB,IAC7B,MAAA,QAAA,GAAA,MAAA;AAEA,MAAM,IAAA,eAAgC,CAAA,KAAA;AACpC,QAAA,OAAiB;AACjB,MAAA,gBAAgB,CAAK,KAAA,CAAA,IAAA,CAAA,CAAA;AAAS,KAAA,CAAA;AAC9B,IAAM,MAAA,OAAA,GAAA,CAAA,OAAA;AAA+B,MACvC,MAAA,EAAA,IAAA,EAAA,GAAA,KAAA,CAAA;AAEA,MAAA,IAAM,YAAe,IAAA,CAAA,OAAA;AACnB,QAAM,OAAA;AACJ,MAAA,KAAA,CAAI,iBAAQ,CAAA,IAAA,EAAA,OAAA,CAAA,CAAA;AAAO,KAAS,CAAA;AAAA,IAAA,MAC7B,MAAA,GAAA,MAAA;AAAA,MACH,KAAA,CAAA,QAAA,CAAA,KAAA,CAAA,IAAA,EAAA,MAAA;AAEA,QAAM,IAAA,CAAA,MAAA,CAAA,KAAA;AACJ,UAAI,QAAa,EAAA,CAAA;AAAO,OAAA,CAAA,CAAA;AACxB,KAAa,CAAA;AACb,IAAA,MAAQ,iBAAc,GAAA,CAAA,CAAA,KAAA;AAAW,MACnC,IAAA,CAAA,WAAA,CAAA,KAAA;AAEA,QAAA;AACE,MAAA,YAAiB,EAAA,CAAA;AAEjB,MAAI,CAAA,MAAY,CAAA,KAAA,IAAA,IAAA,CAAA,QAAc,EAAA,CAAA,CAAA,CAAA;AAAS,KAAA,CAAA;AACvC,IAAK,MAAA,YAAkB,GAAA,MAAA;AAAW,MACpC,MAAA,EAAA,IAAA,EAAA,GAAA,KAAA,CAAA;AAEA,MAAA,IAAM,iBAAoB,IAAA,IAAA,CAAA,OAAA;AACxB,QAAI,OAAA;AAAoC,MAAA,IAAA,CAAA,MAAA,GAAA,QAAA,EAAA,GAAA,MAAA,EAAA,CAAA;AAExC,KACE,CAAA;AAKA,IAAA,MAAA,WAAgB,GAAA,MAAA;AAAA,MAClB,IAAO,WAAA,CAAA,KAAA,IAAA,CAAA,MAAA,CAAA,KAAA;AACL,QAAa,OAAA;AAAA,MACf,IAAA,MAAA,CAAA,KAAA,IAAA,CAAA,UAAA,CAAA,KAAA,IAAA,CAAA,aAAA,CAAA,KAAA,IAAA,CAAA,QAAA,CAAA,KAAA,EAAA;AAAA,QACF,WAAA,CAAA,IAAA,CAAA,CAAA;AAEA,OAAM,MAAA;AACJ,QAAA;AACE,OAAA;AACA,KAAI,CAAA;AACF,IAAS,MAAA,iBAAA,GAAA,CAAA,OAAA,KAAA;AAAA,MACX,IAAA,aAAA,CAAA,KAAA,EAAA;AAAA,QACK,OAAA,CAAA,OAAA,CAAA,CAAA;AACL,QAAA,IAAA,KAAA,CAAA,IAAmB,CAAA,MAAA,EAAA;AAAA,UACrB,QAAA,EAAA,CAAA;AAAA,SACF;AAEA,OAAM,MAAA;AACJ,QAAI,WAAO,CAAA,OAAa,CAAA,CAAA;AACtB,OAAO;AAAA,KAAA,CACT;AACE,IAAA,MAAA,WAAe,GAAA,CAAA,OAAA,KAAA;AACf,MAAC,IAAA,CAAA,KAAA,CAAA,IAAA,CAAA;AAAgC,QACnC,MAAA,EAAA,CAAA;AAAA,OACF,MAAA;AAEA,QAAO,OAAA,CAAA,OAAA,CAAA,CAAA;AAAA,QACL,CAAA,aAAA,CAAA,KAAA,IAAA,QAAA,EAAA,CAAA;AAAA,OACA;AAAA,KACA,CAAA;AAAA,IACA,OAAA;AAAA,MACA,KAAA;AAAA,MACA,WAAA;AAAA,MACA,QAAA;AAAA,MACA,aAAA;AAAA,MACA,aAAA;AAAA,MACA,UAAA;AAAA,MACA,MAAA;AAAA,MACA,UAAA;AAAA,MACA,eAAA;AAAA,MACA,aAAA;AAAA,MACA,EAAA;AAAA,MACA,iBAAA;AAAA,MACF,YAAA;AAAA,MACF,WAAA;AACF,MAAC,WAAA;;;;;;;;;;;0BApJM,GAAAC,oBAAA,CAAA,SAAA,CAAA,CAAA;AAAA,EAzDF,MAAE,uBAAoB,GAAAA,oBAAA,CAAA,cAAA,CAAA,CAAA;AAAA,EAAA,MAClB,kBAAA,GAAAA,oBAAA,CAAA,SAAA,CAAA,CAAA;AAAA,EAAA,4BACY,GAAAA,oBAAA,CAAA,aAAA,CAAA,CAAA;AAAA,EAChB,OAAAC,uCAA2B,CAAA,IAAA,EAAA;AAAA,IAC3B,EAAe,EAAA,CAAA,EAAA,IAAA,CAAA,MAAA,CAAA,CAAA,EAAA,IAAA,CAAA,IAAA,CAAA,GAAA,CAAA,CAAA;AAAA,IACf,IAAA,EAAA;AAA4B,IAC5B,eAAK,EAAA,CAAA,IAAA,CAAA,MAAA;AAAA,IAAA,WAAc,EAAA,IAAA,CAAA,MAAA,GAAA,IAAA,GAAA,IAAA,CAAA,MAAA;AAAA,IAAU,eAAK,EAAA,IAAA,CAAA,eAA4B;AAAA,IAAA,QAAY,EAAA,IAAa,CAAA,UAAA,GAAA,CAAA,CAAA,GAAK,KAAO,CAAA;AAAA,IAAS,KAAA,EAAAC,kBAAK,CAAA;AAAwB,MAAS,IAAe,CAAA,EAAA,CAAA,CAAA,EAAA;AAAA,MAA4B,IAAa,CAAA,EAAA,CAAA,EAAA,CAAA,YAAA,EAAA,IAAA,CAAA,aAAA,CAAA;AAAA,MAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,QAAA,EAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA;AAQ1M,MAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,UAAY,EAAA,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,0BACL,IAAA,gBAAA;AAAA,wBACA,IAAA,iBAAA;AAAA,KAAA,CAAA;AAER,IAAA,YAAA,EAAA,MAAA,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,iBAAA,IAAA,IAAA,CAAA,iBAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,IAEQ,+BADR,CAOE,GAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,iBAAA,IAAA,IAAA,CAAA,iBAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,IAAA,OAAA,EAAA,MAAA,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,WAAA,IAAA,IAAA,CAAA,WAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AALC,GAAA,EAAA;AAAkB,IAAAC,sBACE,CAAA,UAAA,CAAA;AAAA,IAAA,IACV,CAAA,QAAA,IAAAF,aAAA,EAAA,EAAAG,eAAA,CAAA,sBAAA,EAAA;AAAA,MACV;AAAD,MAAW,aAAA,EAAA,IAAA,CAAA,IAAA,CAAA,OAAA;AAAA,MACV,aAAoB,EAAA,IAAA,CAAA,IAAA,CAAA,aAAA;AAAA,MAAA,QAAA,EAAA,IAAA,CAAA,UAAA;AAeZ,MAAA,OAAA,EAAA,MAAA,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAAC,iBAAA,CAAA,MAAA;OAXK,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,MACb,qBAAY,EAAA,IAAA,CAAA,iBAAA;AAAA,KAAA,EACF,IAAA,EAAA,CAAA,EAAA,CAAA,aAAA,EAAA,eAAA,EAAA,UAAA,EAAA,qBAAA,CAAA,CAAA,IAAA,IAAA,CAAA,aAAA,IAAAJ,aAAA,EAAA,EAAAG,eAAA,CAAA,mBAAA,EAAA;AAAA,MACV,GAAoB,EAAA,CAAA;AAAA,MACpB,aAAD,EAAA,IAAA,CAAA,aAAA;AAAA,MAAW,KAAA,EAAA,IAAA,CAAA,IAAA,CAAA,GAAA;AAAA,MAAA,QAAA,EAAA,IAAA,CAAA,UAAA;2BAKR,EAAA,IAAA,CAAA,iBAAA;AAAA,MAHH,OAAA,EAAA,MAAA,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAAC,iBAAA,CAAA,MAAA;AAAA,OAIA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,KAAA,EAAA;;sLAIQ,CAAA;AAAA,QAAA,UAAA;OAFwC,CAAA;AAAM,MAAA,CAAA,EAAA,CAAA;wBACtD,EAAS,OAAA,EAAA,UAAA,EAAA,qBAAA,CAAA,CAAA,IAAA,IAAA,CAAA,MAAA,IAAA,IAAA,CAAA,IAAA,CAAA,OAAA,IAAAJ,aAAA,EAAA,EAAAG,eAAA,CAAA,kBAAA,EAAA;AAAA,MAAA,GAAA,EAAA,CAAA;AAAA,MAAA,KAAA,EAAAF,kBAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA;;;AAGX,QAAAI,eAAA,CAAA,gBAAA,CAAA;AAAA,OACgB,CAAA;AAAA,MAEhB,CAAA,EAAA,CAAA;AAAA,KACiB,yCAON,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,IANMH;AAEL,IAAAG,eAAA,CAAA,uBAAA,CAAA;AAFoB,IAAAH,sBAAQ,CAAA,WAAA,CAAA;AAAsB,IAAA,CAAA,IAAA,CAAA,MAAA,IAAAF,aAAA,EAAA,EAAAM,sBAAA,CAAAC,YAAA,EAAA,EAAA,GAAA,EAAA,CAAA,EAAA,EAAA;2BAC/CP,aAAA,EAAA,EAAAG,eAAA,CAAA,kBAAA,EAAA;AAAA,QAAA,GAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAAF,kBAAA,CAAA,CAAA,IAAA,CAAA,EAAA,CAAA,EAAA,CAAA,SAAA,CAAA,EAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA;;;AAIH,UAAAI,eAAA,CAAA,kBAAA,CAAA;SAFY,CAAA;AAAsB,QAAA,CAAA,EAAA,CAAA;2BAC3BL,aAAA,EAAA,EAAAG,eAAA,CAAA,kBAAA,EAAA;AAAA,QAAA,GAAA,EAAA,CAAA;AAAA,QAAA,KAAA,EAAAF,kBAAA,CAAA,CAAA,aAAA,EAAA,IAAA,CAAA,EAAA,CAAA,CAAA,CAAA,SAAA,CAAA,CAAA,CAAA;;;;;;;;;;;;;;"}