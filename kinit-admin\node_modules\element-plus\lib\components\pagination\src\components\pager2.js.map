{"version": 3, "file": "pager2.js", "sources": ["../../../../../../../packages/components/pagination/src/components/pager.vue"], "sourcesContent": ["<template>\n  <ul :class=\"nsPager.b()\" @click=\"onPagerClick\" @keyup.enter=\"onEnter\">\n    <li\n      v-if=\"pageCount > 0\"\n      :class=\"[\n        nsPager.is('active', currentPage === 1),\n        nsPager.is('disabled', disabled),\n      ]\"\n      class=\"number\"\n      :aria-current=\"currentPage === 1\"\n      :aria-label=\"t('el.pagination.currentPage', { pager: 1 })\"\n      :tabindex=\"tabindex\"\n    >\n      1\n    </li>\n    <li\n      v-if=\"showPrevMore\"\n      :class=\"prevMoreKls\"\n      :tabindex=\"tabindex\"\n      :aria-label=\"t('el.pagination.prevPages', { pager: pagerCount - 2 })\"\n      @mouseenter=\"onMouseEnter(true)\"\n      @mouseleave=\"quickPrevHover = false\"\n      @focus=\"onFocus(true)\"\n      @blur=\"quickPrevFocus = false\"\n    >\n      <d-arrow-left v-if=\"(quickPrevHover || quickPrevFocus) && !disabled\" />\n      <more-filled v-else />\n    </li>\n    <li\n      v-for=\"pager in pagers\"\n      :key=\"pager\"\n      :class=\"[\n        nsPager.is('active', currentPage === pager),\n        nsPager.is('disabled', disabled),\n      ]\"\n      class=\"number\"\n      :aria-current=\"currentPage === pager\"\n      :aria-label=\"t('el.pagination.currentPage', { pager })\"\n      :tabindex=\"tabindex\"\n    >\n      {{ pager }}\n    </li>\n    <li\n      v-if=\"showNextMore\"\n      :class=\"nextMoreKls\"\n      :tabindex=\"tabindex\"\n      :aria-label=\"t('el.pagination.nextPages', { pager: pagerCount - 2 })\"\n      @mouseenter=\"onMouseEnter()\"\n      @mouseleave=\"quickNextHover = false\"\n      @focus=\"onFocus()\"\n      @blur=\"quickNextFocus = false\"\n    >\n      <d-arrow-right v-if=\"(quickNextHover || quickNextFocus) && !disabled\" />\n      <more-filled v-else />\n    </li>\n    <li\n      v-if=\"pageCount > 1\"\n      :class=\"[\n        nsPager.is('active', currentPage === pageCount),\n        nsPager.is('disabled', disabled),\n      ]\"\n      class=\"number\"\n      :aria-current=\"currentPage === pageCount\"\n      :aria-label=\"t('el.pagination.currentPage', { pager: pageCount })\"\n      :tabindex=\"tabindex\"\n    >\n      {{ pageCount }}\n    </li>\n  </ul>\n</template>\n<script lang=\"ts\" setup>\nimport { computed, ref, watchEffect } from 'vue'\nimport { DArrowLeft, DArrowRight, MoreFilled } from '@element-plus/icons-vue'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { paginationPagerProps } from './pager'\ndefineOptions({\n  name: 'ElPaginationPager',\n})\nconst props = defineProps(paginationPagerProps)\nconst emit = defineEmits(['change'])\nconst nsPager = useNamespace('pager')\nconst nsIcon = useNamespace('icon')\nconst { t } = useLocale()\n\nconst showPrevMore = ref(false)\nconst showNextMore = ref(false)\nconst quickPrevHover = ref(false)\nconst quickNextHover = ref(false)\nconst quickPrevFocus = ref(false)\nconst quickNextFocus = ref(false)\nconst pagers = computed(() => {\n  const pagerCount = props.pagerCount\n  const halfPagerCount = (pagerCount - 1) / 2\n  const currentPage = Number(props.currentPage)\n  const pageCount = Number(props.pageCount)\n  let showPrevMore = false\n  let showNextMore = false\n  if (pageCount > pagerCount) {\n    if (currentPage > pagerCount - halfPagerCount) {\n      showPrevMore = true\n    }\n    if (currentPage < pageCount - halfPagerCount) {\n      showNextMore = true\n    }\n  }\n  const array: number[] = []\n  if (showPrevMore && !showNextMore) {\n    const startPage = pageCount - (pagerCount - 2)\n    for (let i = startPage; i < pageCount; i++) {\n      array.push(i)\n    }\n  } else if (!showPrevMore && showNextMore) {\n    for (let i = 2; i < pagerCount; i++) {\n      array.push(i)\n    }\n  } else if (showPrevMore && showNextMore) {\n    const offset = Math.floor(pagerCount / 2) - 1\n    for (let i = currentPage - offset; i <= currentPage + offset; i++) {\n      array.push(i)\n    }\n  } else {\n    for (let i = 2; i < pageCount; i++) {\n      array.push(i)\n    }\n  }\n  return array\n})\n\nconst prevMoreKls = computed(() => [\n  'more',\n  'btn-quickprev',\n  nsIcon.b(),\n  nsPager.is('disabled', props.disabled),\n])\nconst nextMoreKls = computed(() => [\n  'more',\n  'btn-quicknext',\n  nsIcon.b(),\n  nsPager.is('disabled', props.disabled),\n])\n\nconst tabindex = computed(() => (props.disabled ? -1 : 0))\nwatchEffect(() => {\n  const halfPagerCount = (props.pagerCount - 1) / 2\n  showPrevMore.value = false\n  showNextMore.value = false\n  if (props.pageCount! > props.pagerCount) {\n    if (props.currentPage > props.pagerCount - halfPagerCount) {\n      showPrevMore.value = true\n    }\n    if (props.currentPage < props.pageCount! - halfPagerCount) {\n      showNextMore.value = true\n    }\n  }\n})\nfunction onMouseEnter(forward = false) {\n  if (props.disabled) return\n  if (forward) {\n    quickPrevHover.value = true\n  } else {\n    quickNextHover.value = true\n  }\n}\nfunction onFocus(forward = false) {\n  if (forward) {\n    quickPrevFocus.value = true\n  } else {\n    quickNextFocus.value = true\n  }\n}\nfunction onEnter(e: UIEvent) {\n  const target = e.target as HTMLElement\n  if (\n    target.tagName.toLowerCase() === 'li' &&\n    Array.from(target.classList).includes('number')\n  ) {\n    const newPage = Number(target.textContent)\n    if (newPage !== props.currentPage) {\n      emit('change', newPage)\n    }\n  } else if (\n    target.tagName.toLowerCase() === 'li' &&\n    Array.from(target.classList).includes('more')\n  ) {\n    onPagerClick(e)\n  }\n}\nfunction onPagerClick(event: UIEvent) {\n  const target = event.target as HTMLElement\n  if (target.tagName.toLowerCase() === 'ul' || props.disabled) {\n    return\n  }\n  let newPage = Number(target.textContent)\n  const pageCount = props.pageCount!\n  const currentPage = props.currentPage\n  const pagerCountOffset = props.pagerCount - 2\n  if (target.className.includes('more')) {\n    if (target.className.includes('quickprev')) {\n      newPage = currentPage - pagerCountOffset\n    } else if (target.className.includes('quicknext')) {\n      newPage = currentPage + pagerCountOffset\n    }\n  }\n  if (!Number.isNaN(+newPage)) {\n    if (newPage < 1) {\n      newPage = 1\n    }\n    if (newPage > pageCount) {\n      newPage = pageCount\n    }\n  }\n  if (newPage !== currentPage) {\n    emit('change', newPage)\n  }\n}\n</script>\n"], "names": ["useNamespace", "useLocale", "ref", "computed", "watchEffect"], "mappings": ";;;;;;;;;;;;;;;;;;uCA2Ec,CAAA;AAAA,EACZ,IAAM,EAAA,mBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAGA,IAAM,MAAA,OAAA,GAAUA,mBAAa,OAAO,CAAA,CAAA;AACpC,IAAM,MAAA,MAAA,GAASA,mBAAa,MAAM,CAAA,CAAA;AAClC,IAAM,MAAA,EAAE,MAAMC,iBAAU,EAAA,CAAA;AAExB,IAAM,MAAA,YAAA,GAAeC,QAAI,KAAK,CAAA,CAAA;AAC9B,IAAM,MAAA,YAAA,GAAeA,QAAI,KAAK,CAAA,CAAA;AAC9B,IAAM,MAAA,cAAA,GAAiBA,QAAI,KAAK,CAAA,CAAA;AAChC,IAAM,MAAA,cAAA,GAAiBA,QAAI,KAAK,CAAA,CAAA;AAChC,IAAM,MAAA,cAAA,GAAiBA,QAAI,KAAK,CAAA,CAAA;AAChC,IAAM,MAAA,cAAA,GAAiBA,QAAI,KAAK,CAAA,CAAA;AAChC,IAAM,MAAA,MAAA,GAASC,aAAS,MAAM;AAC5B,MAAA,MAAM,aAAa,KAAM,CAAA,UAAA,CAAA;AACzB,MAAM,MAAA,cAAA,GAAkB,cAAa,CAAK,IAAA,CAAA,CAAA;AAC1C,MAAM,MAAA,WAAA,GAAc,MAAO,CAAA,KAAA,CAAM,WAAW,CAAA,CAAA;AAC5C,MAAM,MAAA,SAAA,GAAY,MAAO,CAAA,KAAA,CAAM,SAAS,CAAA,CAAA;AACxC,MAAA,IAAI,aAAe,GAAA,KAAA,CAAA;AACnB,MAAA,IAAI,aAAe,GAAA,KAAA,CAAA;AACnB,MAAA,IAAI,YAAY,UAAY,EAAA;AAC1B,QAAI,IAAA,WAAA,GAAc,aAAa,cAAgB,EAAA;AAC7C,UAAe,aAAA,GAAA,IAAA,CAAA;AAAA,SACjB;AACA,QAAI,IAAA,WAAA,GAAc,YAAY,cAAgB,EAAA;AAC5C,UAAe,aAAA,GAAA,IAAA,CAAA;AAAA,SACjB;AAAA,OACF;AACA,MAAA,MAAM,QAAkB,EAAC,CAAA;AACzB,MAAI,IAAA,aAAA,IAAgB,CAAC,aAAc,EAAA;AACjC,QAAM,MAAA,SAAA,GAAY,aAA0B,UAAA,GAAA,CAAA,CAAA,CAAA;AAC5C,QAAA,KAAA,IAAS,CAAI,GAAA,SAAA,EAAW,CAAI,GAAA,SAAA,EAAW,CAAK,EAAA,EAAA;AAC1C,UAAA,KAAA,CAAM,KAAK,CAAC,CAAA,CAAA;AAAA,SACd;AAAA,OACF,MAAA,IAAW,CAAC,aAAA,IAAgB,aAAc,EAAA;AACxC,QAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,UAAA,EAAY,CAAK,EAAA,EAAA;AACnC,UAAA,KAAA,CAAM,KAAK,CAAC,CAAA,CAAA;AAAA,SACd;AAAA,OACF,MAAA,IAAW,iBAAgB,aAAc,EAAA;AACvC,QAAA,MAAM,MAAS,GAAA,IAAA,CAAK,KAAM,CAAA,UAAA,GAAa,CAAC,CAAI,GAAA,CAAA,CAAA;AAC5C,QAAA,KAAA,IAAS,IAAI,WAAc,GAAA,MAAA,EAAQ,CAAK,IAAA,WAAA,GAAc,QAAQ,CAAK,EAAA,EAAA;AACjE,UAAA,KAAA,CAAM,KAAK,CAAC,CAAA,CAAA;AAAA,SACd;AAAA,OACK,MAAA;AACL,QAAA,KAAA,IAAS,CAAI,GAAA,CAAA,EAAG,CAAI,GAAA,SAAA,EAAW,CAAK,EAAA,EAAA;AAClC,UAAA,KAAA,CAAM,KAAK,CAAC,CAAA,CAAA;AAAA,SACd;AAAA,OACF;AACA,MAAO,OAAA,KAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,WAAA,GAAcA,aAAS,MAAM;AAAA,MACjC,MAAA;AAAA,MACA,eAAA;AAAA,MACA,OAAO,CAAE,EAAA;AAAA,MACT,OAAQ,CAAA,EAAA,CAAG,UAAY,EAAA,KAAA,CAAM,QAAQ,CAAA;AAAA,KACtC,CAAA,CAAA;AACD,IAAM,MAAA,WAAA,GAAcA,aAAS,MAAM;AAAA,MACjC,MAAA;AAAA,MACA,eAAA;AAAA,MACA,OAAO,CAAE,EAAA;AAAA,MACT,OAAQ,CAAA,EAAA,CAAG,UAAY,EAAA,KAAA,CAAM,QAAQ,CAAA;AAAA,KACtC,CAAA,CAAA;AAED,IAAA,MAAM,WAAWA,YAAS,CAAA,MAAO,KAAM,CAAA,QAAA,GAAW,KAAK,CAAE,CAAA,CAAA;AACzD,IAAAC,eAAA,CAAY,MAAM;AAChB,MAAM,MAAA,cAAA,GAAkB,CAAM,KAAA,CAAA,UAAA,GAAa,CAAK,IAAA,CAAA,CAAA;AAChD,MAAA,YAAA,CAAa,KAAQ,GAAA,KAAA,CAAA;AACrB,MAAA,YAAA,CAAa,KAAQ,GAAA,KAAA,CAAA;AACrB,MAAI,IAAA,KAAA,CAAM,SAAa,GAAA,KAAA,CAAM,UAAY,EAAA;AACvC,QAAA,IAAI,KAAM,CAAA,WAAA,GAAc,KAAM,CAAA,UAAA,GAAa,cAAgB,EAAA;AACzD,UAAA,YAAA,CAAa,KAAQ,GAAA,IAAA,CAAA;AAAA,SACvB;AACA,QAAA,IAAI,KAAM,CAAA,WAAA,GAAc,KAAM,CAAA,SAAA,GAAa,cAAgB,EAAA;AACzD,UAAA,YAAA,CAAa,KAAQ,GAAA,IAAA,CAAA;AAAA,SACvB;AAAA,OACF;AAAA,KACD,CAAA,CAAA;AACD,IAAA,SAAA,YAAA,CAAsB,UAAU,KAAO,EAAA;AACrC,MAAA,IAAI,KAAM,CAAA,QAAA;AAAU,QAAA,OAAA;AACpB,MAAA,IAAI,OAAS,EAAA;AACX,QAAA,cAAA,CAAe,KAAQ,GAAA,IAAA,CAAA;AAAA,OAClB,MAAA;AACL,QAAA,cAAA,CAAe,KAAQ,GAAA,IAAA,CAAA;AAAA,OACzB;AAAA,KACF;AACA,IAAA,SAAA,OAAA,CAAiB,UAAU,KAAO,EAAA;AAChC,MAAA,IAAI,OAAS,EAAA;AACX,QAAA,cAAA,CAAe,KAAQ,GAAA,IAAA,CAAA;AAAA,OAClB,MAAA;AACL,QAAA,cAAA,CAAe,KAAQ,GAAA,IAAA,CAAA;AAAA,OACzB;AAAA,KACF;AACA,IAAA,SAAA,OAAA,CAAiB,CAAY,EAAA;AAC3B,MAAA,MAAM,SAAS,CAAE,CAAA,MAAA,CAAA;AACjB,MAAA,IACE,MAAO,CAAA,OAAA,CAAQ,WAAY,EAAA,KAAM,IACjC,IAAA,KAAA,CAAM,IAAK,CAAA,MAAA,CAAO,SAAS,CAAA,CAAE,QAAS,CAAA,QAAQ,CAC9C,EAAA;AACA,QAAM,MAAA,OAAA,GAAU,MAAO,CAAA,MAAA,CAAO,WAAW,CAAA,CAAA;AACzC,QAAI,IAAA,OAAA,KAAY,MAAM,WAAa,EAAA;AACjC,UAAA,IAAA,CAAK,UAAU,OAAO,CAAA,CAAA;AAAA,SACxB;AAAA,OAEA,MAAA,IAAA,MAAA,CAAO,OAAQ,CAAA,WAAA,EAAkB,KAAA,IAAA,IACjC,KAAM,CAAA,IAAA,CAAK,MAAO,CAAA,SAAS,CAAE,CAAA,QAAA,CAAS,MAAM,CAC5C,EAAA;AACA,QAAA,YAAA,CAAa,CAAC,CAAA,CAAA;AAAA,OAChB;AAAA,KACF;AACA,IAAA,SAAA,YAAA,CAAsB,KAAgB,EAAA;AACpC,MAAA,MAAM,SAAS,KAAM,CAAA,MAAA,CAAA;AACrB,MAAA,IAAI,OAAO,OAAQ,CAAA,WAAA,EAAkB,KAAA,IAAA,IAAQ,MAAM,QAAU,EAAA;AAC3D,QAAA,OAAA;AAAA,OACF;AACA,MAAI,IAAA,OAAA,GAAU,MAAO,CAAA,MAAA,CAAO,WAAW,CAAA,CAAA;AACvC,MAAA,MAAM,YAAY,KAAM,CAAA,SAAA,CAAA;AACxB,MAAA,MAAM,cAAc,KAAM,CAAA,WAAA,CAAA;AAC1B,MAAM,MAAA,gBAAA,GAAmB,MAAM,UAAa,GAAA,CAAA,CAAA;AAC5C,MAAA,IAAI,MAAO,CAAA,SAAA,CAAU,QAAS,CAAA,MAAM,CAAG,EAAA;AACrC,QAAA,IAAI,MAAO,CAAA,SAAA,CAAU,QAAS,CAAA,WAAW,CAAG,EAAA;AAC1C,UAAA,OAAA,GAAU,WAAc,GAAA,gBAAA,CAAA;AAAA,SACf,MAAA,IAAA,MAAA,CAAO,SAAU,CAAA,QAAA,CAAS,WAAW,CAAG,EAAA;AACjD,UAAA,OAAA,GAAU,WAAc,GAAA,gBAAA,CAAA;AAAA,SAC1B;AAAA,OACF;AACA,MAAA,IAAI,CAAC,MAAA,CAAO,KAAM,CAAA,CAAC,OAAO,CAAG,EAAA;AAC3B,QAAA,IAAI,UAAU,CAAG,EAAA;AACf,UAAU,OAAA,GAAA,CAAA,CAAA;AAAA,SACZ;AACA,QAAA,IAAI,UAAU,SAAW,EAAA;AACvB,UAAU,OAAA,GAAA,SAAA,CAAA;AAAA,SACZ;AAAA,OACF;AACA,MAAA,IAAI,YAAY,WAAa,EAAA;AAC3B,QAAA,IAAA,CAAK,UAAU,OAAO,CAAA,CAAA;AAAA,OACxB;AAAA,KACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}