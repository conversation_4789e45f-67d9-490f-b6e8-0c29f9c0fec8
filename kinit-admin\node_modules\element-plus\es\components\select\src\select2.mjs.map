{"version": 3, "file": "select2.mjs", "sources": ["../../../../../../packages/components/select/src/select.vue"], "sourcesContent": ["<template>\n  <div\n    ref=\"selectRef\"\n    v-click-outside:[popperRef]=\"handleClickOutside\"\n    :class=\"[nsSelect.b(), nsSelect.m(selectSize)]\"\n    @mouseenter=\"states.inputHovering = true\"\n    @mouseleave=\"states.inputHovering = false\"\n    @click.stop=\"toggleMenu\"\n  >\n    <el-tooltip\n      ref=\"tooltipRef\"\n      :visible=\"dropdownMenuVisible\"\n      :placement=\"placement\"\n      :teleported=\"teleported\"\n      :popper-class=\"[nsSelect.e('popper'), popperClass]\"\n      :popper-options=\"popperOptions\"\n      :fallback-placements=\"fallbackPlacements\"\n      :effect=\"effect\"\n      pure\n      trigger=\"click\"\n      :transition=\"`${nsSelect.namespace.value}-zoom-in-top`\"\n      :stop-popper-mouse-event=\"false\"\n      :gpu-acceleration=\"false\"\n      :persistent=\"persistent\"\n      @before-show=\"handleMenuEnter\"\n      @hide=\"states.isBeforeHide = false\"\n    >\n      <template #default>\n        <div\n          ref=\"wrapperRef\"\n          :class=\"[\n            nsSelect.e('wrapper'),\n            nsSelect.is('focused', isFocused),\n            nsSelect.is('hovering', states.inputHovering),\n            nsSelect.is('filterable', filterable),\n            nsSelect.is('disabled', selectDisabled),\n          ]\"\n        >\n          <div\n            v-if=\"$slots.prefix\"\n            ref=\"prefixRef\"\n            :class=\"nsSelect.e('prefix')\"\n          >\n            <slot name=\"prefix\" />\n          </div>\n          <div\n            ref=\"selectionRef\"\n            :class=\"[\n              nsSelect.e('selection'),\n              nsSelect.is(\n                'near',\n                multiple && !$slots.prefix && !!states.selected.length\n              ),\n            ]\"\n          >\n            <slot v-if=\"multiple\" name=\"tag\">\n              <div\n                v-for=\"item in showTagList\"\n                :key=\"getValueKey(item)\"\n                :class=\"nsSelect.e('selected-item')\"\n              >\n                <el-tag\n                  :closable=\"!selectDisabled && !item.isDisabled\"\n                  :size=\"collapseTagSize\"\n                  :type=\"tagType\"\n                  disable-transitions\n                  :style=\"tagStyle\"\n                  @close=\"deleteTag($event, item)\"\n                >\n                  <span :class=\"nsSelect.e('tags-text')\">\n                    {{ item.currentLabel }}\n                  </span>\n                </el-tag>\n              </div>\n\n              <el-tooltip\n                v-if=\"collapseTags && states.selected.length > maxCollapseTags\"\n                ref=\"tagTooltipRef\"\n                :disabled=\"dropdownMenuVisible || !collapseTagsTooltip\"\n                :fallback-placements=\"['bottom', 'top', 'right', 'left']\"\n                :effect=\"effect\"\n                placement=\"bottom\"\n                :teleported=\"teleported\"\n              >\n                <template #default>\n                  <div\n                    ref=\"collapseItemRef\"\n                    :class=\"nsSelect.e('selected-item')\"\n                  >\n                    <el-tag\n                      :closable=\"false\"\n                      :size=\"collapseTagSize\"\n                      :type=\"tagType\"\n                      disable-transitions\n                      :style=\"collapseTagStyle\"\n                    >\n                      <span :class=\"nsSelect.e('tags-text')\">\n                        + {{ states.selected.length - maxCollapseTags }}\n                      </span>\n                    </el-tag>\n                  </div>\n                </template>\n                <template #content>\n                  <div ref=\"tagMenuRef\" :class=\"nsSelect.e('selection')\">\n                    <div\n                      v-for=\"item in collapseTagList\"\n                      :key=\"getValueKey(item)\"\n                      :class=\"nsSelect.e('selected-item')\"\n                    >\n                      <el-tag\n                        class=\"in-tooltip\"\n                        :closable=\"!selectDisabled && !item.isDisabled\"\n                        :size=\"collapseTagSize\"\n                        :type=\"tagType\"\n                        disable-transitions\n                        @close=\"deleteTag($event, item)\"\n                      >\n                        <span :class=\"nsSelect.e('tags-text')\">\n                          {{ item.currentLabel }}\n                        </span>\n                      </el-tag>\n                    </div>\n                  </div>\n                </template>\n              </el-tooltip>\n            </slot>\n            <div\n              v-if=\"!selectDisabled\"\n              :class=\"[\n                nsSelect.e('selected-item'),\n                nsSelect.e('input-wrapper'),\n                nsSelect.is('hidden', !filterable),\n              ]\"\n            >\n              <input\n                :id=\"inputId\"\n                ref=\"inputRef\"\n                v-model=\"states.inputValue\"\n                type=\"text\"\n                :class=\"[nsSelect.e('input'), nsSelect.is(selectSize)]\"\n                :disabled=\"selectDisabled\"\n                :autocomplete=\"autocomplete\"\n                :style=\"inputStyle\"\n                role=\"combobox\"\n                :readonly=\"!filterable\"\n                spellcheck=\"false\"\n                :aria-activedescendant=\"hoverOption?.id || ''\"\n                :aria-controls=\"contentId\"\n                :aria-expanded=\"dropdownMenuVisible\"\n                :aria-label=\"ariaLabel\"\n                aria-autocomplete=\"none\"\n                aria-haspopup=\"listbox\"\n                @focus=\"handleFocus\"\n                @blur=\"handleBlur\"\n                @keydown.down.stop.prevent=\"navigateOptions('next')\"\n                @keydown.up.stop.prevent=\"navigateOptions('prev')\"\n                @keydown.esc.stop.prevent=\"handleEsc\"\n                @keydown.enter.stop.prevent=\"selectOption\"\n                @keydown.delete.stop=\"deletePrevTag\"\n                @compositionstart=\"handleCompositionStart\"\n                @compositionupdate=\"handleCompositionUpdate\"\n                @compositionend=\"handleCompositionEnd\"\n                @input=\"onInput\"\n                @click.stop=\"toggleMenu\"\n              />\n              <span\n                v-if=\"filterable\"\n                ref=\"calculatorRef\"\n                aria-hidden=\"true\"\n                :class=\"nsSelect.e('input-calculator')\"\n                v-text=\"states.inputValue\"\n              />\n            </div>\n            <div\n              v-if=\"shouldShowPlaceholder\"\n              :class=\"[\n                nsSelect.e('selected-item'),\n                nsSelect.e('placeholder'),\n                nsSelect.is(\n                  'transparent',\n                  !hasModelValue || (expanded && !states.inputValue)\n                ),\n              ]\"\n            >\n              <span>{{ currentPlaceholder }}</span>\n            </div>\n          </div>\n          <div ref=\"suffixRef\" :class=\"nsSelect.e('suffix')\">\n            <el-icon\n              v-if=\"iconComponent && !showClose\"\n              :class=\"[nsSelect.e('caret'), nsSelect.e('icon'), iconReverse]\"\n            >\n              <component :is=\"iconComponent\" />\n            </el-icon>\n            <el-icon\n              v-if=\"showClose && clearIcon\"\n              :class=\"[nsSelect.e('caret'), nsSelect.e('icon')]\"\n              @click=\"handleClearClick\"\n            >\n              <component :is=\"clearIcon\" />\n            </el-icon>\n            <el-icon\n              v-if=\"validateState && validateIcon\"\n              :class=\"[nsInput.e('icon'), nsInput.e('validateIcon')]\"\n            >\n              <component :is=\"validateIcon\" />\n            </el-icon>\n          </div>\n        </div>\n      </template>\n      <template #content>\n        <el-select-menu ref=\"menuRef\">\n          <div v-if=\"$slots.header\" :class=\"nsSelect.be('dropdown', 'header')\">\n            <slot name=\"header\" />\n          </div>\n          <el-scrollbar\n            v-show=\"states.options.size > 0 && !loading\"\n            :id=\"contentId\"\n            ref=\"scrollbarRef\"\n            tag=\"ul\"\n            :wrap-class=\"nsSelect.be('dropdown', 'wrap')\"\n            :view-class=\"nsSelect.be('dropdown', 'list')\"\n            :class=\"[nsSelect.is('empty', filteredOptionsCount === 0)]\"\n            role=\"listbox\"\n            :aria-label=\"ariaLabel\"\n            aria-orientation=\"vertical\"\n          >\n            <el-option\n              v-if=\"showNewOption\"\n              :value=\"states.inputValue\"\n              :created=\"true\"\n            />\n            <el-options>\n              <slot />\n            </el-options>\n          </el-scrollbar>\n          <div\n            v-if=\"$slots.loading && loading\"\n            :class=\"nsSelect.be('dropdown', 'loading')\"\n          >\n            <slot name=\"loading\" />\n          </div>\n          <div\n            v-else-if=\"loading || filteredOptionsCount === 0\"\n            :class=\"nsSelect.be('dropdown', 'empty')\"\n          >\n            <slot name=\"empty\">\n              <span>{{ emptyText }}</span>\n            </slot>\n          </div>\n          <div v-if=\"$slots.footer\" :class=\"nsSelect.be('dropdown', 'footer')\">\n            <slot name=\"footer\" />\n          </div>\n        </el-select-menu>\n      </template>\n    </el-tooltip>\n  </div>\n</template>\n\n<script lang=\"ts\">\n// @ts-nocheck\nimport { defineComponent, provide, reactive } from 'vue'\nimport { ClickOutside } from '@element-plus/directives'\nimport ElInput from '@element-plus/components/input'\nimport ElTooltip from '@element-plus/components/tooltip'\nimport ElScrollbar from '@element-plus/components/scrollbar'\nimport ElTag from '@element-plus/components/tag'\nimport ElIcon from '@element-plus/components/icon'\nimport { CHANGE_EVENT, UPDATE_MODEL_EVENT } from '@element-plus/constants'\nimport ElOption from './option.vue'\nimport ElSelectMenu from './select-dropdown.vue'\nimport { useSelect } from './useSelect'\nimport { selectKey } from './token'\nimport ElOptions from './options'\n\nimport { SelectProps } from './select'\nimport type { SelectContext } from './token'\n\nconst COMPONENT_NAME = 'ElSelect'\nexport default defineComponent({\n  name: COMPONENT_NAME,\n  componentName: COMPONENT_NAME,\n  components: {\n    ElInput,\n    ElSelectMenu,\n    ElOption,\n    ElOptions,\n    ElTag,\n    ElScrollbar,\n    ElTooltip,\n    ElIcon,\n  },\n  directives: { ClickOutside },\n  props: SelectProps,\n  emits: [\n    UPDATE_MODEL_EVENT,\n    CHANGE_EVENT,\n    'remove-tag',\n    'clear',\n    'visible-change',\n    'focus',\n    'blur',\n  ],\n\n  setup(props, { emit }) {\n    const API = useSelect(props, emit)\n\n    provide(\n      selectKey,\n      reactive({\n        props,\n        states: API.states,\n        optionsArray: API.optionsArray,\n        handleOptionSelect: API.handleOptionSelect,\n        onOptionCreate: API.onOptionCreate,\n        onOptionDestroy: API.onOptionDestroy,\n        selectRef: API.selectRef,\n        setSelected: API.setSelected,\n      }) as unknown as SelectContext\n    )\n\n    return {\n      ...API,\n    }\n  },\n})\n</script>\n"], "names": ["ElOption", "_createElementBlock", "_normalizeClass", "_createVNode", "_withCtx", "_createElementVNode", "_openBlock", "_renderSlot", "_createCommentVNode", "_Fragment", "_renderList", "_normalizeStyle", "_createBlock", "_withDirectives", "_with<PERSON><PERSON><PERSON>", "_withModifiers", "_vModelText", "_vShow", "_toDisplayString"], "mappings": ";;;;;;;;;;;;;;;;;;AAsRA,MAAM,cAAiB,GAAA,UAAA,CAAA;AACvB,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,cAAA;AAAA,EACN,aAAe,EAAA,cAAA;AAAA,EACf,UAAY,EAAA;AAAA,IACV,OAAA;AAAA,IACA,YAAA;AAAA,cACAA,MAAA;AAAA,IACA,SAAA;AAAA,IACA,KAAA;AAAA,IACA,WAAA;AAAA,IACA,SAAA;AAAA,IACA,MAAA;AAAA,GACF;AAAA,EACA,UAAA,EAAY,EAAE,YAAa,EAAA;AAAA,EAC3B,KAAO,EAAA,WAAA;AAAA,EACP,KAAO,EAAA;AAAA,IACL,kBAAA;AAAA,IACA,YAAA;AAAA,IACA,YAAA;AAAA,IACA,OAAA;AAAA,IACA,gBAAA;AAAA,IACA,OAAA;AAAA,IACA,MAAA;AAAA,GACF;AAAA,EAEA,KAAA,CAAM,KAAO,EAAA,EAAE,IAAQ,EAAA,EAAA;AACrB,IAAM,MAAA,GAAA,GAAM,SAAU,CAAA,KAAA,EAAO,IAAI,CAAA,CAAA;AAEjC,IAAA,OAAA,CACE,WACA,QAAS,CAAA;AAAA,MACP,KAAA;AAAA,MACA,QAAQ,GAAI,CAAA,MAAA;AAAA,MACZ,cAAc,GAAI,CAAA,YAAA;AAAA,MAClB,oBAAoB,GAAI,CAAA,kBAAA;AAAA,MACxB,gBAAgB,GAAI,CAAA,cAAA;AAAA,MACpB,iBAAiB,GAAI,CAAA,eAAA;AAAA,MACrB,WAAW,GAAI,CAAA,SAAA;AAAA,MACf,aAAa,GAAI,CAAA,WAAA;AAAA,KAClB,CACH,CAAA,CAAA;AAEA,IAAO,OAAA;AAAA,MACL,GAAG,GAAA;AAAA,KACL,CAAA;AAAA,GACF;AACF,CAAC,CAAA,CAAA;;;;;;;;;;;;sCApUCC,kBA+PM,CAAA,KAAA,EAAA;AAAA,IA9PJ,GAAI,EAAA,WAAA;AAAA,IAEH,OAAKC,cAAG,CAAA,CAAA,IAAA,CAAA,QAAA,CAAS,GAAK,EAAA,IAAA,CAAA,QAAA,CAAS,EAAE,IAAU,CAAA,UAAA,CAAA,CAAA,CAAA;AAAA,IAC3C,YAAA,EAAU,MAAE,CAAA,EAAA,CAAA,KAAA,MAAA,CAAA,EAAA,CAAA,GAAA,CAAA,MAAA,KAAA,IAAA,CAAA,MAAA,CAAO,aAAa,GAAA,IAAA,CAAA;AAAA,IAChC,YAAA,EAAU,MAAE,CAAA,EAAA,CAAA,KAAA,MAAA,CAAA,EAAA,CAAA,GAAA,CAAA,MAAA,KAAA,IAAA,CAAA,MAAA,CAAO,aAAa,GAAA,KAAA,CAAA;AAAA,IAChC,OAAA,EAAK,uDAAO,IAAU,CAAA,UAAA,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,GAAA,EAAA;IAEvBC,WAsPa,CAAA,qBAAA,EAAA;AAAA,MArPX,GAAI,EAAA,YAAA;AAAA,MACH,OAAS,EAAA,IAAA,CAAA,mBAAA;AAAA,MACT,SAAW,EAAA,IAAA,CAAA,SAAA;AAAA,MACX,UAAY,EAAA,IAAA,CAAA,UAAA;AAAA,MACZ,cAAY,EAAA,CAAG,IAAS,CAAA,QAAA,CAAA,CAAA,CAAC,WAAY,IAAW,CAAA,WAAA,CAAA;AAAA,MAChD,gBAAgB,EAAA,IAAA,CAAA,aAAA;AAAA,MAChB,qBAAqB,EAAA,IAAA,CAAA,kBAAA;AAAA,MACrB,MAAQ,EAAA,IAAA,CAAA,MAAA;AAAA,MACT,IAAA,EAAA,EAAA;AAAA,MACA,OAAQ,EAAA,OAAA;AAAA,MACP,UAAA,EAAU,CAAK,EAAA,IAAA,CAAA,QAAA,CAAS,SAAU,CAAA,KAAA,CAAA,YAAA,CAAA;AAAA,MAClC,yBAAyB,EAAA,KAAA;AAAA,MACzB,kBAAkB,EAAA,KAAA;AAAA,MAClB,UAAY,EAAA,IAAA,CAAA,UAAA;AAAA,MACZ,YAAa,EAAA,IAAA,CAAA,eAAA;AAAA,MACb,MAAA,EAAI,MAAE,CAAA,EAAA,CAAA,KAAA,MAAA,CAAA,EAAA,CAAA,GAAA,CAAA,MAAA,KAAA,IAAA,CAAA,MAAA,CAAO,YAAY,GAAA,KAAA,CAAA;AAAA,KAAA,EAAA;AAEf,MAAA,OAAA,EAAOC,QAChB,MAoLM;AAAA,QApLN,IAoLM,EAAA,CAAA;AAAA,QAAA,OAnLA;AAAA,UACHC,kBAAK,CAAA,KAAA,EAAA;AAAA,YAAgB,iBAAU;AAAA,YAAyB,KAAA,EAAAH,cAAW,CAAA;AAAqB,cAAwB,IAAA,CAAA,QAAA,CAAA,CAAE,CAAa,SAAA,CAAA;AAAoB,cAAe,IAAA,CAAA,QAAW,CAAA,EAAA,CAAA,SAAA,EAAA,IAAyB,CAAA,SAAA,CAAA;AAAA,cAAe,IAAA,CAAA,QAAW,CAAA,EAAA,CAAA,UAAa,EAAc,IAAA,CAAA,MAAA,CAAA,aAAA,CAAA;AAAA,cAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,YAAA,EAAA,IAAA,CAAA,UAAA,CAAA;;AASpP,aAAA,CAAA;AAKF,WAAA,EAAA;YAJJ,IAAI,CAAA,MAAA,CAAA,MAAA,IAAAI,SAAA,EAAA,EAAAL,kBAAA,CAAA,KAAA,EAAA;AAAA,cACH,GAAA,EAAK,CAAE;AAAU,cAAA,GAAA,EAAA,WAAA;cAEI,KAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,QAAA,CAAA,CAAA;AAAA,aAAA,EAAA;cA+IlBK,UAAA,CAAA,IAAA,CAAA,MAAA,EAAA,QAAA,CAAA;AAAA,aA5IA,EAAA,CAAA,CAAA,IAAAC,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,YACHH,kBAAK,CAAA,KAAA,EAAA;AAAA,cAAkB,mBAAU;AAAA,cAA6B,KAAA,EAAAH;;;eAQ/D,CAAA;AAsEO,aAAA,EAAA;wCApDC,CAAA,IAAA,CAAA,MAAA,EAAA,KAAA,EAAA,EAAA,GAAA,EAAA,CAAA,EAAA,EAAA,MAAA;AAAA,iBAfHI,mCAAqB,CAAAG,QAAA,EAAA,IAAA,EAAAC,UAAA,CAAA,IAAA,CAAA,WAAA,EAAA,CAAA,IAAA,KAAA;AAAA,kBACrB,OAAKJ,SAAE,EAAA,EAAAL,kBAAU,CAAA,KAAA,EAAA;AAAA,oBAAA,GAAA,EAAA,IAAA,CAAA,WAAA,CAAA,IAAA,CAAA;oBAaT,KAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,eAAA,CAAA,CAAA;AAAA,mBAAA,EAVE;AAA2B,oBACnCC,WAAM,CAAA,iBAAA,EAAA;AAAA,sBACA,QAAA,EAAA,CAAA,IAAA,CAAA,cAAA,IAAA,CAAA,IAAA,CAAA,UAAA;AAAA,sBACP,IAAA,EAAA,IAAA,CAAA,eAAA;AAAA,sBACC,IAAA;AAAe,sBACV,qBAAY,EAAA,EAAA;AAAY,sBAAA,KAAA,EAAAQ,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA;sCAE9B,KAEO,IAAA,CAAA,SAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,qBAAA,EAAA;AAAA,sBAFA,OAAA,EAAKP,OAAE,CAAA,MAAA;AAAU,wBAAAC,yBACF,EAAA;AAAA,0BAAA,KAAA,EAAAH,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA;;;;;AAMlB,mBAAA,EAAA,CAAA,CAAA,CAAA;AAgDK,iBAAA,CAAA,EAAA,GAAA,CAAA;gBA/CX,IAAI,CAAA,YAAA,IAAA,IAAA,CAAA,MAAA,CAAA,QAAA,CAAA,MAAA,GAAA,IAAA,CAAA,eAAA,IAAAI,SAAA,EAAA,EAAAM,WAAA,CAAA,qBAAA,EAAA;AAAA,kBACH,GAAA,EAAA,CAAA;AAAkC,kBACb,GAAA,EAAA,eAAA;AAAA,kBACb,QAAA,EAAA,IAAA,CAAA,mBAAA,IAAA,CAAA,IAAA,CAAA,mBAAA;AAAA,kBACC,qBAAA,EAAA,CAAA,QAAA,EAAA,KAAA,EAAA,OAAA,EAAA,MAAA,CAAA;AAAA,kBACG,MAAA,EAAA,IAAA,CAAA,MAAA;AAAA,kBAAA,SAAA,EAAA,QAAA;AAEF,kBAAA,gBACT,CAeM,UAAA;AAAA,iBAAA,EAAA;AAAA,kBAAA,OAdA,EAAAR,OAAA,CAAA,MAAA;AAAA,oBACHC,kBAAO,CAAA,KAAA,EAAA;AAAU,sBAAA,GAAA,EAAA,iBAAA;sBAYT,KAAA,EAAAH,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,eAAA,CAAA,CAAA;AAAA,qBAAA,EATI;AAAA,sBACVC,WAAM,CAAA,iBAAA,EAAA;AAAA,wBACA,QAAA,EAAA,KAAA;AAAA,wBACP,IAAA,EAAA,IAAA,CAAA,eAAA;AAAA,wBACC,IAAA;AAAuB,wBAAA,qBAAA,EAAA,EAAA;6CAIjB,CAAA,IAAA,CAAA,gBAAA,CAAA;AAAA,uBAAA,EAAA;AAAA,wBAFA,OAAA,EAAKC,OAAE,CAAA,MAAA;AAAU,0BAAAC,kBACpB,CAAA,MAAG,EAAO;AAAiC,4BAAA,KAAA,EAAAH,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA;;;;;AAK1C,qBAAA,EAAA,CAAA,CAAO;AAoBV,mBAAA,CAAA;AAAA,kBAAA,OAnBG,EAAAE,OAAA,CAAA,MAAA;AAAA,oBAAcC,kBAAO,CAAA,KAAA,EAAA;AAAU,sBAAA,GAAA,EAAA,YAAA;2CAkBhC,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA;;AAAA,uBAfHC,mCAAqB,CAAAG,QAAA,EAAA,IAAA,EAAAC,UAAA,CAAA,IAAA,CAAA,eAAA,EAAA,CAAA,IAAA,KAAA;AAAA,wBACrB,OAAKJ,SAAE,EAAA,EAAAL,kBAAU,CAAA,KAAA,EAAA;AAAA,0BAAA,GAAA,EAAA,IAAA,CAAA,WAAA,CAAA,IAAA,CAAA;0BAaT,KAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,eAAA,CAAA,CAAA;AAAA,yBAAA,EAVD;AAAA,0BACLC,WAAyB,CAAA,iBAAA,EAAA;AAAU,4BAC7B,KAAA,EAAA,YAAA;AAAA,4BACA,QAAA,EAAA,CAAA,IAAA,CAAA,cAAA,IAAA,CAAA,IAAA,CAAA,UAAA;AAAA,4BACP,IAAA,EAAA,IAAA,CAAA,eAAA;AAAA,4BACM,IAAA,EAAA,IAAA,CAAA,OAAA;AAAwB,4BAAA,qBAAA,EAAA,EAAA;4CAE9B,KAEO,IAAA,CAAA,SAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,2BAAA,EAAA;AAAA,4BAFA,OAAA,EAAKC,OAAE,CAAA,MAAA;AAAU,8BAAAC,yBACF,EAAA;AAAA,gCAAA,KAAA,EAAAH,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,WAAA,CAAA,CAAA;;;;;;;;;;AASzB,iBAAA,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,QAAA,EAAA,YA6CH,CAAA,CAAA,IAAAM,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,eAAA,CAAA,GAAAA,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;cA5CH,CAAK,IAAA,CAAA,cAAA,IAAAF,SAAA,EAAA,EAAAL,kBAAA,CAAA,KAAA,EAAA;AAAA,gBAAoB;AAAU,gBAAmC,qBAAU,CAAA;AAAA,kBAAmC,IAAA,CAAA,QAAW,CAAA,CAAA,CAAA,eAAsB,CAAA;AAAA,kBAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,eAAA,CAAA;;;AAoCnJ,eAAA,EA7BK;AAAA,gBACLY,cAAI,CAAAR,kBAAA,CAAA,OAAA,EAAA;AAAA,kBAAA,EAAA,EAAA,IAAA,CAAA,OAAA;AACsB,kBACrB,GAAA,EAAA,UAAA;AAAA,uCACI,EAAA,MAAA,CAAA,CAAA,CAAA,WAAqB,CAAA,CAAA,CAAA,GAAA,CAAA,MAAA,KAAA,IAAY,CAAU,MAAA,CAAA,UAAA,GAAA,MAAA,CAAA;AAAA,kBACzC,IAAA,EAAA,MAAA;AAAA,kBACI,KAAA,EAAAH,cAAA,CAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,IAAA,CAAA,UAAA,CAAA,CAAA,CAAA;AAAA,kBACd,6BAAiB;AAAA,kBACb,YAAA,EAAA,IAAA,CAAA,YAAA;AAAA,uCACO,CAAA,IAAA,CAAA,UAAA,CAAA;AAAA,kBACD,IAAA,EAAA,UAAA;AAAA,kBACV,QAAA,EAAA,CAAA,IAAA,CAAA;AAAsC,kBACvB,UAAA,EAAA,OAAA;AAAA,kBACA,uBAAA,EAAA,CAAA,CAAA,EAAA,GAAA,IAAA,CAAA,WAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,EAAA,KAAA,EAAA;AAAA,kBACH,eAAA,EAAA,IAAA,CAAA,SAAA;AAAA,kBACK,eAAA,EAAA,IAAA,CAAA,mBAAA;AAAA,kBACJ,YAAA,EAAA,IAAA,CAAA,SAAA;AAAA,qCACN,EAAA,MAAA;AAAA,iCACD,EAAA,SAAA;AAAA,kBACC,OAAA,EAAA,MAAA,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,WAAA,IAAA,IAAA,CAAA,WAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,kBAAA,MAAA,EAAA,MAAA,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,UAAA,IAAmC,IAAA,CAAA,UAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,kBAAA,SAAA,EAAA;AACF,oBAAA,MAAA,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAAY,QAAA,CAAAC,aAAA,CAAA,CAAA,MAAA,KACL,IAAA,CAAA,eAAA,CAAA,MAAA,CAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,oBAAA,MAAA,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAAD,QAAA,CAAAC,aAAA,CAAA,CAAA,MAAA,KACK,IAAA,CAAA,eAAA,CAAA,MAAA,CAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,IAAA,CAAA,CAAA,CAAA;AAAA,oBAAA,MAAA,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAAD,QAAA,CAAAC,aAAA,CAAA,CAAA,GAAA,IAAA,KACN,IAAA,CAAA,SAAA,IAAA,IAAA,CAAA,SAAA,CAAA,GAAA,IAAA,CAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,oBAAA,MAAA,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAAD,QAAA,CAAAC,aAAA,CAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,YAAA,IAAA,IAAA,CAAA,YAAA,CAAA,GAAA,IAAA,CAAA,EAAA,CAAA,MAAA,EAAA,SAAA,CAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AAClC,oBAAA,MAAA,CAAA,CAAA,CAAA,KAAA,MAAkB,CAAA,CAAA,CAAA,GAAAD,QAAA,CAAAC,aAAA,CAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,aAAA,IAAA,IAAA,CAAA,aAAA,CAAA,GAAA,IAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,EAAA,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA;AACC,oCACH,EAAA,MAAA,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,sBAAA,IAAA,IAAA,CAAA,sBAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,qCACT,EAAA,MAAA,CAAA,CAAA,CAAA,KAAA,MAAA,CAAA,CAAA,CAAA,GAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,uBAAA,IAAA,IAAA,CAAA,uBAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,kBACP,+DAAY,CAAU,oBAAA,IAAA,IAAA,CAAA,oBAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AAAA,kBAAA,OAAA,EAAA,MAAA,CAAA,EAAA,CAAA,KAAA,MAAA,CAAA,EAAA,CAAA,GAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,OAAA,IAAA,IAAA,CAAA,OAAA,CAAA,GAAA,IAAA,CAAA,CAAA;AA1Bd,kBAAA,OAAA,EAAA,MAAA,CAAA,EAAA,CAAA,KAAO,MAAU,CAAA,EAAA,CAAA,GAAAA,aAAA,CAAA,CAAA,GAAA,IAAA,KAAA,IAAA,CAAA,UAAA,IAAA,IAAA,CAAA,UAAA,CAAA,GAAA,IAAA,CAAA,EAAA,CAAA,MAAA,CAAA,CAAA,CAAA;AAAA,iBAAA,EAAA,IAAA,EAAA,EAAA,EAAA,UAAA,CAAA,EAAA;AA6BpB,kBAAA,CAAAC,UAAA,EAAA,IAAA,CAAA,MAAA,CAAA,UAKN,CAAA;AAAA,iBAAA,CAAA;gBAJA,IAAI,CAAA,UAAA,IAAAV,SAAA,EAAA,EAAAL,kBAAA,CAAA,MAAA,EAAA;AAAA,kBACQ,GAAA,EAAA,CAAA;AAAA,kBACX,GAAA,EAAK,eAAE;AAAU,kBAClB,aAAA,EAAA,MAAA;AAAyB,kBAAA,KAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,kBAAA,CAAA,CAAA;;AAIrB,iBAAA,EAAA,IAAA,EAAA,EAAA,EAAA,UAAA,CAAA,IAAAM,kBAWF,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,eAAA,EAAA,CAAA,CAAA,IAAAA,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;cAVH,IAAK,CAAA,qBAAA,IAAAF,SAAA,EAAA,EAAAL,kBAAA,CAAA,KAAA,EAAA;AAAA,gBAAoB;AAAU,gBAAmC,qBAAU,CAAA;AAAA,kBAA0C,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,eAAwD,CAAkB;;;AASrM,iBAAA,CAAA;AAA2B,eAAA,EAAA;;eAuBzB,EAAA,CAAA,CAAA,IAAAO,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,aApBG,EAAA,CAAA,CAAA;AAAA,YAAaH,kBAAO,CAAA,KAAA,EAAA;AAAU,cAAA,GAAA,EAAA,WAAA;cAEhB,KAAA,EAAAH;AAIX,aAAA,EAAA;AAHP,cAAA,IAAA,CAAA,sBAAiB,CAAA,SAAA,aAAqB,EAAA,EAAAU,8BAAsB,EAAA;AAAA,gBAAA,GAAA,EAAA,CAAA;qCAE5B,CAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,MAAA,CAAA,EAAA,IAAA,CAAA,WAAA,CAAA,CAAA;AAAA,eAAjC,EAAA;AAA6B,gBAAA,OAAA,EAAAR,OAAA,CAAA,MAAA;;;AAGvB,gBAAA,CAAA,EAAA,CAAA;AAKE,eAAA,EAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,IAAAI,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAJP,cAAA,IAAA,CAAA,SAAQ,IAAA,IAAA,CAAA,SAAA,IAAAF,SAAU,EAAA,EAAWM,8BAAU,EAAA;AAAA,gBAChC,GAAA,EAAA,CAAA;AAAA,gBAAA,KAAA,EAAAV,cAAA,CAAA,CAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,OAAA,CAAA,EAAA,IAAA,CAAA,QAAA,CAAA,CAAA,CAAA,MAAA,CAAA,CAAA,CAAA;8CAEqB;AAAA,eAA7B,EAAA;AAAyB,gBAAA,OAAA,EAAAE,OAAA,CAAA,MAAA;;;AAGnB,gBAAA,CAAA,EAAA,CAAA;AAIE,eAAA,EAAA,CAAA,EAAA,CAAA,OAAA,EAAA,SAAA,CAAA,CAAA,IAAAI,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAHP,cAAA,IAAA,CAAA,aAAQ,IAAA,IAAA,CAAA,YAAS,IAAAF,wBAAmB,CAAA,kBAAA,EAAA;AAAA,gBAAA,GAAA,EAAA,CAAA;qCAEL,CAAA,CAAA,IAAA,CAAA,OAAA,CAAA,CAAA,CAAA,MAAA,CAAA,EAAA,IAAA,CAAA,OAAA,CAAA,CAAA,CAAA,cAAA,CAAA,CAAA,CAAA;AAAA,eAAhC,EAAA;AAA4B,gBAAA,OAAA,EAAAF,OAAA,CAAA,MAAA;;;;;;AAKzB,WAAA,EAAA,CAAA,CAAO;AA2CC,SA1CjB,CAAA;AAA6B,OAAA,CAAA;AAGrB,MAFK,OAAA,EAAAA,OAAA,CAAO;AAEZ,QAAAD,WAAA,CAAA,yBAAA,EAAA,EAAA,GAAA,EAAA,SAAA,EAAA,EAAA;iBAF0B,EAAAC,OAAA,CAAA,MAAA;AAAa,YAAA,IAAA,CAAA,MAAA,CAAA,MAAA,IAAAE,SAAA,EAAA,EAAAL,kBAAA,CAAA,KAAA,EAAA;cAC3C,GAAsB,EAAA,CAAA;AAAA,cAAA,KAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA;;AAsBT,cAlBZK,UAAI,CAAA,IAAA,CAAA,MAAA,EAAA,QAAA,CAAA;AAAA,aAAA,EACD,CAAA,CAAA,IAAAC,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,YAAAK,cACA,CAAAV,WAAA,CAAA,uBAAA,EAAA;AAAA,cACH,EAAA,EAAA,IAAA,CAAA;AAAuB,cACvB,GAAA,EAAA;AAAuB,cACvB,GAAK,EAAA,IAAA;AAA4C,cAClD,YAAK,EAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,MAAA,CAAA;AAAA,cACJ,YAAY,EAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,MAAA,CAAA;AAAA,cACb,KAAiB,EAAAD,cAAA,CAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,OAAA,EAAA,IAAA,CAAA,oBAAA,KAAA,CAAA,CAAA,CAAA,CAAA;AAAA,cAAA,IAAA,EAAA,SAAA;gCAEjB,CAIE,SAAA;AAAA,cAAA;AAAA,aAAA,EAAA;AAFC,cAAA,OAAA,EAAAE,OAAc,CAAA,MAAA;AAAA,gBAAA,IACL,CAAA,aAAA,IAAAE,SAAA,EAAA,EAAAM,WAAA,CAAA,oBAAA,EAAA;AAAA,kBAAA,GAAA,EAAA,CAAA;kBAIC,KAAA,EAAA,IAAA,CAAA,MAAA,CAAA,UAAA;AAAA,kBAAA,OAAA,EAAA,IAAA;AADH,iBAAA,EAAA,IAAA,EAAA,CAAA,EAAA,CAAA,OAAA,CAAA,CAAA,IAAAJ,kBAAA,CAAA,MAAA,EAAA,IAAA,CAAA;AAAA,gBAAAL,WAAA,CAAA,qBAAA,EAAA,IAAA,EAAA;;;;;;;AAjBiC,cAAA,CAAA,EAAA,CAAA;aAqB9B,EAAA,CAAA,EAAA,CAAA,IAAA,EAAA,iDAIT,CAAA,CAAA,EAAA;AAAA,cAAA,CAAAc,KAAA,EAAA,IAAA,CAAA,MAAA,CAAA,OAAA,CAAA,IAAA,GAAA,CAAA,IAAA,CAAA,IAAA,CAAA,OAAA,CAAA;cAHH;AAAkB,YAAA,IAAA,CAAA,MAAA,CAAA,OAAA,IAAA,IAAA,CAAA,OAAA,IAAAX,SAAA,EAAA,EAAAL,kBAAA,CAAA,KAAA,EAAA;cAEnB,GAAuB,EAAA,CAAA;AAAA,cAGZ,KAAA,EAAAC,cAAA,CAAW,IAAoB,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,SAAA,CAAA,CAAA;AAMtC,aAAA,EAAA;cALHK,UAAK,CAAA,IAAA,CAAA,MAAE;AAAW,aAAA,EAAA,CAAA,CAAA,IAAA,IAAA,CAAA,OAAA,IAAA,IAAA,CAAA,oBAAA,KAAA,CAAA,IAAAD,SAAA,EAAA,EAAAL,kBAAA,CAAA,KAAA,EAAA;AAEnB,cAAA,GAAA,EAAA,CAAA;AAEO,cADL,KAAA,EAAAC,cAA4B,qCAAV,CAAA,CAAA;AAAA,aAAA,EAAA;;AAGX,gBAAAG,+BAAX,EAEMa,eAAA,CAAA,IAAA,CAAA,SAAA,CAAA,EAAA,CAAA,CAAA;AAAA,eAAA,CAAA;eAF0B,CAAA,CAAA,IAAAV,+BAAa,CAAA;AAAA,YAAA,IAAA,CAAA,MAAA,CAAA,MAAA,IAAAF,SAAA,EAAA,EAAAL,kBAAA,CAAA,KAAA,EAAA;cAC3C,GAAsB,EAAA,CAAA;AAAA,cAAA,KAAA,EAAAC,cAAA,CAAA,IAAA,CAAA,QAAA,CAAA,EAAA,CAAA,UAAA,EAAA,QAAA,CAAA,CAAA;;;;;;;;AAxPD,MAAA,CAAA,EAAA,CAAA;AAAF,KAAA,EAAA,CAAA,EAAA,CAAA,SAAA,EAAA,WAAA,EAAA,YAAA,EAAA,cAAA,EAAA,gBAAA,EAAA,qBAAA,EAAA,QAAA,EAAA,YAAA,EAAA,YAAA,EAAA,cAAA,CAAA,CAAA;;;;;;;;;"}