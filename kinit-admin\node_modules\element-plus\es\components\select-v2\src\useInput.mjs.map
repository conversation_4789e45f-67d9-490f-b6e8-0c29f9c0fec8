{"version": 3, "file": "useInput.mjs", "sources": ["../../../../../../packages/components/select-v2/src/useInput.ts"], "sourcesContent": ["// @ts-nocheck\nimport { ref } from 'vue'\nimport { isFunction } from '@vue/shared'\nimport { isKorean } from '@element-plus/utils'\n\nexport function useInput(handleInput: (event: InputEvent) => void) {\n  const isComposing = ref(false)\n\n  const handleCompositionStart = () => {\n    isComposing.value = true\n  }\n\n  const handleCompositionUpdate = (event) => {\n    const text = event.target.value\n    const lastCharacter = text[text.length - 1] || ''\n    isComposing.value = !isKorean(lastCharacter)\n  }\n\n  const handleCompositionEnd = (event) => {\n    if (isComposing.value) {\n      isComposing.value = false\n      if (isFunction(handleInput)) {\n        handleInput(event)\n      }\n    }\n  }\n\n  return {\n    handleCompositionStart,\n    handleCompositionUpdate,\n    handleCompositionEnd,\n  }\n}\n"], "names": [], "mappings": ";;;;;AAGO,SAAS,QAAQ,CAAC,WAAW,EAAE;AACtC,EAAE,MAAM,WAAW,GAAG,GAAG,CAAC,KAAK,CAAC,CAAC;AACjC,EAAE,MAAM,sBAAsB,GAAG,MAAM;AACvC,IAAI,WAAW,CAAC,KAAK,GAAG,IAAI,CAAC;AAC7B,GAAG,CAAC;AACJ,EAAE,MAAM,uBAAuB,GAAG,CAAC,KAAK,KAAK;AAC7C,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC;AACpC,IAAI,MAAM,aAAa,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;AACtD,IAAI,WAAW,CAAC,KAAK,GAAG,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;AACjD,GAAG,CAAC;AACJ,EAAE,MAAM,oBAAoB,GAAG,CAAC,KAAK,KAAK;AAC1C,IAAI,IAAI,WAAW,CAAC,KAAK,EAAE;AAC3B,MAAM,WAAW,CAAC,KAAK,GAAG,KAAK,CAAC;AAChC,MAAM,IAAI,UAAU,CAAC,WAAW,CAAC,EAAE;AACnC,QAAQ,WAAW,CAAC,KAAK,CAAC,CAAC;AAC3B,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AACJ,EAAE,OAAO;AACT,IAAI,sBAAsB;AAC1B,IAAI,uBAAuB;AAC3B,IAAI,oBAAoB;AACxB,GAAG,CAAC;AACJ;;;;"}