{"version": 3, "file": "step2.js", "sources": ["../../../../../../packages/components/tour/src/step.vue"], "sourcesContent": ["<template>\n  <button\n    v-if=\"mergedShowClose\"\n    aria-label=\"Close\"\n    :class=\"ns.e('closebtn')\"\n    type=\"button\"\n    @click=\"onClose\"\n  >\n    <el-icon :class=\"ns.e('close')\">\n      <component :is=\"mergedCloseIcon\" />\n    </el-icon>\n  </button>\n  <header :class=\"[ns.e('header'), { 'show-close': showClose }]\">\n    <slot name=\"header\">\n      <span role=\"heading\" :class=\"ns.e('title')\">\n        {{ title }}\n      </span>\n    </slot>\n  </header>\n  <div :class=\"ns.e('body')\">\n    <slot>\n      <span>{{ description }}</span>\n    </slot>\n  </div>\n  <footer :class=\"ns.e('footer')\">\n    <div :class=\"ns.b('indicators')\">\n      <component\n        :is=\"tourSlots.indicators\"\n        v-if=\"tourSlots.indicators\"\n        :current=\"current\"\n        :total=\"total\"\n      />\n      <template v-else>\n        <span\n          v-for=\"(item, index) in total\"\n          :key=\"item\"\n          :class=\"[ns.b('indicator'), index === current ? 'is-active' : '']\"\n        />\n      </template>\n    </div>\n    <div :class=\"ns.b('buttons')\">\n      <el-button\n        v-if=\"current > 0\"\n        size=\"small\"\n        :type=\"mergedType\"\n        v-bind=\"filterButtonProps(prevButtonProps)\"\n        @click=\"onPrev\"\n      >\n        {{ prevButtonProps?.children ?? t('el.tour.previous') }}\n      </el-button>\n      <el-button\n        v-if=\"current <= total - 1\"\n        size=\"small\"\n        :type=\"mergedType === 'primary' ? 'default' : 'primary'\"\n        v-bind=\"filterButtonProps(nextButtonProps)\"\n        @click=\"onNext\"\n      >\n        {{\n          nextButtonProps?.children ??\n          (current === total - 1 ? t('el.tour.finish') : t('el.tour.next'))\n        }}\n      </el-button>\n    </div>\n  </footer>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, inject, watch } from 'vue'\nimport { omit } from 'lodash-unified'\nimport { ElButton } from '@element-plus/components/button'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { CloseComponents } from '@element-plus/utils'\nimport { useLocale } from '@element-plus/hooks'\nimport { tourStepEmits, tourStepProps } from './step'\nimport { tourKey } from './helper'\n\nimport type { TourBtnProps } from './types'\n\ndefineOptions({\n  name: 'ElTourStep',\n})\n\nconst props = defineProps(tourStepProps)\nconst emit = defineEmits(tourStepEmits)\n\nconst { Close } = CloseComponents\n\nconst { t } = useLocale()\n\nconst {\n  currentStep,\n  current,\n  total,\n  showClose,\n  closeIcon,\n  mergedType,\n  ns,\n  slots: tourSlots,\n  updateModelValue,\n  onClose: tourOnClose,\n  onFinish: tourOnFinish,\n  onChange,\n} = inject(tourKey)!\n\nwatch(\n  props,\n  (val) => {\n    currentStep.value = val\n  },\n  {\n    immediate: true,\n  }\n)\n\nconst mergedShowClose = computed(() => props.showClose ?? showClose.value)\nconst mergedCloseIcon = computed(\n  () => props.closeIcon ?? closeIcon.value ?? Close\n)\n\nconst filterButtonProps = (btnProps?: TourBtnProps) => {\n  if (!btnProps) return\n  return omit(btnProps, ['children', 'onClick'])\n}\n\nconst onPrev = () => {\n  current.value -= 1\n  if (props.prevButtonProps?.onClick) {\n    props.prevButtonProps?.onClick()\n  }\n  onChange()\n}\n\nconst onNext = () => {\n  if (current.value >= total.value - 1) {\n    onFinish()\n  } else {\n    current.value += 1\n  }\n  if (props.nextButtonProps?.onClick) {\n    props.nextButtonProps.onClick()\n  }\n  onChange()\n}\n\nconst onFinish = () => {\n  onClose()\n  tourOnFinish()\n}\n\nconst onClose = () => {\n  updateModelValue(false)\n  tourOnClose()\n  emit('close')\n}\n</script>\n"], "names": ["CloseComponents", "useLocale", "inject", "tourKey", "watch", "computed", "omit"], "mappings": ";;;;;;;;;;;;;;;;uCA8Ec,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAA,MAAM,EAAE,KAAU,EAAA,GAAAA,oBAAA,CAAA;AAElB,IAAM,MAAA,EAAE,MAAMC,eAAU,EAAA,CAAA;AAExB,IAAM,MAAA;AAAA,MACJ,WAAA;AAAA,MACA,OAAA;AAAA,MACA,KAAA;AAAA,MACA,SAAA;AAAA,MACA,SAAA;AAAA,MACA,UAAA;AAAA,MACA,EAAA;AAAA,MACA,KAAO,EAAA,SAAA;AAAA,MACP,gBAAA;AAAA,MACA,OAAS,EAAA,WAAA;AAAA,MACT,QAAU,EAAA,YAAA;AAAA,MACV,QAAA;AAAA,KAAA,GACEC,WAAOC,cAAO,CAAA,CAAA;AAElB,IACEC,SAAA,CAAA,KAAA,EACA,CAAC,GAAQ,KAAA;AACP,MAAA,WAAA,CAAY,KAAQ,GAAA,GAAA,CAAA;AAAA,KAEtB,EAAA;AAAA,MACE,SAAW,EAAA,IAAA;AAAA,KAEf,CAAA,CAAA;AAEA,IAAA,MAAM,kBAAkBC,YAAS,CAAA,MAAM;AACvC,MAAA,IAAM;AAIN,MAAM,OAAA,CAAA,EAAA,GAAA,KAAA,CAAA,SAAiD,KAAA,IAAA,GAAA,EAAA,GAAA,SAAA,CAAA,KAAA,CAAA;AACrD,KAAA,CAAA,CAAA;AAAe,IAAA,MAAA,eAAA,GAAAA,YAAA,CAAA,MAAA;AACf,MAAA,IAAA,EAAA,EAAY,EAAA,CAAA;AAAiC,MAC/C,OAAA,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,SAAA,KAAA,IAAA,GAAA,EAAA,GAAA,SAAA,CAAA,KAAA,KAAA,IAAA,GAAA,EAAA,GAAA,KAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AACE,IAAA,MAAA,iBAAiB,GAAA,CAAA,QAAA,KAAA;AACjB,MAAI,IAAA,CAAA;AACF,QAAA;AAA+B,MACjC,OAAAC,kBAAA,CAAA,QAAA,EAAA,CAAA,UAAA,EAAA,SAAA,CAAA,CAAA,CAAA;AACA,KAAS,CAAA;AAAA,IACX,MAAA,MAAA,GAAA,MAAA;AAEA,MAAA,IAAM;AACJ,MAAA,OAAY,CAAA,KAAA,IAAA,CAAA,CAAA;AACV,MAAS,IAAA,CAAA,EAAA,GAAA,KAAA,CAAA,eAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,EAAA;AAAA,QACJ,CAAA,EAAA,GAAA,KAAA,CAAA,eAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,EAAA,CAAA;AACL,OAAA;AAAiB,MACnB,QAAA,EAAA,CAAA;AACA,KAAI,CAAA;AACF,IAAA,MAAA;AAA8B,MAChC,IAAA,EAAA,CAAA;AACA,MAAS,IAAA,OAAA,CAAA,KAAA,IAAA,KAAA,CAAA,KAAA,GAAA,CAAA,EAAA;AAAA,QACX,QAAA,EAAA,CAAA;AAEA,OAAA;AACE,QAAQ,OAAA,CAAA,KAAA,IAAA,CAAA,CAAA;AACR,OAAa;AAAA,MACf,IAAA,CAAA,EAAA,GAAA,KAAA,CAAA,eAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,OAAA,EAAA;AAEA,QAAA,qBAAsB,CAAA,OAAA,EAAA,CAAA;AACpB,OAAA;AACA,MAAY,QAAA,EAAA,CAAA;AACZ,KAAA,CAAA;AAAY,IACd,MAAA,QAAA,GAAA,MAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}