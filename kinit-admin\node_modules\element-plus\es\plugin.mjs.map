{"version": 3, "file": "plugin.mjs", "sources": ["../../../packages/element-plus/plugin.ts"], "sourcesContent": ["import { ElInfiniteScroll } from '@element-plus/components/infinite-scroll'\nimport { ElLoading } from '@element-plus/components/loading'\nimport { ElMessage } from '@element-plus/components/message'\nimport { ElMessageBox } from '@element-plus/components/message-box'\nimport { ElNotification } from '@element-plus/components/notification'\nimport { ElPopoverDirective } from '@element-plus/components/popover'\n\nimport type { Plugin } from 'vue'\n\nexport default [\n  ElInfiniteScroll,\n  ElLoading,\n  ElMessage,\n  ElMessageBox,\n  ElNotification,\n  ElPopoverDirective,\n] as Plugin[]\n"], "names": [], "mappings": ";;;;;;;AAMA,cAAe;AACf,EAAE,gBAAgB;AAClB,EAAE,SAAS;AACX,EAAE,SAAS;AACX,EAAE,YAAY;AACd,EAAE,cAAc;AAChB,EAAE,kBAAkB;AACpB,CAAC;;;;"}