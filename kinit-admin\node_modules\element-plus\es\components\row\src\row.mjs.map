{"version": 3, "file": "row.mjs", "sources": ["../../../../../../packages/components/row/src/row.ts"], "sourcesContent": ["import { buildProps } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type Row from './row.vue'\n\nexport const RowJustify = [\n  'start',\n  'center',\n  'end',\n  'space-around',\n  'space-between',\n  'space-evenly',\n] as const\n\nexport const RowAlign = ['top', 'middle', 'bottom'] as const\n\nexport const rowProps = buildProps({\n  /**\n   * @description custom element tag\n   */\n  tag: {\n    type: String,\n    default: 'div',\n  },\n  /**\n   * @description grid spacing\n   */\n  gutter: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description horizontal alignment of flex layout\n   */\n  justify: {\n    type: String,\n    values: RowJustify,\n    default: 'start',\n  },\n  /**\n   * @description vertical alignment of flex layout\n   */\n  align: {\n    type: String,\n    values: RowAlign,\n  },\n} as const)\n\nexport type RowProps = ExtractPropTypes<typeof rowProps>\nexport type RowInstance = InstanceType<typeof Row>\n"], "names": [], "mappings": ";;;AACY,MAAC,UAAU,GAAG;AAC1B,EAAE,OAAO;AACT,EAAE,QAAQ;AACV,EAAE,KAAK;AACP,EAAE,cAAc;AAChB,EAAE,eAAe;AACjB,EAAE,cAAc;AAChB,EAAE;AACU,MAAC,QAAQ,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE;AACxC,MAAC,QAAQ,GAAG,UAAU,CAAC;AACnC,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,MAAM,EAAE;AACV,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,OAAO,EAAE;AACX,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,UAAU;AACtB,IAAI,OAAO,EAAE,OAAO;AACpB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,QAAQ;AACpB,GAAG;AACH,CAAC;;;;"}