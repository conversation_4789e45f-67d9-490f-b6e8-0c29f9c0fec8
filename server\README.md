# Kinit-API 重构项目

## 项目架构

本项目按照指定的架构进行了完整重构，采用了清晰的分层架构设计：

```
server/
├── config/                 # 配置文件目录
│   ├── setting.py          # 主配置文件
│   ├── urls.py             # 路由配置文件
│   └── env/                # 环境配置目录
│       ├── dev.py          # 开发环境配置
│       └── pro.py          # 生产环境配置
├── core/                   # 核心模块（足够抽象，供各子应用调用）
│   ├── crud.py             # 数据库操作核心封装
│   ├── database.py         # 数据库核心配置
│   ├── data_types.py       # 自定义数据类型
│   ├── dependencies.py     # 常用依赖项
│   ├── docs.py             # 文档配置
│   ├── enum.py             # 枚举类型
│   ├── event.py            # 全局事件
│   ├── exception.py        # 异常处理
│   ├── logger.py           # 日志处理
│   ├── middleware.py       # 中间件配置
│   ├── mongo_manage.py     # MongoDB操作封装
│   └── validator.py        # 验证器
├── apps/                   # 应用目录
│   └── admin/             # Admin管理应用
│       ├── apis/           # API接口目录
│       │   └── auth.py     # 认证相关API
│       ├── params/         # 请求参数目录
│       │   └── auth/       # 认证相关参数
│       ├── schemas/        # 数据模型目录
│       │   └── auth/       # 认证相关模型
│       ├── services/       # 业务逻辑目录
│       │   └── auth.py     # 认证相关服务
│       └── depts.py        # 解耦层（与core模块对接）
├── utils/                  # 工具类目录
│   ├── cache.py            # 缓存工具
│   ├── response.py         # 响应工具
│   ├── status.py           # 状态码定义
│   └── tools.py            # 通用工具
├── models/                 # 统一模型目录
│   ├── base.py             # 基础模型
│   └── admin/             # Admin相关模型
│       └── auth/           # 认证相关模型
│           ├── user.py     # 用户模型
│           ├── role.py     # 角色模型
│           ├── menu.py     # 菜单模型
│           ├── dept.py     # 部门模型
│           └── m2m.py      # 多对多关系表
├── main.py                 # 项目入口文件
└── requirements.txt        # 依赖包文件
```

## 架构特点

### 1. 分层架构
- **config/**: 统一管理所有配置，支持多环境配置
- **core/**: 提供抽象的核心功能，供各应用复用
- **apps/**: 按业务模块组织应用代码
- **models/**: 统一管理所有数据模型
- **utils/**: 提供通用工具函数

### 2. 解耦设计
- **depts.py**: 作为应用与核心模块的解耦层，实现依赖注入和适配
- **services/**: 业务逻辑层，封装复杂的业务操作
- **apis/**: 接口层，只负责请求响应处理

### 3. 代码组织
- **按功能模块分组**: auth、system、record、help、resource
- **按层次分离**: apis、params、schemas、services
- **统一模型管理**: 所有模型集中在models目录

## 重构完成情况

✅ **已完成的重构任务**:
1. 创建新的目录结构
2. 迁移配置文件到config目录
3. 重构核心模块，使其更加抽象
4. 统一模型文件到models目录
5. 重构admin应用结构
6. 创建解耦层depts.py
7. 迁移工具类
8. 更新入口文件和路由配置

## 接口兼容性

✅ **接口调用保持不变**:
- 路由前缀保持原有结构: `/admin/auth/`, `/admin/system/` 等
- API响应格式保持一致
- 请求参数结构保持不变

## 最佳实践

1. **依赖注入**: 通过depts.py统一管理依赖
2. **分层解耦**: 各层职责明确，便于维护
3. **代码复用**: 核心功能抽象化，减少重复代码
4. **配置管理**: 支持多环境配置，便于部署
5. **错误处理**: 统一的异常处理机制

## 下一步工作

1. 完善其他模块的API、服务和模型
2. 添加单元测试
3. 完善文档和注释
4. 性能优化和监控
