{"version": 3, "file": "ro.mjs", "sources": ["../../../../../packages/locale/lang/ro.ts"], "sourcesContent": ["export default {\n  name: 'ro',\n  el: {\n    colorpicker: {\n      confirm: 'OK',\n      clear: '<PERSON><PERSON><PERSON>',\n    },\n    datepicker: {\n      now: 'Acum',\n      today: '<PERSON><PERSON>',\n      cancel: '<PERSON><PERSON><PERSON><PERSON>',\n      clear: '<PERSON><PERSON><PERSON>',\n      confirm: 'OK',\n      selectDate: 'Selectează data',\n      selectTime: 'Selectează ora',\n      startDate: 'Data de început',\n      startTime: 'Ora de început',\n      endDate: 'Data de sfârșit',\n      endTime: 'Ora de sfârșit',\n      prevYear: 'Anul trecut',\n      nextYear: 'Anul următor',\n      prevMonth: 'Luna trecută',\n      nextMonth: '<PERSON> următoare',\n      year: '',\n      month1: '<PERSON><PERSON>rie',\n      month2: 'Februarie',\n      month3: 'Martie',\n      month4: 'Aprilie',\n      month5: 'Mai',\n      month6: 'Iunie',\n      month7: 'Iulie',\n      month8: 'August',\n      month9: 'Septembrie',\n      month10: 'Octombrie',\n      month11: 'No<PERSON><PERSON>rie',\n      month12: 'Decembrie',\n      // week: 'week',\n      weeks: {\n        sun: 'Du',\n        mon: 'Lu',\n        tue: 'Ma',\n        wed: 'Mi',\n        thu: 'Jo',\n        fri: 'Vi',\n        sat: '<PERSON><PERSON>',\n      },\n      months: {\n        jan: 'Ian',\n        feb: 'Feb',\n        mar: 'Mar',\n        apr: 'Apr',\n        may: 'Mai',\n        jun: 'Iun',\n        jul: 'Iul',\n        aug: 'Aug',\n        sep: 'Sep',\n        oct: 'Oct',\n        nov: 'Noi',\n        dec: 'Dec',\n      },\n    },\n    select: {\n      loading: 'Se încarcă',\n      noMatch: 'Nu există date potrivite',\n      noData: 'Nu există date',\n      placeholder: 'Selectează',\n    },\n    cascader: {\n      noMatch: 'Nu există date potrivite',\n      loading: 'Se încarcă',\n      placeholder: 'Selectează',\n      noData: 'Nu există date',\n    },\n    pagination: {\n      goto: 'Go to',\n      pagesize: '/pagina',\n      total: 'Total {total}',\n      pageClassifier: '',\n      page: 'Page', // to be translated\n      prev: 'Go to previous page', // to be translated\n      next: 'Go to next page', // to be translated\n      currentPage: 'page {pager}', // to be translated\n      prevPages: 'Previous {pager} pages', // to be translated\n      nextPages: 'Next {pager} pages', // to be translated\n    },\n    messagebox: {\n      title: 'Mesaj',\n      confirm: 'OK',\n      cancel: 'Anulează',\n      error: 'Date introduse eronate',\n    },\n    upload: {\n      deleteTip: 'apăsați pe ștergeți pentru a elimina',\n      delete: 'șterge',\n      preview: 'previzualizare',\n      continue: 'continuă',\n    },\n    table: {\n      emptyText: 'Nu există date',\n      confirmFilter: 'Confirmă',\n      resetFilter: 'Resetează',\n      clearFilter: 'Tot',\n      sumText: 'Suma',\n    },\n    tree: {\n      emptyText: 'Nu există date',\n    },\n    transfer: {\n      noMatch: 'Nu există date potrivite',\n      noData: 'Nu există date',\n      titles: ['Lista 1', 'Lista 2'],\n      filterPlaceholder: 'Introduceți cuvântul cheie',\n      noCheckedFormat: '{total} elemente',\n      hasCheckedFormat: '{checked}/{total} verificate',\n    },\n    image: {\n      error: 'FAILED', // to be translated\n    },\n    pageHeader: {\n      title: 'Back', // to be translated\n    },\n    popconfirm: {\n      confirmButtonText: 'Yes', // to be translated\n      cancelButtonText: 'No', // to be translated\n    },\n    carousel: {\n      leftArrow: 'Carousel arrow left', // to be translated\n      rightArrow: 'Carousel arrow right', // to be translated\n      indicator: 'Carousel switch to index {index}', // to be translated\n    },\n  },\n}\n"], "names": [], "mappings": "AAAA,SAAe;AACf,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,EAAE,EAAE;AACN,IAAI,WAAW,EAAE;AACjB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,KAAK,EAAE,aAAa;AAC1B,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,GAAG,EAAE,MAAM;AACjB,MAAM,KAAK,EAAE,KAAK;AAClB,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,KAAK,EAAE,aAAa;AAC1B,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,UAAU,EAAE,qBAAqB;AACvC,MAAM,SAAS,EAAE,oBAAoB;AACrC,MAAM,SAAS,EAAE,mBAAmB;AACpC,MAAM,OAAO,EAAE,yBAAyB;AACxC,MAAM,OAAO,EAAE,wBAAwB;AACvC,MAAM,QAAQ,EAAE,aAAa;AAC7B,MAAM,QAAQ,EAAE,mBAAmB;AACnC,MAAM,SAAS,EAAE,mBAAmB;AACpC,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,IAAI,EAAE,EAAE;AACd,MAAM,MAAM,EAAE,UAAU;AACxB,MAAM,MAAM,EAAE,WAAW;AACzB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,SAAS;AACvB,MAAM,MAAM,EAAE,KAAK;AACnB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,OAAO;AACrB,MAAM,MAAM,EAAE,QAAQ;AACtB,MAAM,MAAM,EAAE,YAAY;AAC1B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,OAAO,EAAE,WAAW;AAC1B,MAAM,KAAK,EAAE;AACb,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,IAAI;AACjB,QAAQ,GAAG,EAAE,OAAO;AACpB,OAAO;AACP,MAAM,MAAM,EAAE;AACd,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,QAAQ,GAAG,EAAE,KAAK;AAClB,OAAO;AACP,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,OAAO,EAAE,+BAA+B;AAC9C,MAAM,MAAM,EAAE,qBAAqB;AACnC,MAAM,WAAW,EAAE,iBAAiB;AACpC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,+BAA+B;AAC9C,MAAM,OAAO,EAAE,oBAAoB;AACnC,MAAM,WAAW,EAAE,iBAAiB;AACpC,MAAM,MAAM,EAAE,qBAAqB;AACnC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,IAAI,EAAE,OAAO;AACnB,MAAM,QAAQ,EAAE,SAAS;AACzB,MAAM,KAAK,EAAE,eAAe;AAC5B,MAAM,cAAc,EAAE,EAAE;AACxB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,IAAI,EAAE,qBAAqB;AACjC,MAAM,IAAI,EAAE,iBAAiB;AAC7B,MAAM,WAAW,EAAE,cAAc;AACjC,MAAM,SAAS,EAAE,wBAAwB;AACzC,MAAM,SAAS,EAAE,oBAAoB;AACrC,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,OAAO;AACpB,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,MAAM,EAAE,eAAe;AAC7B,MAAM,KAAK,EAAE,wBAAwB;AACrC,KAAK;AACL,IAAI,MAAM,EAAE;AACZ,MAAM,SAAS,EAAE,0DAA0D;AAC3E,MAAM,MAAM,EAAE,aAAa;AAC3B,MAAM,OAAO,EAAE,gBAAgB;AAC/B,MAAM,QAAQ,EAAE,eAAe;AAC/B,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,aAAa,EAAE,eAAe;AACpC,MAAM,WAAW,EAAE,gBAAgB;AACnC,MAAM,WAAW,EAAE,KAAK;AACxB,MAAM,OAAO,EAAE,MAAM;AACrB,KAAK;AACL,IAAI,IAAI,EAAE;AACV,MAAM,SAAS,EAAE,qBAAqB;AACtC,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,OAAO,EAAE,+BAA+B;AAC9C,MAAM,MAAM,EAAE,qBAAqB;AACnC,MAAM,MAAM,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;AACpC,MAAM,iBAAiB,EAAE,oCAAoC;AAC7D,MAAM,eAAe,EAAE,kBAAkB;AACzC,MAAM,gBAAgB,EAAE,8BAA8B;AACtD,KAAK;AACL,IAAI,KAAK,EAAE;AACX,MAAM,KAAK,EAAE,QAAQ;AACrB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,KAAK,EAAE,MAAM;AACnB,KAAK;AACL,IAAI,UAAU,EAAE;AAChB,MAAM,iBAAiB,EAAE,KAAK;AAC9B,MAAM,gBAAgB,EAAE,IAAI;AAC5B,KAAK;AACL,IAAI,QAAQ,EAAE;AACd,MAAM,SAAS,EAAE,qBAAqB;AACtC,MAAM,UAAU,EAAE,sBAAsB;AACxC,MAAM,SAAS,EAAE,kCAAkC;AACnD,KAAK;AACL,GAAG;AACH,CAAC;;;;"}