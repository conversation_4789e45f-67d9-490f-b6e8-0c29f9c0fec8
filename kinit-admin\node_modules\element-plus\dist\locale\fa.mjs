/*! Element Plus v2.5.6 */

var fa = {
  name: "fa",
  el: {
    colorpicker: {
      confirm: "\u062A\u0627\u06CC\u06CC\u062F",
      clear: "\u062D\u0630\u0641"
    },
    datepicker: {
      now: "\u0627\u06A9\u0646\u0648\u0646",
      today: "\u0627\u0645\u0631\u0648\u0632",
      cancel: "\u0627\u0646\u0635\u0631\u0627\u0641",
      clear: "\u062D\u0630\u0641",
      confirm: "\u062A\u0627\u06CC\u06CC\u062F",
      selectDate: "\u0627\u0646\u062A\u062E\u0627\u0628 \u062A\u0627\u0631\u06CC\u062E",
      selectTime: "\u0627\u0646\u062A\u062E\u0627\u0628 \u0632\u0645\u0627\u0646",
      startDate: "\u062A\u0627\u0631\u06CC\u062E \u0634\u0631\u0648\u0639",
      startTime: "\u0632\u0645\u0627\u0646 \u0634\u0631\u0648\u0639",
      endDate: "\u062A\u0627\u0631\u06CC\u062E \u067E\u0627\u06CC\u0627\u0646",
      endTime: "\u0632\u0645\u0627\u0646 \u067E\u0627\u06CC\u0627\u0646",
      prevYear: "\u0633\u0627\u0644 \u0642\u0628\u0644",
      nextYear: "\u0633\u0627\u0644 \u0628\u0639\u062F",
      prevMonth: "\u0645\u0627\u0647 \u0642\u0628\u0644",
      nextMonth: "\u0645\u0627\u0647 \u0628\u0639\u062F",
      year: "\u0633\u0627\u0644",
      month1: "\u0698\u0627\u0646\u0648\u06CC\u0647",
      month2: "\u0641\u0648\u0631\u06CC\u0647",
      month3: "\u0645\u0627\u0631\u0633",
      month4: "\u0622\u0648\u0631\u06CC\u0644",
      month5: "\u0645\u0647",
      month6: "\u0698\u0648\u0626\u0646",
      month7: "\u062C\u0648\u0644\u0627\u06CC",
      month8: "\u0627\u0648\u062A",
      month9: "\u0633\u067E\u062A\u0627\u0645\u0628\u0631",
      month10: "\u0627\u06A9\u062A\u0628\u0631",
      month11: "\u0646\u0648\u0627\u0645\u0628\u0631",
      month12: "\u062F\u0633\u0627\u0645\u0628\u0631",
      weeks: {
        sun: "\u06CC\u06A9\u0634\u0646\u0628\u0647",
        mon: "\u062F\u0648\u0634\u0646\u0628\u0647",
        tue: "\u0633\u0647\u200B\u0634\u0646\u0628\u0647",
        wed: "\u0686\u0647\u0627\u0631\u0634\u0646\u0628\u0647",
        thu: "\u067E\u0646\u062C\u200B\u0634\u0646\u0628\u0647",
        fri: "\u062C\u0645\u0639\u0647",
        sat: "\u0634\u0646\u0628\u0647"
      },
      months: {
        jan: "\u0698\u0627\u0646\u0648\u06CC\u0647",
        feb: "\u0641\u0648\u0631\u06CC\u0647",
        mar: "\u0645\u0627\u0631\u0633",
        apr: "\u0622\u0648\u0631\u06CC\u0644",
        may: "\u0645\u0647",
        jun: "\u0698\u0648\u0626\u0646",
        jul: "\u062C\u0648\u0644\u0627\u06CC",
        aug: "\u0627\u0648\u062A",
        sep: "\u0633\u067E\u062A\u0627\u0645\u0628\u0631",
        oct: "\u0627\u06A9\u062A\u0628\u0631",
        nov: "\u0646\u0648\u0627\u0645\u0628\u0631",
        dec: "\u062F\u0633\u0627\u0645\u0628\u0631"
      }
    },
    select: {
      loading: "\u062F\u0631 \u062D\u0627\u0644 \u0628\u0627\u0631\u06AF\u0630\u0627\u0631\u06CC",
      noMatch: "\u0647\u06CC\u0686 \u0646\u062A\u06CC\u062C\u0647\u200C\u0627\u06CC \u067E\u06CC\u062F\u0627 \u0646\u0634\u062F",
      noData: "\u0627\u0637\u0644\u0627\u0639\u0627\u062A\u06CC \u0648\u062C\u0648\u062F \u0646\u062F\u0627\u0631\u062F",
      placeholder: "\u0627\u0646\u062A\u062E\u0627\u0628 \u06A9\u0646\u06CC\u062F"
    },
    cascader: {
      noMatch: "\u0647\u06CC\u0686 \u0646\u062A\u06CC\u062C\u0647\u200C\u0627\u06CC \u067E\u06CC\u062F\u0627 \u0646\u0634\u062F",
      loading: "\u062F\u0631 \u062D\u0627\u0644 \u0628\u0627\u0631\u06AF\u0630\u0627\u0631\u06CC",
      placeholder: "\u0627\u0646\u062A\u062E\u0627\u0628 \u06A9\u0646\u06CC\u062F",
      noData: "\u0627\u0637\u0644\u0627\u0639\u0627\u062A\u06CC \u0648\u062C\u0648\u062F \u0646\u062F\u0627\u0631\u062F"
    },
    pagination: {
      goto: "\u0628\u0631\u0648 \u0628\u0647",
      pagesize: "/\u0635\u0641\u062D\u0647",
      total: "\u0645\u062C\u0645\u0648\u0639 {total}",
      pageClassifier: "",
      page: "\u0635\u0641\u062D\u0647",
      prev: "\u0635\u0641\u062D\u0647 \u0642\u0628\u0644",
      next: "\u0635\u0641\u062D\u0647 \u0628\u0639\u062F",
      currentPage: "\u0635\u0641\u062D\u0647 {pager}",
      prevPages: "{pager} \u0635\u0641\u062D\u0647 \u0642\u0628\u0644",
      nextPages: "{pager} \u0635\u0641\u062D\u0647 \u0628\u0639\u062F"
    },
    messagebox: {
      title: "\u067E\u06CC\u0627\u0645",
      confirm: "\u062A\u0627\u06CC\u06CC\u062F",
      cancel: "\u0627\u0646\u0635\u0631\u0627\u0641",
      error: "\u0648\u0631\u0648\u062F\u06CC \u063A\u06CC\u0631 \u0645\u062C\u0627\u0632"
    },
    upload: {
      deleteTip: "\u0628\u0631\u0627\u06CC \u067E\u0627\u06A9 \u06A9\u0631\u062F\u0646 \u062D\u0630\u0641 \u0631\u0627 \u0641\u0634\u0627\u0631 \u062F\u0647\u06CC\u062F",
      delete: "\u062D\u0630\u0641",
      preview: "\u067E\u06CC\u0634\u200C\u0646\u0645\u0627\u06CC\u0634",
      continue: "\u0627\u062F\u0627\u0645\u0647"
    },
    table: {
      emptyText: "\u0627\u0637\u0644\u0627\u0639\u0627\u062A\u06CC \u0648\u062C\u0648\u062F \u0646\u062F\u0627\u0631\u062F",
      confirmFilter: "\u062A\u0627\u06CC\u06CC\u062F",
      resetFilter: "\u062D\u0630\u0641",
      clearFilter: "\u0647\u0645\u0647",
      sumText: "\u062C\u0645\u0639"
    },
    tour: {
      next: "\u0628\u0639\u062F\u06CC",
      previous: "\u0642\u0628\u0644\u06CC",
      finish: "\u067E\u0627\u06CC\u0627\u0646"
    },
    tree: {
      emptyText: "\u0627\u0637\u0644\u0627\u0639\u0627\u062A\u06CC \u0648\u062C\u0648\u062F \u0646\u062F\u0627\u0631\u062F"
    },
    transfer: {
      noMatch: "\u0647\u06CC\u0686 \u0646\u062A\u06CC\u062C\u0647\u200C\u0627\u06CC \u067E\u06CC\u062F\u0627 \u0646\u0634\u062F",
      noData: "\u0627\u0637\u0644\u0627\u0639\u0627\u062A\u06CC \u0648\u062C\u0648\u062F \u0646\u062F\u0627\u0631\u062F",
      titles: ["\u0644\u06CC\u0633\u062A 1", "\u0644\u06CC\u0633\u062A 2"],
      filterPlaceholder: "\u06A9\u0644\u06CC\u062F \u0648\u0627\u0698\u0647\u200C\u0647\u0627 \u0631\u0627 \u0648\u0627\u0631\u062F \u06A9\u0646",
      noCheckedFormat: "{total} \u0645\u0648\u0631\u062F",
      hasCheckedFormat: "{checked} \u0645\u0648\u0631\u062F \u0627\u0632 {total} \u0645\u0648\u0631\u062F \u0627\u0646\u062A\u062E\u0627\u0628 \u0634\u062F\u0647 \u0627\u0633\u062A"
    },
    image: {
      error: "\u062E\u0637\u0627 \u062F\u0631 \u0628\u0627\u0631\u06AF\u0630\u0627\u0631\u06CC \u062A\u0635\u0648\u06CC\u0631"
    },
    pageHeader: {
      title: "\u0628\u0627\u0632\u06AF\u0634\u062A"
    },
    popconfirm: {
      confirmButtonText: "\u0628\u0644\u0647",
      cancelButtonText: "\u062E\u06CC\u0631"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

export { fa as default };
