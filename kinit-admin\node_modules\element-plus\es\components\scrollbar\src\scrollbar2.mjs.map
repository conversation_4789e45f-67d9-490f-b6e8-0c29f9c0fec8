{"version": 3, "file": "scrollbar2.mjs", "sources": ["../../../../../../packages/components/scrollbar/src/scrollbar.vue"], "sourcesContent": ["<template>\n  <div ref=\"scrollbarRef\" :class=\"ns.b()\">\n    <div\n      ref=\"wrapRef\"\n      :class=\"wrapKls\"\n      :style=\"wrapStyle\"\n      @scroll=\"handleScroll\"\n    >\n      <component\n        :is=\"tag\"\n        :id=\"id\"\n        ref=\"resizeRef\"\n        :class=\"resizeKls\"\n        :style=\"viewStyle\"\n        :role=\"role\"\n        :aria-label=\"ariaLabel\"\n        :aria-orientation=\"ariaOrientation\"\n      >\n        <slot />\n      </component>\n    </div>\n    <template v-if=\"!native\">\n      <bar ref=\"barRef\" :always=\"always\" :min-size=\"minSize\" />\n    </template>\n  </div>\n</template>\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  nextTick,\n  onMounted,\n  onUpdated,\n  provide,\n  reactive,\n  ref,\n  watch,\n} from 'vue'\nimport { useEventListener, useResizeObserver } from '@vueuse/core'\nimport { addUnit, debugWarn, isNumber, isObject } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport Bar from './bar.vue'\nimport { scrollbarContextKey } from './constants'\nimport { scrollbarEmits, scrollbarProps } from './scrollbar'\nimport type { BarInstance } from './bar'\nimport type { CSSProperties, StyleValue } from 'vue'\n\nconst COMPONENT_NAME = 'ElScrollbar'\n\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\nconst props = defineProps(scrollbarProps)\nconst emit = defineEmits(scrollbarEmits)\n\nconst ns = useNamespace('scrollbar')\n\nlet stopResizeObserver: (() => void) | undefined = undefined\nlet stopResizeListener: (() => void) | undefined = undefined\n\nconst scrollbarRef = ref<HTMLDivElement>()\nconst wrapRef = ref<HTMLDivElement>()\nconst resizeRef = ref<HTMLElement>()\nconst barRef = ref<BarInstance>()\n\nconst wrapStyle = computed<StyleValue>(() => {\n  const style: CSSProperties = {}\n  if (props.height) style.height = addUnit(props.height)\n  if (props.maxHeight) style.maxHeight = addUnit(props.maxHeight)\n  return [props.wrapStyle, style]\n})\n\nconst wrapKls = computed(() => {\n  return [\n    props.wrapClass,\n    ns.e('wrap'),\n    { [ns.em('wrap', 'hidden-default')]: !props.native },\n  ]\n})\n\nconst resizeKls = computed(() => {\n  return [ns.e('view'), props.viewClass]\n})\n\nconst handleScroll = () => {\n  if (wrapRef.value) {\n    barRef.value?.handleScroll(wrapRef.value)\n\n    emit('scroll', {\n      scrollTop: wrapRef.value.scrollTop,\n      scrollLeft: wrapRef.value.scrollLeft,\n    })\n  }\n}\n\n// TODO: refactor method overrides, due to script setup dts\n// @ts-nocheck\nfunction scrollTo(xCord: number, yCord?: number): void\nfunction scrollTo(options: ScrollToOptions): void\nfunction scrollTo(arg1: unknown, arg2?: number) {\n  if (isObject(arg1)) {\n    wrapRef.value!.scrollTo(arg1)\n  } else if (isNumber(arg1) && isNumber(arg2)) {\n    wrapRef.value!.scrollTo(arg1, arg2)\n  }\n}\n\nconst setScrollTop = (value: number) => {\n  if (!isNumber(value)) {\n    debugWarn(COMPONENT_NAME, 'value must be a number')\n    return\n  }\n  wrapRef.value!.scrollTop = value\n}\n\nconst setScrollLeft = (value: number) => {\n  if (!isNumber(value)) {\n    debugWarn(COMPONENT_NAME, 'value must be a number')\n    return\n  }\n  wrapRef.value!.scrollLeft = value\n}\n\nconst update = () => {\n  barRef.value?.update()\n}\n\nwatch(\n  () => props.noresize,\n  (noresize) => {\n    if (noresize) {\n      stopResizeObserver?.()\n      stopResizeListener?.()\n    } else {\n      ;({ stop: stopResizeObserver } = useResizeObserver(resizeRef, update))\n      stopResizeListener = useEventListener('resize', update)\n    }\n  },\n  { immediate: true }\n)\n\nwatch(\n  () => [props.maxHeight, props.height],\n  () => {\n    if (!props.native)\n      nextTick(() => {\n        update()\n        if (wrapRef.value) {\n          barRef.value?.handleScroll(wrapRef.value)\n        }\n      })\n  }\n)\n\nprovide(\n  scrollbarContextKey,\n  reactive({\n    scrollbarElement: scrollbarRef,\n    wrapElement: wrapRef,\n  })\n)\n\nonMounted(() => {\n  if (!props.native)\n    nextTick(() => {\n      update()\n    })\n})\nonUpdated(() => update())\n\ndefineExpose({\n  /** @description scrollbar wrap ref */\n  wrapRef,\n  /** @description update scrollbar state manually */\n  update,\n  /** @description scrolls to a particular set of coordinates */\n  scrollTo,\n  /** @description set distance to scroll top */\n  setScrollTop,\n  /** @description set distance to scroll left */\n  setScrollLeft,\n  /** @description handle scroll event */\n  handleScroll,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;mCAgDc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;;AAKA,IAAM,MAAA,EAAA,GAAK,aAAa,WAAW,CAAA,CAAA;AAEnC,IAAA,IAAI,kBAA+C,GAAA,KAAA,CAAA,CAAA;AACnD,IAAA,IAAI,kBAA+C,GAAA,KAAA,CAAA,CAAA;AAEnD,IAAA,MAAM,eAAe,GAAoB,EAAA,CAAA;AACzC,IAAA,MAAM,UAAU,GAAoB,EAAA,CAAA;AACpC,IAAA,MAAM,YAAY,GAAiB,EAAA,CAAA;AACnC,IAAA,MAAM,SAAS,GAAiB,EAAA,CAAA;AAEhC,IAAM,MAAA,SAAA,GAAY,SAAqB,MAAM;AAC3C,MAAA,MAAM,QAAuB,EAAC,CAAA;AAC9B,MAAA,IAAI,KAAM,CAAA,MAAA;AAAQ,QAAM,KAAA,CAAA,MAAA,GAAS,OAAQ,CAAA,KAAA,CAAM,MAAM,CAAA,CAAA;AACrD,MAAA,IAAI,KAAM,CAAA,SAAA;AAAW,QAAM,KAAA,CAAA,SAAA,GAAY,OAAQ,CAAA,KAAA,CAAM,SAAS,CAAA,CAAA;AAC9D,MAAO,OAAA,CAAC,KAAM,CAAA,SAAA,EAAW,KAAK,CAAA,CAAA;AAAA,KAC/B,CAAA,CAAA;AAED,IAAM,MAAA,OAAA,GAAU,SAAS,MAAM;AAC7B,MAAO,OAAA;AAAA,QACL,KAAM,CAAA,SAAA;AAAA,QACN,EAAA,CAAG,EAAE,MAAM,CAAA;AAAA,QACX,EAAE,CAAC,EAAG,CAAA,EAAA,CAAG,QAAQ,gBAAgB,CAAA,GAAI,CAAC,KAAA,CAAM,MAAO,EAAA;AAAA,OACrD,CAAA;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,SAAA,GAAY,SAAS,MAAM;AAC/B,MAAA,OAAO,CAAC,EAAG,CAAA,CAAA,CAAE,MAAM,CAAA,EAAG,MAAM,SAAS,CAAA,CAAA;AAAA,KACtC,CAAA,CAAA;AAED,IAAA,MAAM,eAAe,MAAM;AACzB,MAAA,IAAI;AACF,MAAO,IAAA,OAAA,CAAA,KAAoB,EAAA;AAE3B,QAAA,CAAA,EAAA,GAAe,MAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AAAA,QACb,IAAA,CAAA,QAAW;AAAc,UACzB,SAAA,EAAA,aAA0B,CAAA,SAAA;AAAA,UAC3B,UAAA,EAAA,OAAA,CAAA,KAAA,CAAA,UAAA;AAAA,SACH,CAAA,CAAA;AAAA,OACF;AAMA,KAAA,CAAA;AACE,IAAI,SAAA,aAAgB,EAAA,IAAA,EAAA;AAClB,MAAQ,IAAA,QAAA,CAAA;AAAoB,qBACV,CAAA,QAAI,CAAK,IAAA,CAAA,CAAA;AAC3B,OAAQ,MAAA,IAAA,QAAgB,CAAA,IAAA,CAAA,IAAA,QAAU,CAAA,IAAA,CAAA,EAAA;AAAA,QACpC,OAAA,CAAA,KAAA,CAAA,QAAA,CAAA,IAAA,EAAA,IAAA,CAAA,CAAA;AAAA,OACF;AAEA,KAAM;AACJ,IAAI,MAAA,YAAU,GAAK,CAAG,KAAA,KAAA;AACpB,MAAA,IAAA,CAAA;AACA,QAAA,SAAA,CAAA,cAAA,EAAA,wBAAA,CAAA,CAAA;AAAA,QACF,OAAA;AACA,OAAA;AAA2B,MAC7B,OAAA,CAAA,KAAA,CAAA,SAAA,GAAA,KAAA,CAAA;AAEA,KAAM,CAAA;AACJ,IAAI,MAAA,aAAU,GAAQ,CAAA,KAAA,KAAA;AACpB,MAAA,IAAA,CAAA;AACA,QAAA,SAAA,CAAA,cAAA,EAAA,wBAAA,CAAA,CAAA;AAAA,QACF,OAAA;AACA,OAAA;AAA4B,MAC9B,OAAA,CAAA,KAAA,CAAA,UAAA,GAAA,KAAA,CAAA;AAEA,KAAA,CAAA;AACE,IAAA,MAAA,eAAqB;AAAA,MACvB,IAAA,EAAA,CAAA;AAEA,MAAA,CAAA,EAAA,GACQ,MAAA,CAAA,KACN,KAAA,IAAC,GAAa,KAAA,CAAA,GAAA,EAAA,CAAA,MAAA,EAAA,CAAA;AACZ,KAAA,CAAA;AACE,IAAqB,KAAA,CAAA,MAAA,KAAA,CAAA,QAAA,EAAA,CAAA,QAAA,KAAA;AACrB,MAAqB,IAAA,QAAA,EAAA;AAAA,QAChB,kBAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,kBAAA,EAAA,CAAA;AACL,QAAA,kBAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,kBAAA,EAAA,CAAA;AAAC,OAAA,MAAS;AACV,QAAqB,CAAA;AAAiC,QACxD,CAAA,EAAA,IAAA,EAAA,kBAAA,EAAA,GAAA,iBAAA,CAAA,SAAA,EAAA,MAAA,CAAA,EAAA;AAAA,QAEF,kBACF,GAAA,gBAAA,CAAA,QAAA,EAAA,MAAA,CAAA,CAAA;AAEA,OAAA;AAGI,KAAA,EAAA,EAAA,SAAW,EAAA,IAAA,EAAA,CAAA,CAAA;AACT,IAAA,KAAA,CAAA,MAAA,CAAS,KAAM,CAAA,SAAA,EAAA,KAAA,CAAA,MAAA,CAAA,EAAA,MAAA;AACb,MAAO,IAAA,CAAA,KAAA,CAAA,MAAA;AACP,QAAA,eAAmB;AACjB,UAAO,IAAA,EAAA,CAAA;AAAiC,UAC1C,MAAA,EAAA,CAAA;AAAA,UACD,IAAA,OAAA,CAAA,KAAA,EAAA;AAAA,YAEP,CAAA,EAAA,GAAA,MAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,YAAA,CAAA,OAAA,CAAA,KAAA,CAAA,CAAA;AAEA,WAAA;AAEW,SACW,CAAA,CAAA;AAAA,KAAA,CAClB,CAAa;AAAA,IACf,OACF,CAAA,mBAAA,EAAA,QAAA,CAAA;AAEA,MAAA,gBAAgB,EAAA,YAAA;AACd,MAAA,WAAW,EAAA,OAAA;AACT,KAAA,CAAA,CAAA,CAAA;AACE,IAAO,SAAA,CAAA,MAAA;AAAA,MAAA,IACR,CAAA,KAAA,CAAA,MAAA;AAAA,QACJ,QAAA,CAAA,MAAA;AACD,UAAU,MAAA,EAAA,CAAA;AAEV,SAAa,CAAA,CAAA;AAAA,KAEX,CAAA,CAAA;AAAA,IAEA,SAAA,CAAA,MAAA,MAAA,EAAA,CAAA,CAAA;AAAA,IAEA,MAAA,CAAA;AAAA,MAEA,OAAA;AAAA,MAEA,MAAA;AAAA,MAEA,QAAA;AAAA,MACD,YAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}