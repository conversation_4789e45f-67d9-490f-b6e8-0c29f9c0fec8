{"version": 3, "file": "token.mjs", "sources": ["../../../../../../packages/components/select/src/token.ts"], "sourcesContent": ["import type { ExtractPropTypes, InjectionKey } from 'vue'\nimport type { SelectProps } from './select'\n\ninterface SelectGroupContext {\n  disabled: boolean\n}\n\nexport interface SelectContext {\n  props: ExtractPropTypes<typeof SelectProps>\n  states: any\n  expanded: boolean\n  selectRef: HTMLElement\n  optionsArray: any[]\n  setSelected(): void\n  onOptionCreate(vm: SelectOptionProxy): void\n  onOptionDestroy(\n    key: number | string | Record<string, string>,\n    vm: SelectOptionProxy\n  ): void\n  handleOptionSelect(vm: SelectOptionProxy): void\n}\n\n// For individual build sharing injection key, we had to make `Symbol` to string\nexport const selectGroupKey: InjectionKey<SelectGroupContext> =\n  Symbol('ElSelectGroup')\n\nexport const selectKey: InjectionKey<SelectContext> = Symbol('ElSelect')\n\nexport interface SelectOptionProxy {\n  value: string | number | Record<string, string>\n  label: string | number\n  created: boolean\n  disabled: boolean\n  currentLabel: string\n  itemSelected: boolean\n  isDisabled: boolean\n  select: SelectContext\n  hoverItem: () => void\n  updateOption: (query: string) => void\n  visible: boolean\n  hover: boolean\n  selectOptionClick: () => void\n}\n\nexport type ISelectProps = ExtractPropTypes<typeof SelectProps>\n"], "names": [], "mappings": "AAAY,MAAC,cAAc,GAAG,MAAM,CAAC,eAAe,EAAE;AAC1C,MAAC,SAAS,GAAG,MAAM,CAAC,UAAU;;;;"}