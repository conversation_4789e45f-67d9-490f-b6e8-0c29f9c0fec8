{"version": 3, "file": "slider.mjs", "sources": ["../../../../../../packages/components/slider/src/slider.ts"], "sourcesContent": ["import { placements } from '@popperjs/core'\nimport {\n  buildProps,\n  definePropType,\n  isArray,\n  isNumber,\n} from '@element-plus/utils'\nimport {\n  CHANGE_EVENT,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { useSizeProp } from '@element-plus/hooks'\nimport type { Arrayable } from '@element-plus/utils'\nimport type { ExtractPropTypes } from 'vue'\nimport type { SliderMarkerProps } from './marker'\nimport type Slider from './slider.vue'\n\ntype SliderMarks = Record<number, string | SliderMarkerProps['mark']>\n\nexport interface SliderInitData {\n  firstValue: number\n  secondValue: number\n  oldValue?: Arrayable<number>\n  dragging: boolean\n  sliderSize: number\n}\n\nexport const sliderProps = buildProps({\n  /**\n   * @description binding value\n   */\n  modelValue: {\n    type: definePropType<Arrayable<number>>([Number, Array]),\n    default: 0,\n  },\n  id: {\n    type: String,\n    default: undefined,\n  },\n  /**\n   * @description minimum value\n   */\n  min: {\n    type: Number,\n    default: 0,\n  },\n  /**\n   * @description maximum value\n   */\n  max: {\n    type: Number,\n    default: 100,\n  },\n  /**\n   * @description step size\n   */\n  step: {\n    type: Number,\n    default: 1,\n  },\n  /**\n   * @description whether to display an input box, works when `range` is false\n   */\n  showInput: Boolean,\n  /**\n   * @description whether to display control buttons when `show-input` is true\n   */\n  showInputControls: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description size of the slider wrapper, will not work in vertical mode\n   */\n  size: useSizeProp,\n  /**\n   * @description size of the input box, when set `size`, the default is the value of `size`\n   */\n  inputSize: useSizeProp,\n  /**\n   * @description whether to display breakpoints\n   */\n  showStops: Boolean,\n  /**\n   * @description whether to display tooltip value\n   */\n  showTooltip: {\n    type: Boolean,\n    default: true,\n  },\n  /**\n   * @description format to display tooltip value\n   */\n  formatTooltip: {\n    type: definePropType<(val: number) => number | string>(Function),\n    default: undefined,\n  },\n  /**\n   * @description whether Slider is disabled\n   */\n  disabled: Boolean,\n  /**\n   * @description whether to select a range\n   */\n  range: Boolean,\n  /**\n   * @description vertical mode\n   */\n  vertical: Boolean,\n  /**\n   * @description slider height, required in vertical mode\n   */\n  height: String,\n  /**\n   * @description debounce delay when typing, in milliseconds, works when `show-input` is true\n   */\n  debounce: {\n    type: Number,\n    default: 300,\n  },\n  /**\n   * @description label for screen reader\n   */\n  label: {\n    type: String,\n    default: undefined,\n  },\n  /**\n   * @description when `range` is true, screen reader label for the start of the range\n   */\n  rangeStartLabel: {\n    type: String,\n    default: undefined,\n  },\n  /**\n   * @description when `range` is true, screen reader label for the end of the range\n   */\n  rangeEndLabel: {\n    type: String,\n    default: undefined,\n  },\n  /**\n   * @description format to display the `aria-valuenow` attribute for screen readers\n   */\n  formatValueText: {\n    type: definePropType<(val: number) => string>(Function),\n    default: undefined,\n  },\n  /**\n   * @description custom class name for the tooltip\n   */\n  tooltipClass: {\n    type: String,\n    default: undefined,\n  },\n  /**\n   * @description position of Tooltip\n   */\n  placement: {\n    type: String,\n    values: placements,\n    default: 'top',\n  },\n  /**\n   * @description marks, type of key must be `number` and must in closed interval `[min, max]`, each mark can custom style\n   */\n  marks: {\n    type: definePropType<SliderMarks>(Object),\n  },\n  /**\n   * @description whether to trigger form validation\n   */\n  validateEvent: {\n    type: Boolean,\n    default: true,\n  },\n} as const)\nexport type SliderProps = ExtractPropTypes<typeof sliderProps>\n\nconst isValidValue = (value: Arrayable<number>) =>\n  isNumber(value) || (isArray(value) && value.every(isNumber))\nexport const sliderEmits = {\n  [UPDATE_MODEL_EVENT]: isValidValue,\n  [INPUT_EVENT]: isValidValue,\n  [CHANGE_EVENT]: isValidValue,\n}\nexport type SliderEmits = typeof sliderEmits\n\nexport type SliderInstance = InstanceType<typeof Slider>\n"], "names": [], "mappings": ";;;;;;;;;;AAaY,MAAC,WAAW,GAAG,UAAU,CAAC;AACtC,EAAE,UAAU,EAAE;AACd,IAAI,IAAI,EAAE,cAAc,CAAC,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;AACzC,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,EAAE,EAAE;AACN,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,GAAG,EAAE;AACP,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,IAAI,EAAE;AACR,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,CAAC;AACd,GAAG;AACH,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,iBAAiB,EAAE;AACrB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,IAAI,EAAE,WAAW;AACnB,EAAE,SAAS,EAAE,WAAW;AACxB,EAAE,SAAS,EAAE,OAAO;AACpB,EAAE,WAAW,EAAE;AACf,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,KAAK,EAAE,OAAO;AAChB,EAAE,QAAQ,EAAE,OAAO;AACnB,EAAE,MAAM,EAAE,MAAM;AAChB,EAAE,QAAQ,EAAE;AACZ,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,GAAG;AAChB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,eAAe,EAAE;AACnB,IAAI,IAAI,EAAE,cAAc,CAAC,QAAQ,CAAC;AAClC,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,YAAY,EAAE;AAChB,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,OAAO,EAAE,KAAK,CAAC;AACnB,GAAG;AACH,EAAE,SAAS,EAAE;AACb,IAAI,IAAI,EAAE,MAAM;AAChB,IAAI,MAAM,EAAE,UAAU;AACtB,IAAI,OAAO,EAAE,KAAK;AAClB,GAAG;AACH,EAAE,KAAK,EAAE;AACT,IAAI,IAAI,EAAE,cAAc,CAAC,MAAM,CAAC;AAChC,GAAG;AACH,EAAE,aAAa,EAAE;AACjB,IAAI,IAAI,EAAE,OAAO;AACjB,IAAI,OAAO,EAAE,IAAI;AACjB,GAAG;AACH,CAAC,EAAE;AACH,MAAM,YAAY,GAAG,CAAC,KAAK,KAAK,QAAQ,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC;AAC/E,MAAC,WAAW,GAAG;AAC3B,EAAE,CAAC,kBAAkB,GAAG,YAAY;AACpC,EAAE,CAAC,WAAW,GAAG,YAAY;AAC7B,EAAE,CAAC,YAAY,GAAG,YAAY;AAC9B;;;;"}