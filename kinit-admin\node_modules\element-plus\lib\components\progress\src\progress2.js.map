{"version": 3, "file": "progress2.js", "sources": ["../../../../../../packages/components/progress/src/progress.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[\n      ns.b(),\n      ns.m(type),\n      ns.is(status),\n      {\n        [ns.m('without-text')]: !showText,\n        [ns.m('text-inside')]: textInside,\n      },\n    ]\"\n    role=\"progressbar\"\n    :aria-valuenow=\"percentage\"\n    aria-valuemin=\"0\"\n    aria-valuemax=\"100\"\n  >\n    <div v-if=\"type === 'line'\" :class=\"ns.b('bar')\">\n      <div\n        :class=\"ns.be('bar', 'outer')\"\n        :style=\"{ height: `${strokeWidth}px` }\"\n      >\n        <div\n          :class=\"[\n            ns.be('bar', 'inner'),\n            { [ns.bem('bar', 'inner', 'indeterminate')]: indeterminate },\n            { [ns.bem('bar', 'inner', 'striped')]: striped },\n            { [ns.bem('bar', 'inner', 'striped-flow')]: stripedFlow },\n          ]\"\n          :style=\"barStyle\"\n        >\n          <div\n            v-if=\"(showText || $slots.default) && textInside\"\n            :class=\"ns.be('bar', 'innerText')\"\n          >\n            <slot :percentage=\"percentage\">\n              <span>{{ content }}</span>\n            </slot>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div\n      v-else\n      :class=\"ns.b('circle')\"\n      :style=\"{ height: `${width}px`, width: `${width}px` }\"\n    >\n      <svg viewBox=\"0 0 100 100\">\n        <path\n          :class=\"ns.be('circle', 'track')\"\n          :d=\"trackPath\"\n          :stroke=\"`var(${ns.cssVarName('fill-color-light')}, #e5e9f2)`\"\n          :stroke-linecap=\"strokeLinecap\"\n          :stroke-width=\"relativeStrokeWidth\"\n          fill=\"none\"\n          :style=\"trailPathStyle\"\n        />\n        <path\n          :class=\"ns.be('circle', 'path')\"\n          :d=\"trackPath\"\n          :stroke=\"stroke\"\n          fill=\"none\"\n          :opacity=\"percentage ? 1 : 0\"\n          :stroke-linecap=\"strokeLinecap\"\n          :stroke-width=\"relativeStrokeWidth\"\n          :style=\"circlePathStyle\"\n        />\n      </svg>\n    </div>\n    <div\n      v-if=\"(showText || $slots.default) && !textInside\"\n      :class=\"ns.e('text')\"\n      :style=\"{ fontSize: `${progressTextSize}px` }\"\n    >\n      <slot :percentage=\"percentage\">\n        <span v-if=\"!status\">{{ content }}</span>\n        <el-icon v-else><component :is=\"statusIcon\" /></el-icon>\n      </slot>\n    </div>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed } from 'vue'\nimport { ElIcon } from '@element-plus/components/icon'\nimport {\n  Check,\n  CircleCheck,\n  CircleClose,\n  Close,\n  WarningFilled,\n} from '@element-plus/icons-vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { isFunction, isString } from '@element-plus/utils'\nimport { progressProps } from './progress'\nimport type { CSSProperties } from 'vue'\nimport type { ProgressColor } from './progress'\n\ndefineOptions({\n  name: 'ElProgress',\n})\n\nconst STATUS_COLOR_MAP: Record<string, string> = {\n  success: '#13ce66',\n  exception: '#ff4949',\n  warning: '#e6a23c',\n  default: '#20a0ff',\n}\n\nconst props = defineProps(progressProps)\n\nconst ns = useNamespace('progress')\n\nconst barStyle = computed<CSSProperties>(() => ({\n  width: `${props.percentage}%`,\n  animationDuration: `${props.duration}s`,\n  backgroundColor: getCurrentColor(props.percentage),\n}))\n\nconst relativeStrokeWidth = computed(() =>\n  ((props.strokeWidth / props.width) * 100).toFixed(1)\n)\n\nconst radius = computed(() => {\n  if (['circle', 'dashboard'].includes(props.type)) {\n    return Number.parseInt(\n      `${50 - Number.parseFloat(relativeStrokeWidth.value) / 2}`,\n      10\n    )\n  }\n  return 0\n})\n\nconst trackPath = computed(() => {\n  const r = radius.value\n  const isDashboard = props.type === 'dashboard'\n  return `\n          M 50 50\n          m 0 ${isDashboard ? '' : '-'}${r}\n          a ${r} ${r} 0 1 1 0 ${isDashboard ? '-' : ''}${r * 2}\n          a ${r} ${r} 0 1 1 0 ${isDashboard ? '' : '-'}${r * 2}\n          `\n})\n\nconst perimeter = computed(() => 2 * Math.PI * radius.value)\n\nconst rate = computed(() => (props.type === 'dashboard' ? 0.75 : 1))\n\nconst strokeDashoffset = computed(() => {\n  const offset = (-1 * perimeter.value * (1 - rate.value)) / 2\n  return `${offset}px`\n})\n\nconst trailPathStyle = computed<CSSProperties>(() => ({\n  strokeDasharray: `${perimeter.value * rate.value}px, ${perimeter.value}px`,\n  strokeDashoffset: strokeDashoffset.value,\n}))\n\nconst circlePathStyle = computed<CSSProperties>(() => ({\n  strokeDasharray: `${\n    perimeter.value * rate.value * (props.percentage / 100)\n  }px, ${perimeter.value}px`,\n  strokeDashoffset: strokeDashoffset.value,\n  transition:\n    'stroke-dasharray 0.6s ease 0s, stroke 0.6s ease, opacity ease 0.6s',\n}))\n\nconst stroke = computed(() => {\n  let ret: string\n  if (props.color) {\n    ret = getCurrentColor(props.percentage)\n  } else {\n    ret = STATUS_COLOR_MAP[props.status] || STATUS_COLOR_MAP.default\n  }\n  return ret\n})\n\nconst statusIcon = computed(() => {\n  if (props.status === 'warning') {\n    return WarningFilled\n  }\n  if (props.type === 'line') {\n    return props.status === 'success' ? CircleCheck : CircleClose\n  } else {\n    return props.status === 'success' ? Check : Close\n  }\n})\n\nconst progressTextSize = computed(() => {\n  return props.type === 'line'\n    ? 12 + props.strokeWidth * 0.4\n    : props.width * 0.111111 + 2\n})\n\nconst content = computed(() => props.format(props.percentage))\n\nfunction getColors(color: ProgressColor[]) {\n  const span = 100 / color.length\n  const seriesColors = color.map((seriesColor, index) => {\n    if (isString(seriesColor)) {\n      return {\n        color: seriesColor,\n        percentage: (index + 1) * span,\n      }\n    }\n    return seriesColor\n  })\n  return seriesColors.sort((a, b) => a.percentage - b.percentage)\n}\n\nconst getCurrentColor = (percentage: number) => {\n  const { color } = props\n  if (isFunction(color)) {\n    return color(percentage)\n  } else if (isString(color)) {\n    return color\n  } else {\n    const colors = getColors(color)\n    for (const color of colors) {\n      if (color.percentage > percentage) return color.color\n    }\n    return colors[colors.length - 1]?.color\n  }\n}\n</script>\n"], "names": ["useNamespace", "computed", "WarningFilled", "CircleCheck", "CircleClose", "Check", "Close", "isString", "isFunction"], "mappings": ";;;;;;;;;;;;;;;;;;;uCAiGc,CAAA;AAAA,EACZ,IAAM,EAAA,YAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAEA,IAAA,MAAM,gBAA2C,GAAA;AAAA,MAC/C,OAAS,EAAA,SAAA;AAAA,MACT,SAAW,EAAA,SAAA;AAAA,MACX,OAAS,EAAA,SAAA;AAAA,MACT,OAAS,EAAA,SAAA;AAAA,KACX,CAAA;AAIA,IAAM,MAAA,EAAA,GAAKA,mBAAa,UAAU,CAAA,CAAA;AAElC,IAAM,MAAA,QAAA,GAAWC,aAAwB,OAAO;AAAA,MAC9C,KAAA,EAAO,GAAG,KAAM,CAAA,UAAA,CAAA,CAAA,CAAA;AAAA,MAChB,iBAAA,EAAmB,GAAG,KAAM,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA,MAC5B,eAAA,EAAiB,eAAgB,CAAA,KAAA,CAAM,UAAU,CAAA;AAAA,KACjD,CAAA,CAAA,CAAA;AAEF,IAAM,MAAA,mBAAA,GAAsBA,YAAS,CAAA,MACjC,CAAM,KAAA,CAAA,WAAA,GAAc,MAAM,KAAS,GAAA,GAAA,EAAK,OAAQ,CAAA,CAAC,CACrD,CAAA,CAAA;AAEA,IAAM,MAAA,MAAA,GAASA,aAAS,MAAM;AAC5B,MAAA,IAAI,CAAC,QAAU,EAAA,WAAW,EAAE,QAAS,CAAA,KAAA,CAAM,IAAI,CAAG,EAAA;AAChD,QAAO,OAAA,MAAA,CAAO,QACZ,CAAA,CAAA,EAAG,EAAK,GAAA,MAAA,CAAO,WAAW,mBAAoB,CAAA,KAAK,CAAI,GAAA,CAAA,CAAA,CAAA,EACvD,EACF,CAAA,CAAA;AAAA,OACF;AACA,MAAO,OAAA,CAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,SAAA,GAAYA,aAAS,MAAM;AAC/B,MAAA,MAAM,IAAI,MAAO,CAAA,KAAA,CAAA;AACjB,MAAM,MAAA,WAAA,GAAc,MAAM,IAAS,KAAA,WAAA,CAAA;AACnC,MAAO,OAAA,CAAA;AAAA;AAAA,cAEO,EAAA,WAAA,GAAc,KAAK,GAAM,CAAA,EAAA,CAAA,CAAA;AAAA,YAAA,EAC3B,CAAK,CAAA,CAAA,EAAA,CAAA,CAAA,SAAA,EAAa,WAAc,GAAA,GAAA,GAAM,KAAK,CAAI,GAAA,CAAA,CAAA;AAAA,YAAA,EAC/C,CAAK,CAAA,CAAA,EAAA,CAAA,CAAA,SAAA,EAAa,WAAc,GAAA,EAAA,GAAK,MAAM,CAAI,GAAA,CAAA,CAAA;AAAA,UAAA,CAAA,CAAA;AAAA,KAE5D,CAAA,CAAA;AAED,IAAA,MAAM,YAAYA,YAAS,CAAA,MAAM,IAAI,IAAK,CAAA,EAAA,GAAK,OAAO,KAAK,CAAA,CAAA;AAE3D,IAAA,MAAM,OAAOA,YAAS,CAAA,MAAO,MAAM,IAAS,KAAA,WAAA,GAAc,OAAO,CAAE,CAAA,CAAA;AAEnE,IAAM,MAAA,gBAAA,GAAmBA,aAAS,MAAM;AACtC,MAAA,MAAM,SAAU,CAAK,CAAA,GAAA,SAAA,CAAU,KAAS,IAAA,CAAA,GAAI,KAAK,KAAU,CAAA,GAAA,CAAA,CAAA;AAC3D,MAAA,OAAO,CAAG,EAAA,MAAA,CAAA,EAAA,CAAA,CAAA;AAAA,KACX,CAAA,CAAA;AAED,IAAM,MAAA,cAAA,GAAiBA,aAAwB,OAAO;AAAA,MACpD,iBAAiB,CAAG,EAAA,SAAA,CAAU,KAAQ,GAAA,IAAA,CAAK,YAAY,SAAU,CAAA,KAAA,CAAA,EAAA,CAAA;AAAA,MACjE,kBAAkB,gBAAiB,CAAA,KAAA;AAAA,KACnC,CAAA,CAAA,CAAA;AAEF,IAAM,MAAA,eAAA,GAAkBA,aAAwB,OAAO;AAAA,MACrD,eAAA,EAAiB,GACf,SAAU,CAAA,KAAA,GAAQ,KAAK,KAAS,IAAA,KAAA,CAAM,UAAa,GAAA,GAAA,CAAA,CAAA,IAAA,EAC9C,SAAU,CAAA,KAAA,CAAA,EAAA,CAAA;AAAA,MACjB,kBAAkB,gBAAiB,CAAA,KAAA;AAAA,MACnC,UACE,EAAA,oEAAA;AAAA,KACF,CAAA,CAAA,CAAA;AAEF,IAAM,MAAA,MAAA,GAASA,aAAS,MAAM;AAC5B,MAAI,IAAA,GAAA,CAAA;AACJ,MAAA,IAAI,MAAM,KAAO,EAAA;AACf,QAAM,GAAA,GAAA,eAAA,CAAgB,MAAM,UAAU,CAAA,CAAA;AAAA,OACjC,MAAA;AACL,QAAM,GAAA,GAAA,gBAAA,CAAiB,KAAM,CAAA,MAAA,CAAA,IAAW,gBAAiB,CAAA,OAAA,CAAA;AAAA,OAC3D;AACA,MAAO,OAAA,GAAA,CAAA;AAAA,KACR,CAAA,CAAA;AAED,IAAM,MAAA,UAAA,GAAaA,aAAS,MAAM;AAChC,MAAI,IAAA,KAAA,CAAM,WAAW,SAAW,EAAA;AAC9B,QAAO,OAAAC,sBAAA,CAAA;AAAA,OACT;AACA,MAAI,IAAA,KAAA,CAAM,SAAS,MAAQ,EAAA;AACzB,QAAO,OAAA,KAAA,CAAM,MAAW,KAAA,SAAA,GAAYC,oBAAc,GAAAC,oBAAA,CAAA;AAAA,OAC7C,MAAA;AACL,QAAO,OAAA,KAAA,CAAM,MAAW,KAAA,SAAA,GAAYC,cAAQ,GAAAC,cAAA,CAAA;AAAA,OAC9C;AAAA,KACD,CAAA,CAAA;AAED,IAAM,MAAA,gBAAA,GAAmBL,aAAS,MAAM;AACtC,MAAO,OAAA,KAAA,CAAM,SAAS,MAClB,GAAA,EAAA,GAAK,MAAM,WAAc,GAAA,GAAA,GACzB,KAAM,CAAA,KAAA,GAAQ,QAAW,GAAA,CAAA,CAAA;AAAA,KAC9B,CAAA,CAAA;AAED,IAAA,MAAM,UAAUA,YAAS,CAAA,MAAM,MAAM,MAAO,CAAA,KAAA,CAAM,UAAU,CAAC,CAAA,CAAA;AAE7D,IAAA,SAAA,SAAA,CAAmB,KAAwB,EAAA;AACzC,MAAM,MAAA,IAAA,GAAO,MAAM,KAAM,CAAA,MAAA,CAAA;AACzB,MAAA,MAAM,YAAe,GAAA,KAAA,CAAM,GAAI,CAAA,CAAC,aAAa,KAAU,KAAA;AACrD,QAAI,IAAAM,eAAA,CAAS,WAAW,CAAG,EAAA;AACzB,UAAO,OAAA;AAAA,YACL,KAAO,EAAA,WAAA;AAAA,YACP,UAAA,EAAa,SAAQ,CAAK,IAAA,IAAA;AAAA,WAC5B,CAAA;AAAA,SACF;AACA,QAAO,OAAA,WAAA,CAAA;AAAA,OACR,CAAA,CAAA;AACD,MAAO,OAAA,YAAA,CAAa,KAAK,CAAC,CAAA,EAAG,MAAM,CAAE,CAAA,UAAA,GAAa,EAAE,UAAU,CAAA,CAAA;AAAA,KAChE;AAEA,IAAM,MAAA,eAAA,GAAkB,CAAC,UAAuB,KAAA;AAC9C,MAAA,IAAA,EAAM;AACN,MAAI,MAAA,EAAA,KAAA,EAAW,QAAQ,CAAA;AACrB,MAAA,IAAAC,kBAAa,KAAU,CAAA,EAAA;AAAA,QACzB,OAAA,KAAoB,CAAA,UAAK,CAAG,CAAA;AAC1B,OAAO,MAAA,IAAAD,eAAA,CAAA,KAAA,CAAA,EAAA;AAAA,QACF,OAAA,KAAA,CAAA;AACL,OAAM,MAAA;AACN,QAAA,MAAA,kBAA4B,CAAA,KAAA,CAAA,CAAA;AAC1B,QAAA,KAAA,YAAuB,IAAA,MAAA,EAAA;AAAY,UAAA,IAAA,MAAa,CAAA,UAAA,GAAA,UAAA;AAAA,YAClD,OAAA,MAAA,CAAA,KAAA,CAAA;AACA,SAAO;AAA2B,QACpC,OAAA,CAAA,EAAA,GAAA,MAAA,CAAA,MAAA,CAAA,MAAA,GAAA,CAAA,CAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA;AAAA,OACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}