{"version": 3, "file": "trigger2.js", "sources": ["../../../../../../packages/components/tooltip/src/trigger.vue"], "sourcesContent": ["<template>\n  <el-popper-trigger\n    :id=\"id\"\n    :virtual-ref=\"virtualRef\"\n    :open=\"open\"\n    :virtual-triggering=\"virtualTriggering\"\n    :class=\"ns.e('trigger')\"\n    @blur=\"onBlur\"\n    @click=\"onClick\"\n    @contextmenu=\"onContextMenu\"\n    @focus=\"onFocus\"\n    @mouseenter=\"onMouseenter\"\n    @mouseleave=\"onMouseleave\"\n    @keydown=\"onKeydown\"\n  >\n    <slot />\n  </el-popper-trigger>\n</template>\n<script lang=\"ts\" setup>\nimport { inject, ref, toRef, unref } from 'vue'\nimport { ElPopperTrigger } from '@element-plus/components/popper'\nimport { composeEventHandlers } from '@element-plus/utils'\nimport { useNamespace } from '@element-plus/hooks'\nimport { TOOLTIP_INJECTION_KEY } from './constants'\nimport { useTooltipTriggerProps } from './trigger'\nimport { whenTrigger } from './utils'\nimport type { OnlyChildExpose } from '@element-plus/components/slot'\n\ndefineOptions({\n  name: 'ElTooltipTrigger',\n})\n\nconst props = defineProps(useTooltipTriggerProps)\n\nconst ns = useNamespace('tooltip')\nconst { controlled, id, open, onOpen, onClose, onToggle } = inject(\n  TOOLTIP_INJECTION_KEY,\n  undefined\n)!\n\nconst triggerRef = ref<OnlyChildExpose | null>(null)\n\nconst stopWhenControlledOrDisabled = () => {\n  if (unref(controlled) || props.disabled) {\n    return true\n  }\n}\nconst trigger = toRef(props, 'trigger')\nconst onMouseenter = composeEventHandlers(\n  stopWhenControlledOrDisabled,\n  whenTrigger(trigger, 'hover', onOpen)\n)\nconst onMouseleave = composeEventHandlers(\n  stopWhenControlledOrDisabled,\n  whenTrigger(trigger, 'hover', onClose)\n)\nconst onClick = composeEventHandlers(\n  stopWhenControlledOrDisabled,\n  whenTrigger(trigger, 'click', (e) => {\n    // distinguish left click\n    if ((e as MouseEvent).button === 0) {\n      onToggle(e)\n    }\n  })\n)\n\nconst onFocus = composeEventHandlers(\n  stopWhenControlledOrDisabled,\n  whenTrigger(trigger, 'focus', onOpen)\n)\n\nconst onBlur = composeEventHandlers(\n  stopWhenControlledOrDisabled,\n  whenTrigger(trigger, 'focus', onClose)\n)\n\nconst onContextMenu = composeEventHandlers(\n  stopWhenControlledOrDisabled,\n  whenTrigger(trigger, 'contextmenu', (e: Event) => {\n    e.preventDefault()\n    onToggle(e)\n  })\n)\n\nconst onKeydown = composeEventHandlers(\n  stopWhenControlledOrDisabled,\n  (e: KeyboardEvent) => {\n    const { code } = e\n    if (props.triggerKeys.includes(code)) {\n      e.preventDefault()\n      onToggle(e)\n    }\n  }\n)\n\ndefineExpose({\n  /**\n   * @description trigger element\n   */\n  triggerRef,\n})\n</script>\n"], "names": ["useNamespace", "inject", "TOOLTIP_INJECTION_KEY", "ref", "unref", "toRef", "composeEventHandlers", "when<PERSON><PERSON>ger"], "mappings": ";;;;;;;;;;;;;;;;uCA4Bc,CAAA;AAAA,EACZ,IAAM,EAAA,kBAAA;AACR,CAAA,CAAA,CAAA;;;;;;AAIA,IAAM,MAAA,EAAA,GAAKA,mBAAa,SAAS,CAAA,CAAA;AACjC,IAAM,MAAA,EAAE,YAAY,EAAI,EAAA,IAAA,EAAM,QAAQ,OAAS,EAAA,QAAA,EAAA,GAAaC,UAC1D,CAAAC,+BAAA,EACA,KACF,CAAA,CAAA,CAAA;AAEA,IAAM,MAAA,UAAA,GAAaC,QAA4B,IAAI,CAAA,CAAA;AAEnD,IAAA,MAAM,+BAA+B,MAAM;AACzC,MAAA,IAAIC,SAAM,CAAA,UAAU,CAAK,IAAA,KAAA,CAAM,QAAU,EAAA;AACvC,QAAO,OAAA,IAAA,CAAA;AAAA,OACT;AAAA,KACF,CAAA;AACA,IAAM,MAAA,OAAA,GAAUC,SAAM,CAAA,KAAA,EAAO,SAAS,CAAA,CAAA;AACtC,IAAA,MAAM,eAAeC,0BACnB,CAAA,4BAAA,EACAC,kBAAY,OAAS,EAAA,OAAA,EAAS,MAAM,CACtC,CAAA,CAAA;AACA,IAAA,MAAM,eAAeD,0BACnB,CAAA,4BAAA,EACAC,kBAAY,OAAS,EAAA,OAAA,EAAS,OAAO,CACvC,CAAA,CAAA;AACA,IAAA,MAAM,UAAUD,0BACd,CAAA,4BAAA,EACAC,kBAAY,OAAS,EAAA,OAAA,EAAS,CAAC,CAAM,KAAA;AAEnC,MAAK,IAAA,CAAA,CAAiB,WAAW,CAAG,EAAA;AAClC,QAAA,QAAA,CAAS,CAAC,CAAA,CAAA;AAAA,OACZ;AAAA,KACD,CACH,CAAA,CAAA;AAEA,IAAA,MAAM,UAAUD,0BACd,CAAA,4BAAA,EACAC,kBAAY,OAAS,EAAA,OAAA,EAAS,MAAM,CACtC,CAAA,CAAA;AAEA,IAAA,MAAM,SAASD,0BACb,CAAA,4BAAA,EACAC,kBAAY,OAAS,EAAA,OAAA,EAAS,OAAO,CACvC,CAAA,CAAA;AAEA,IAAA,MAAM,gBAAgBD,0BACpB,CAAA,4BAAA,EACAC,kBAAY,OAAS,EAAA,aAAA,EAAe,CAAC,CAAa,KAAA;AAChD,MAAA,CAAA,CAAE,cAAe,EAAA,CAAA;AACjB,MAAA,QAAA,CAAS,CAAC,CAAA,CAAA;AAAA,KACX,CACH,CAAA,CAAA;AAEA,IAAA,MAAM,SAAY,GAAAD,0BAAA,CAChB,4BACA,EAAA,CAAC,CAAqB,KAAA;AACpB,MAAA,MAAM,EAAE,IAAS,EAAA,GAAA,CAAA,CAAA;AACjB,MAAA,IAAI,KAAM,CAAA,WAAA,CAAY,QAAS,CAAA,IAAI,CAAG,EAAA;AACpC,QAAA,CAAA,CAAE,cAAe,EAAA,CAAA;AACjB,QAAA,QAAA,CAAS,CAAC,CAAA,CAAA;AAAA,OACZ;AAAA,KAEJ,CAAA,CAAA;AAEA,IAAa,MAAA,CAAA;AAAA,MAIX,UAAA;AAAA,KACD,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}