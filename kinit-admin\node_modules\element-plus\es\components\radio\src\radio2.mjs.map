{"version": 3, "file": "radio2.mjs", "sources": ["../../../../../../packages/components/radio/src/radio.vue"], "sourcesContent": ["<template>\n  <label\n    :class=\"[\n      ns.b(),\n      ns.is('disabled', disabled),\n      ns.is('focus', focus),\n      ns.is('bordered', border),\n      ns.is('checked', modelValue === label),\n      ns.m(size),\n    ]\"\n  >\n    <span\n      :class=\"[\n        ns.e('input'),\n        ns.is('disabled', disabled),\n        ns.is('checked', modelValue === label),\n      ]\"\n    >\n      <input\n        ref=\"radioRef\"\n        v-model=\"modelValue\"\n        :class=\"ns.e('original')\"\n        :value=\"label\"\n        :name=\"name || radioGroup?.name\"\n        :disabled=\"disabled\"\n        type=\"radio\"\n        @focus=\"focus = true\"\n        @blur=\"focus = false\"\n        @change=\"handleChange\"\n        @click.stop\n      />\n      <span :class=\"ns.e('inner')\" />\n    </span>\n    <span :class=\"ns.e('label')\" @keydown.stop>\n      <slot>\n        {{ label }}\n      </slot>\n    </span>\n  </label>\n</template>\n\n<script lang=\"ts\" setup>\nimport { nextTick } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\nimport { radioEmits, radioProps } from './radio'\nimport { useRadio } from './use-radio'\n\ndefineOptions({\n  name: 'ElRadio',\n})\n\nconst props = defineProps(radioProps)\nconst emit = defineEmits(radioEmits)\n\nconst ns = useNamespace('radio')\nconst { radioRef, radioGroup, focus, size, disabled, modelValue } = useRadio(\n  props,\n  emit\n)\n\nfunction handleChange() {\n  nextTick(() => emit('change', modelValue.value))\n}\n</script>\n"], "names": [], "mappings": ";;;;;;;;mCA+Cc,CAAA;AAAA,EACZ,IAAM,EAAA,SAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAA,GAAK,aAAa,OAAO,CAAA,CAAA;AAC/B,IAAM,MAAA,EAAE,UAAU,UAAY,EAAA,KAAA,EAAO,MAAM,QAAU,EAAA,UAAA,EAAA,GAAe,QAClE,CAAA,KAAA,EACA,IACF,CAAA,CAAA;AAEA,IAAwB,SAAA,YAAA,GAAA;AACtB,MAAA,QAAA,CAAS,MAAM,IAAA,CAAK,QAAU,EAAA,UAAA,CAAW,KAAK,CAAC,CAAA,CAAA;AAAA,KACjD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}