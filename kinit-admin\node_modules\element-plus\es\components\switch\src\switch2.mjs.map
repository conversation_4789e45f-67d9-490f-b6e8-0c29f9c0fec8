{"version": 3, "file": "switch2.mjs", "sources": ["../../../../../../packages/components/switch/src/switch.vue"], "sourcesContent": ["<template>\n  <div :class=\"switchKls\" :style=\"styles\" @click.prevent=\"switchValue\">\n    <input\n      :id=\"inputId\"\n      ref=\"input\"\n      :class=\"ns.e('input')\"\n      type=\"checkbox\"\n      role=\"switch\"\n      :aria-checked=\"checked\"\n      :aria-disabled=\"switchDisabled\"\n      :aria-label=\"label\"\n      :name=\"name\"\n      :true-value=\"activeValue\"\n      :false-value=\"inactiveValue\"\n      :disabled=\"switchDisabled\"\n      :tabindex=\"tabindex\"\n      @change=\"handleChange\"\n      @keydown.enter=\"switchValue\"\n    />\n    <span\n      v-if=\"!inlinePrompt && (inactiveIcon || inactiveText)\"\n      :class=\"labelLeftKls\"\n    >\n      <el-icon v-if=\"inactiveIcon\">\n        <component :is=\"inactiveIcon\" />\n      </el-icon>\n      <span v-if=\"!inactiveIcon && inactiveText\" :aria-hidden=\"checked\">{{\n        inactiveText\n      }}</span>\n    </span>\n    <span ref=\"core\" :class=\"ns.e('core')\" :style=\"coreStyle\">\n      <div v-if=\"inlinePrompt\" :class=\"ns.e('inner')\">\n        <template v-if=\"activeIcon || inactiveIcon\">\n          <el-icon :class=\"ns.is('icon')\">\n            <component :is=\"checked ? activeIcon : inactiveIcon\" />\n          </el-icon>\n        </template>\n        <template v-else-if=\"activeText || inactiveText\">\n          <span :class=\"ns.is('text')\" :aria-hidden=\"!checked\">\n            {{ checked ? activeText : inactiveText }}\n          </span>\n        </template>\n      </div>\n      <div :class=\"ns.e('action')\">\n        <el-icon v-if=\"loading\" :class=\"ns.is('loading')\">\n          <loading />\n        </el-icon>\n        <slot v-else-if=\"checked\" name=\"active-action\">\n          <el-icon v-if=\"activeActionIcon\">\n            <component :is=\"activeActionIcon\" />\n          </el-icon>\n        </slot>\n        <slot v-else-if=\"!checked\" name=\"inactive-action\">\n          <el-icon v-if=\"inactiveActionIcon\">\n            <component :is=\"inactiveActionIcon\" />\n          </el-icon>\n        </slot>\n      </div>\n    </span>\n    <span\n      v-if=\"!inlinePrompt && (activeIcon || activeText)\"\n      :class=\"labelRightKls\"\n    >\n      <el-icon v-if=\"activeIcon\">\n        <component :is=\"activeIcon\" />\n      </el-icon>\n      <span v-if=\"!activeIcon && activeText\" :aria-hidden=\"!checked\">{{\n        activeText\n      }}</span>\n    </span>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport {\n  computed,\n  getCurrentInstance,\n  nextTick,\n  onMounted,\n  ref,\n  watch,\n} from 'vue'\nimport { isPromise } from '@vue/shared'\nimport { addUnit, debugWarn, isBoolean, throwError } from '@element-plus/utils'\nimport ElIcon from '@element-plus/components/icon'\nimport {\n  useFormDisabled,\n  useFormItem,\n  useFormItemInputId,\n  useFormSize,\n} from '@element-plus/components/form'\nimport { Loading } from '@element-plus/icons-vue'\nimport {\n  CHANGE_EVENT,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { useDeprecated, useNamespace } from '@element-plus/hooks'\nimport { switchEmits, switchProps } from './switch'\nimport type { CSSProperties } from 'vue'\n\nconst COMPONENT_NAME = 'ElSwitch'\ndefineOptions({\n  name: COMPONENT_NAME,\n})\n\nconst props = defineProps(switchProps)\nconst emit = defineEmits(switchEmits)\n\nconst vm = getCurrentInstance()!\nconst { formItem } = useFormItem()\nconst switchSize = useFormSize()\nconst ns = useNamespace('switch')\n\nconst useBatchDeprecated = (list: string[][]) => {\n  list.forEach((param) => {\n    useDeprecated(\n      {\n        from: param[0],\n        replacement: param[1],\n        scope: COMPONENT_NAME,\n        version: '2.3.0',\n        ref: 'https://element-plus.org/en-US/component/switch.html#attributes',\n        type: 'Attribute',\n      },\n      computed(() => !!vm.vnode.props?.[param[2]])\n    )\n  })\n}\nuseBatchDeprecated([\n  ['\"value\"', '\"model-value\" or \"v-model\"', 'value'],\n  ['\"active-color\"', 'CSS var `--el-switch-on-color`', 'activeColor'],\n  ['\"inactive-color\"', 'CSS var `--el-switch-off-color`', 'inactiveColor'],\n  ['\"border-color\"', 'CSS var `--el-switch-border-color`', 'borderColor'],\n])\n\nconst { inputId } = useFormItemInputId(props, {\n  formItemContext: formItem,\n})\n\nconst switchDisabled = useFormDisabled(computed(() => props.loading))\nconst isControlled = ref(props.modelValue !== false)\nconst input = ref<HTMLInputElement>()\nconst core = ref<HTMLSpanElement>()\n\nconst switchKls = computed(() => [\n  ns.b(),\n  ns.m(switchSize.value),\n  ns.is('disabled', switchDisabled.value),\n  ns.is('checked', checked.value),\n])\n\nconst labelLeftKls = computed(() => [\n  ns.e('label'),\n  ns.em('label', 'left'),\n  ns.is('active', !checked.value),\n])\n\nconst labelRightKls = computed(() => [\n  ns.e('label'),\n  ns.em('label', 'right'),\n  ns.is('active', checked.value),\n])\n\nconst coreStyle = computed<CSSProperties>(() => ({\n  width: addUnit(props.width),\n}))\n\nwatch(\n  () => props.modelValue,\n  () => {\n    isControlled.value = true\n  }\n)\n\nwatch(\n  () => props.value,\n  () => {\n    isControlled.value = false\n  }\n)\n\nconst actualValue = computed(() => {\n  return isControlled.value ? props.modelValue : props.value\n})\n\nconst checked = computed(() => actualValue.value === props.activeValue)\n\nif (![props.activeValue, props.inactiveValue].includes(actualValue.value)) {\n  emit(UPDATE_MODEL_EVENT, props.inactiveValue)\n  emit(CHANGE_EVENT, props.inactiveValue)\n  emit(INPUT_EVENT, props.inactiveValue)\n}\n\nwatch(checked, (val) => {\n  input.value!.checked = val\n\n  if (props.validateEvent) {\n    formItem?.validate?.('change').catch((err) => debugWarn(err))\n  }\n})\n\nconst handleChange = () => {\n  const val = checked.value ? props.inactiveValue : props.activeValue\n  emit(UPDATE_MODEL_EVENT, val)\n  emit(CHANGE_EVENT, val)\n  emit(INPUT_EVENT, val)\n  nextTick(() => {\n    input.value!.checked = checked.value\n  })\n}\n\nconst switchValue = () => {\n  if (switchDisabled.value) return\n\n  const { beforeChange } = props\n  if (!beforeChange) {\n    handleChange()\n    return\n  }\n\n  const shouldChange = beforeChange()\n\n  const isPromiseOrBool = [\n    isPromise(shouldChange),\n    isBoolean(shouldChange),\n  ].includes(true)\n  if (!isPromiseOrBool) {\n    throwError(\n      COMPONENT_NAME,\n      'beforeChange must return type `Promise<boolean>` or `boolean`'\n    )\n  }\n\n  if (isPromise(shouldChange)) {\n    shouldChange\n      .then((result) => {\n        if (result) {\n          handleChange()\n        }\n      })\n      .catch((e) => {\n        debugWarn(COMPONENT_NAME, `some error occurred: ${e}`)\n      })\n  } else if (shouldChange) {\n    handleChange()\n  }\n}\n\nconst styles = computed(() => {\n  return ns.cssVarBlock({\n    ...(props.activeColor ? { 'on-color': props.activeColor } : null),\n    ...(props.inactiveColor ? { 'off-color': props.inactiveColor } : null),\n    ...(props.borderColor ? { 'border-color': props.borderColor } : null),\n  })\n})\n\nconst focus = (): void => {\n  input.value?.focus?.()\n}\n\nonMounted(() => {\n  input.value!.checked = checked.value\n})\n\ndefineExpose({\n  /**\n   *  @description manual focus to the switch component\n   **/\n  focus,\n  /**\n   * @description whether Switch is checked\n   */\n  checked,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;mCAsGc,CAAA;AAAA,EACZ,IAAM,EAAA,cAAA;AACR;;;;;;;AAKA,IAAA,MAAM,KAAK,kBAAmB,EAAA,CAAA;AAC9B,IAAM,MAAA,EAAE,aAAa,WAAY,EAAA,CAAA;AACjC,IAAA,MAAM,aAAa,WAAY,EAAA,CAAA;AAC/B,IAAM,MAAA,EAAA,GAAK,aAAa,QAAQ,CAAA,CAAA;AAEhC,IAAM,MAAA,kBAAA,GAAqB,CAAC,IAAqB,KAAA;AAC/C,MAAK,IAAA,CAAA,OAAA,CAAQ,CAAC,KAAU,KAAA;AACtB,QACE,aAAA,CAAA;AAAA,UACE,MAAM,KAAM,CAAA,CAAA,CAAA;AAAA,UACZ,aAAa,KAAM,CAAA,CAAA,CAAA;AAAA,UACnB,KAAO,EAAA,cAAA;AAAA,UACP,OAAS,EAAA,OAAA;AAAA,UACT,GAAK,EAAA,iEAAA;AAAA,UACL,IAAM,EAAA,WAAA;AAAA,SACR,EACA,QAAS,CAAA,MAAM;AACjB,UACD,IAAA,EAAA,CAAA;AAAA,UACH,OAAA,CAAA,EAAA,CAAA,EAAA,GAAA,EAAA,CAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACA,SAAmB,CAAA,CAAA,CAAA;AAAA,OAChB,CAAW,CAAA;AAAqC,KACjD,CAAA;AAAkE,IAClE,kBAAqB,CAAA;AAAkD,MACvE,CAAC,SAAkB,EAAA,4BAAA,EAAA,OAAA,CAAA;AAAmD,MACvE,CAAA,gBAAA,EAAA,gCAAA,EAAA,aAAA,CAAA;AAED,MAAM,CAAA,kBAAc,EAAA,iCAA0B,EAAA,eAAA,CAAA;AAAA,MAC5C,CAAiB,gBAAA,EAAA,oCAAA,EAAA,aAAA,CAAA;AAAA,KAClB,CAAA,CAAA;AAED,IAAA,MAAM,gCAAiC,CAAA,KAAA,EAAA;AACvC,MAAA,eAAqB,EAAA,QAAU;AAC/B,KAAA,CAAA,CAAA;AACA,IAAA,MAAM,cAA4B,GAAA,eAAA,CAAA,QAAA,CAAA,MAAA,KAAA,CAAA,OAAA,CAAA,CAAA,CAAA;AAElC,IAAM,MAAA,YAAY,YAAe,CAAA,UAAA,KAAA,KAAA,CAAA,CAAA;AAAA,IAAA,MAC1B,KAAA,GAAA,GAAA,EAAA,CAAA;AAAA,IACL,MAAK,IAAA,GAAA,GAAA,EAAW,CAAK;AAAA,IAAA,MAClB,SAAe,GAAA,QAAA,CAAA,MAAA;AAAoB,MACtC,EAAG,CAAA,CAAA,EAAG;AAAwB,MAC/B,EAAA,CAAA,CAAA,CAAA,UAAA,CAAA,KAAA,CAAA;AAED,MAAM,EAAA,CAAA,EAAA,CAAA,UAAA,EAAA,cAA8B,CAAA,KAAA,CAAA;AAAA,MAClC,EAAA,CAAG,EAAE,CAAO,SAAA,EAAA,OAAA,CAAA,KAAA,CAAA;AAAA,KACZ,CAAA,CAAA;AAAqB,IAAA,MAClB,YAAa,WAAc,CAAA,MAAA;AAAA,MAC/B,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA;AAED,MAAM,EAAA,CAAA,EAAA,CAAA,OAAA,EAAA;AAA+B,MACnC,EAAA,CAAG,EAAE,CAAO,QAAA,EAAA,CAAA,OAAA,CAAA,KAAA,CAAA;AAAA,KACZ,CAAA,CAAA;AAAsB,IAAA,MACnB,aAAa,GAAA,QAAa,CAAA,MAAA;AAAA,MAC9B,EAAA,CAAA,CAAA,CAAA,OAAA,CAAA;AAED,MAAM,EAAA,CAAA,EAAA,CAAA,OAAA,EAAA;AAA2C,MAC/C,EAAA,CAAA,EAAA,CAAA,QAAe,EAAA,OAAW,CAAA,KAAA,CAAA;AAAA,KAC1B,CAAA,CAAA;AAEF,IACE,MAAA,SAAY,GAAA,QAAA,CAAA,OACN;AACJ,MAAA,KAAA,EAAA,OAAqB,CAAA,KAAA,CAAA,KAAA,CAAA;AAAA,KAEzB,CAAA,CAAA,CAAA;AAEA,IACE,KAAA,CAAA,MAAM,KAAM,CAAA,UACN,EAAA,MAAA;AACJ,MAAA,YAAA,CAAa,KAAQ,GAAA,IAAA,CAAA;AAAA,KAEzB,CAAA,CAAA;AAEA,IAAM,KAAA,CAAA,MAAA,KAAA,CAAA,aAA6B;AACjC,MAAA,YAAoB,CAAA,KAAA,GAAA,KAAA,CAAA;AAAiC,KACtD,CAAA,CAAA;AAED,IAAA,MAAM,WAAmB,GAAA,QAAA,CAAA,MAAkB;AAE3C,MAAI,OAAE,YAAmB,CAAA,KAAA,GAAA,gBAAqB,GAAA,KAAA,CAAS,KAAY,CAAA;AACjE,KAAK,CAAA,CAAA;AACL,IAAK,MAAA,OAAA,GAAA,eAAiC,WAAA,CAAA,KAAA,KAAA,KAAA,CAAA,WAAA,CAAA,CAAA;AACtC,IAAK,IAAA,CAAA,CAAA,KAAA,CAAA,kBAAgC,CAAA,aAAA,CAAA,CAAA,QAAA,CAAA,WAAA,CAAA,KAAA,CAAA,EAAA;AAAA,MACvC,IAAA,CAAA,kBAAA,EAAA,KAAA,CAAA,aAAA,CAAA,CAAA;AAEA,MAAM,IAAA,CAAA,YAAkB,EAAA,KAAA,CAAA,aAAA,CAAA,CAAA;AACtB,MAAA,IAAA,CAAA,WAAuB,EAAA,KAAA,CAAA,aAAA,CAAA,CAAA;AAEvB,KAAA;AACE,IAAU,KAAA,CAAA,OAAA,EAAA,CAAA,GAAA,KAAA;AAAkD,MAC9D,IAAA,EAAA,CAAA;AAAA,MACD,KAAA,CAAA,KAAA,CAAA,OAAA,GAAA,GAAA,CAAA;AAED,MAAA,IAAM,mBAAqB,EAAA;AACzB,QAAA,CAAA,EAAA,GAAY,QAAA,IAAQ,IAAQ,GAAA,KAAA,CAAA,oBAA4B,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACxD,OAAA;AACA,KAAA,CAAA,CAAA;AACA,IAAA,MAAA,eAAqB,MAAA;AACrB,MAAA,MAAA,GAAS,GAAM,OAAA,CAAA,KAAA,GAAA,KAAA,CAAA,aAAA,GAAA,KAAA,CAAA,WAAA,CAAA;AACb,MAAM,IAAA,CAAA,oBAAyB,GAAA,CAAA,CAAA;AAAA,MACjC,IAAC,CAAA,YAAA,EAAA,GAAA,CAAA,CAAA;AAAA,MACH,IAAA,CAAA,WAAA,EAAA,GAAA,CAAA,CAAA;AAEA,MAAA;AACE,QAAA,KAAmB,CAAA,KAAA,CAAA,OAAA,GAAA,OAAA,CAAA,KAAA,CAAA;AAAO,OAAA,CAAA,CAAA;AAE1B,KAAA,CAAA;AACA,IAAA,MAAI,WAAe,GAAA,MAAA;AACjB,MAAa,IAAA,cAAA,CAAA,KAAA;AACb,QAAA,OAAA;AAAA,MACF,MAAA,EAAA,YAAA,EAAA,GAAA,KAAA,CAAA;AAEA,MAAA,IAAA,CAAA;AAEA,QAAA,YAAwB,EAAA,CAAA;AAAA,QACtB;AAAsB,OAAA;AACA,MACxB,kBAAe,GAAA,YAAA,EAAA,CAAA;AACf,MAAA,MAAsB,eAAA,GAAA;AACpB,QAAA,SAAA,CAAA;AAGA,QACF,SAAA,CAAA,YAAA,CAAA;AAEA,OAAI,CAAA,QAAA,CAAA;AACF,MACG,IAAA,CAAA,eAAK,EAAY;AAChB,QAAA,UAAY,CAAA,cAAA,EAAA,+DAAA,CAAA,CAAA;AACV,OAAa;AAAA,MACf,IAAA,SAAA,CAAA,YAAA,CAAA,EAAA;AAAA,QACF,YACc,CAAA,IAAA,CAAA,CAAA,MAAA,KAAA;AACZ,UAAU,IAAA,MAAA,EAAA;AAA2C,YACtD,YAAA,EAAA,CAAA;AAAA;AAEH,SAAa,CAAA,CAAA,KAAA,CAAA,CAAA,CAAA,KAAA;AAAA,UACf,SAAA,CAAA,cAAA,EAAA,CAAA,qBAAA,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAAA,SACF,CAAA,CAAA;AAEA,OAAM,MAAA,IAAA,YAAwB,EAAA;AAC5B,QAAA,YAAsB,EAAA,CAAA;AAAA,OAAA;AACwC,KAAA,CAAA;AACK,IAAA,YACvD,GAAA,QAAA,CAAA,MAAgC;AAAsB,MAClE,OAAC,EAAA,CAAA,WAAA,CAAA;AAAA,QACF,GAAA,KAAA,CAAA,WAAA,GAAA,EAAA,UAAA,EAAA,KAAA,CAAA,WAAA,EAAA,GAAA,IAAA;AAED,QAAA,sBAA0B,GAAA,EAAA,WAAA,EAAA,KAAA,CAAA,aAAA,EAAA,GAAA,IAAA;AACxB,QAAA,GAAA,iBAAqB,GAAA,EAAA,cAAA,EAAA,KAAA,CAAA,WAAA,EAAA,GAAA,IAAA;AAAA,OACvB,CAAA,CAAA;AAEA,KAAA,CAAA,CAAA;AACE,IAAM,MAAA,KAAA,GAAO;AAAkB,MAChC,IAAA,EAAA,EAAA,EAAA,CAAA;AAED,MAAa,CAAA,EAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAAA,KAIX,CAAA;AAAA,IAIA,SAAA,CAAA,MAAA;AAAA,MACD,KAAA,CAAA,KAAA,CAAA,OAAA,GAAA,OAAA,CAAA,KAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}